[{"C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js": "4", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js": "6", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js": "7", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js": "8", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js": "9", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js": "10", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js": "11", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js": "14", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js": "15", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js": "16", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js": "17", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js": "18", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js": "19", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js": "20", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js": "21", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js": "22", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js": "23", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js": "24", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js": "25", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js": "26", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js": "27", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ModernMap.js": "28"}, {"size": 648, "mtime": 1750768565997, "results": "29", "hashOfConfig": "30"}, {"size": 2653, "mtime": 1750838920471, "results": "31", "hashOfConfig": "30"}, {"size": 375, "mtime": 1750768575776, "results": "32", "hashOfConfig": "30"}, {"size": 10842, "mtime": 1750775069651, "results": "33", "hashOfConfig": "30"}, {"size": 6703, "mtime": 1750768747649, "results": "34", "hashOfConfig": "30"}, {"size": 7644, "mtime": 1750838937946, "results": "35", "hashOfConfig": "30"}, {"size": 12336, "mtime": 1750775057314, "results": "36", "hashOfConfig": "30"}, {"size": 3466, "mtime": 1750768701081, "results": "37", "hashOfConfig": "30"}, {"size": 7868, "mtime": 1750774457559, "results": "38", "hashOfConfig": "30"}, {"size": 7686, "mtime": 1750774497155, "results": "39", "hashOfConfig": "30"}, {"size": 9384, "mtime": 1750774543945, "results": "40", "hashOfConfig": "30"}, {"size": 9809, "mtime": 1750839016634, "results": "41", "hashOfConfig": "30"}, {"size": 826, "mtime": 1750776245084, "results": "42", "hashOfConfig": "30"}, {"size": 1104, "mtime": 1750776230119, "results": "43", "hashOfConfig": "30"}, {"size": 5719, "mtime": 1750776029978, "results": "44", "hashOfConfig": "30"}, {"size": 5990, "mtime": 1750776119596, "results": "45", "hashOfConfig": "30"}, {"size": 15313, "mtime": 1750776195115, "results": "46", "hashOfConfig": "30"}, {"size": 19413, "mtime": 1750845671080, "results": "47", "hashOfConfig": "30"}, {"size": 8451, "mtime": 1750836275026, "results": "48", "hashOfConfig": "30"}, {"size": 35890, "mtime": 1751890589794, "results": "49", "hashOfConfig": "30"}, {"size": 41381, "mtime": 1750845915689, "results": "50", "hashOfConfig": "30"}, {"size": 49797, "mtime": 1750846153362, "results": "51", "hashOfConfig": "30"}, {"size": 15060, "mtime": 1751890963536, "results": "52", "hashOfConfig": "30"}, {"size": 22604, "mtime": 1750842751821, "results": "53", "hashOfConfig": "30"}, {"size": 13396, "mtime": 1750837589801, "results": "54", "hashOfConfig": "30"}, {"size": 8336, "mtime": 1751889112576, "results": "55", "hashOfConfig": "30"}, {"size": 7155, "mtime": 1751889071654, "results": "56", "hashOfConfig": "30"}, {"size": 12909, "mtime": 1751890340972, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "923mfg", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js", [], ["142"], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js", ["143", "144"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js", ["145"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js", ["146", "147", "148", "149", "150", "151", "152", "153"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js", ["154", "155"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js", ["156", "157"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js", ["158", "159", "160", "161"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ModernMap.js", ["162", "163"], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 34, "column": 6, "nodeType": "166", "endLine": 34, "endColumn": 16, "suggestions": "167", "suppressions": "168"}, {"ruleId": "169", "severity": 1, "message": "170", "line": 33, "column": 10, "nodeType": "171", "messageId": "172", "endLine": 33, "endColumn": 19}, {"ruleId": "169", "severity": 1, "message": "173", "line": 33, "column": 21, "nodeType": "171", "messageId": "172", "endLine": 33, "endColumn": 33}, {"ruleId": "169", "severity": 1, "message": "174", "line": 18, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 18, "endColumn": 13}, {"ruleId": "169", "severity": 1, "message": "175", "line": 15, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 15, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "176", "line": 17, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 17, "endColumn": 11}, {"ruleId": "169", "severity": 1, "message": "177", "line": 18, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 18, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "178", "line": 19, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 19, "endColumn": 13}, {"ruleId": "164", "severity": 1, "message": "179", "line": 108, "column": 6, "nodeType": "166", "endLine": 108, "endColumn": 12, "suggestions": "180"}, {"ruleId": "169", "severity": 1, "message": "181", "line": 273, "column": 9, "nodeType": "171", "messageId": "172", "endLine": 273, "endColumn": 20}, {"ruleId": "169", "severity": 1, "message": "182", "line": 293, "column": 9, "nodeType": "171", "messageId": "172", "endLine": 293, "endColumn": 21}, {"ruleId": "169", "severity": 1, "message": "183", "line": 311, "column": 9, "nodeType": "171", "messageId": "172", "endLine": 311, "endColumn": 19}, {"ruleId": "169", "severity": 1, "message": "184", "line": 454, "column": 7, "nodeType": "171", "messageId": "172", "endLine": 454, "endColumn": 36}, {"ruleId": "169", "severity": 1, "message": "185", "line": 465, "column": 7, "nodeType": "171", "messageId": "172", "endLine": 465, "endColumn": 33}, {"ruleId": "169", "severity": 1, "message": "186", "line": 5, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 5, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "174", "line": 6, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 6, "endColumn": 13}, {"ruleId": "169", "severity": 1, "message": "187", "line": 5, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 5, "endColumn": 16}, {"ruleId": "169", "severity": 1, "message": "186", "line": 9, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 9, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "188", "line": 14, "column": 10, "nodeType": "171", "messageId": "172", "endLine": 14, "endColumn": 16}, {"ruleId": "169", "severity": 1, "message": "189", "line": 14, "column": 18, "nodeType": "171", "messageId": "172", "endLine": 14, "endColumn": 27}, {"ruleId": "169", "severity": 1, "message": "190", "line": 11, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 11, "endColumn": 11}, {"ruleId": "169", "severity": 1, "message": "191", "line": 12, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 12, "endColumn": 11}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'resetTimer'. Either include it or remove the dependency array.", "ArrayExpression", ["192"], ["193"], "no-unused-vars", "'isLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setIsLoading' is assigned a value but never used.", "'FiActivity' is defined but never used.", "'FiEdit3' is defined but never used.", "'FiShare2' is defined but never used.", "'FiHeart' is defined but never used.", "'FiThumbsUp' is defined but never used.", "React Hook useEffect has missing dependencies: 'challenges.length' and 'friendships.length'. Either include them or remove the dependency array.", ["194"], "'addActivity' is assigned a value but never used.", "'likeActivity' is assigned a value but never used.", "'addComment' is assigned a value but never used.", "'calculateTotalSegmentDistance' is assigned a value but never used.", "'generateSegmentLeaderboard' is assigned a value but never used.", "'FiClock' is defined but never used.", "'FiChevronDown' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'FiMapPin' is defined but never used.", "'FiTarget' is defined but never used.", {"desc": "195", "fix": "196"}, {"kind": "197", "justification": "198"}, {"desc": "199", "fix": "200"}, "Update the dependencies array to be: [resetTimer, settings]", {"range": "201", "text": "202"}, "directive", "", "Update the dependencies array to be: [challenges.length, friendships.length, user]", {"range": "203", "text": "204"}, [1205, 1215], "[resetTimer, settings]", [3206, 3212], "[challenges.length, friendships.length, user]"]