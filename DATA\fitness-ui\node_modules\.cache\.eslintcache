[{"C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js": "4", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js": "6", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js": "7", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js": "8", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js": "9", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js": "10", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js": "11", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js": "14", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js": "15", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js": "16", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js": "17", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js": "18", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js": "19", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js": "20", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js": "21", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js": "22", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js": "23", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js": "24", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js": "25", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js": "26", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js": "27"}, {"size": 648, "mtime": 1750768565997, "results": "28", "hashOfConfig": "29"}, {"size": 2653, "mtime": 1750838920471, "results": "30", "hashOfConfig": "29"}, {"size": 375, "mtime": 1750768575776, "results": "31", "hashOfConfig": "29"}, {"size": 10842, "mtime": 1750775069651, "results": "32", "hashOfConfig": "29"}, {"size": 6703, "mtime": 1750768747649, "results": "33", "hashOfConfig": "29"}, {"size": 7644, "mtime": 1750838937946, "results": "34", "hashOfConfig": "29"}, {"size": 12336, "mtime": 1750775057314, "results": "35", "hashOfConfig": "29"}, {"size": 3466, "mtime": 1750768701081, "results": "36", "hashOfConfig": "29"}, {"size": 7868, "mtime": 1750774457559, "results": "37", "hashOfConfig": "29"}, {"size": 7686, "mtime": 1750774497155, "results": "38", "hashOfConfig": "29"}, {"size": 9384, "mtime": 1750774543945, "results": "39", "hashOfConfig": "29"}, {"size": 9809, "mtime": 1750839016634, "results": "40", "hashOfConfig": "29"}, {"size": 826, "mtime": 1750776245084, "results": "41", "hashOfConfig": "29"}, {"size": 1104, "mtime": 1750776230119, "results": "42", "hashOfConfig": "29"}, {"size": 5719, "mtime": 1750776029978, "results": "43", "hashOfConfig": "29"}, {"size": 5990, "mtime": 1750776119596, "results": "44", "hashOfConfig": "29"}, {"size": 15313, "mtime": 1750776195115, "results": "45", "hashOfConfig": "29"}, {"size": 19413, "mtime": 1750845671080, "results": "46", "hashOfConfig": "29"}, {"size": 8451, "mtime": 1750836275026, "results": "47", "hashOfConfig": "29"}, {"size": 41356, "mtime": 1751889643114, "results": "48", "hashOfConfig": "29"}, {"size": 41381, "mtime": 1750845915689, "results": "49", "hashOfConfig": "29"}, {"size": 49797, "mtime": 1750846153362, "results": "50", "hashOfConfig": "29"}, {"size": 16338, "mtime": 1751889137164, "results": "51", "hashOfConfig": "29"}, {"size": 22604, "mtime": 1750842751821, "results": "52", "hashOfConfig": "29"}, {"size": 13396, "mtime": 1750837589801, "results": "53", "hashOfConfig": "29"}, {"size": 8336, "mtime": 1751889112576, "results": "54", "hashOfConfig": "29"}, {"size": 7155, "mtime": 1751889071654, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "923mfg", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js", [], ["137"], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js", ["138", "139"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js", ["140", "141"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js", ["142", "143", "144", "145", "146", "147", "148", "149"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js", ["150", "151", "152"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js", ["153", "154"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js", ["155", "156", "157", "158"], [], {"ruleId": "159", "severity": 1, "message": "160", "line": 34, "column": 6, "nodeType": "161", "endLine": 34, "endColumn": 16, "suggestions": "162", "suppressions": "163"}, {"ruleId": "164", "severity": 1, "message": "165", "line": 33, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 33, "endColumn": 19}, {"ruleId": "164", "severity": 1, "message": "168", "line": 33, "column": 21, "nodeType": "166", "messageId": "167", "endLine": 33, "endColumn": 33}, {"ruleId": "164", "severity": 1, "message": "169", "line": 19, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 13}, {"ruleId": "159", "severity": 1, "message": "170", "line": 113, "column": 6, "nodeType": "161", "endLine": 113, "endColumn": 8, "suggestions": "171"}, {"ruleId": "164", "severity": 1, "message": "172", "line": 15, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 15, "endColumn": 10}, {"ruleId": "164", "severity": 1, "message": "173", "line": 17, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 17, "endColumn": 11}, {"ruleId": "164", "severity": 1, "message": "174", "line": 18, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 18, "endColumn": 10}, {"ruleId": "164", "severity": 1, "message": "175", "line": 19, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 13}, {"ruleId": "159", "severity": 1, "message": "176", "line": 108, "column": 6, "nodeType": "161", "endLine": 108, "endColumn": 12, "suggestions": "177"}, {"ruleId": "164", "severity": 1, "message": "178", "line": 273, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 273, "endColumn": 20}, {"ruleId": "164", "severity": 1, "message": "179", "line": 293, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 293, "endColumn": 21}, {"ruleId": "164", "severity": 1, "message": "180", "line": 311, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 311, "endColumn": 19}, {"ruleId": "181", "severity": 1, "message": "182", "line": 453, "column": 3, "nodeType": "183", "messageId": "184", "endLine": 481, "endColumn": 6}, {"ruleId": "185", "severity": 2, "message": "186", "line": 453, "column": 3, "nodeType": "166", "messageId": "187", "endLine": 453, "endColumn": 9}, {"ruleId": "181", "severity": 1, "message": "182", "line": 483, "column": 3, "nodeType": "188", "messageId": "184", "endLine": 483, "endColumn": 19}, {"ruleId": "164", "severity": 1, "message": "189", "line": 5, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 10}, {"ruleId": "164", "severity": 1, "message": "169", "line": 6, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 6, "endColumn": 13}, {"ruleId": "164", "severity": 1, "message": "190", "line": 5, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 16}, {"ruleId": "164", "severity": 1, "message": "189", "line": 9, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 9, "endColumn": 10}, {"ruleId": "164", "severity": 1, "message": "191", "line": 14, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 14, "endColumn": 16}, {"ruleId": "164", "severity": 1, "message": "192", "line": 14, "column": 18, "nodeType": "166", "messageId": "167", "endLine": 14, "endColumn": 27}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'resetTimer'. Either include it or remove the dependency array.", "ArrayExpression", ["193"], ["194"], "no-unused-vars", "'isLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setIsLoading' is assigned a value but never used.", "'FiActivity' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadRoutesAndPOIs'. Either include it or remove the dependency array.", ["195"], "'FiEdit3' is defined but never used.", "'FiShare2' is defined but never used.", "'FiHeart' is defined but never used.", "'FiThumbsUp' is defined but never used.", "React Hook useEffect has missing dependencies: 'challenges.length' and 'friendships.length'. Either include them or remove the dependency array.", ["196"], "'addActivity' is assigned a value but never used.", "'likeActivity' is assigned a value but never used.", "'addComment' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ExpressionStatement", "unreachableCode", "no-undef", "'routes' is not defined.", "undef", "ReturnStatement", "'FiClock' is defined but never used.", "'FiChevronDown' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", {"desc": "197", "fix": "198"}, {"kind": "199", "justification": "200"}, {"desc": "201", "fix": "202"}, {"desc": "203", "fix": "204"}, "Update the dependencies array to be: [resetTimer, settings]", {"range": "205", "text": "206"}, "directive", "", "Update the dependencies array to be: [loadRoutesAndPOIs]", {"range": "207", "text": "208"}, "Update the dependencies array to be: [challenges.length, friendships.length, user]", {"range": "209", "text": "210"}, [1205, 1215], "[resetTimer, settings]", [3778, 3780], "[loadRoutesAndPOIs]", [3206, 3212], "[challenges.length, friendships.length, user]"]