[{"C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js": "4", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js": "6", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js": "7", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js": "8", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js": "9", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js": "10", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js": "11", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js": "14", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js": "15", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js": "16", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js": "17", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js": "18", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js": "19", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js": "20", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js": "21", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js": "22", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js": "23", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js": "24", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js": "25", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js": "26", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js": "27", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ModernMap.js": "28", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\RoutesModern.js": "29", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ActivityFeed.js": "30", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\StravaHeader.js": "31", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\HeatmapLayer.js": "32"}, {"size": 648, "mtime": 1750768565997, "results": "33", "hashOfConfig": "34"}, {"size": 2869, "mtime": 1751898163669, "results": "35", "hashOfConfig": "34"}, {"size": 375, "mtime": 1750768575776, "results": "36", "hashOfConfig": "34"}, {"size": 10842, "mtime": 1750775069651, "results": "37", "hashOfConfig": "34"}, {"size": 6703, "mtime": 1750768747649, "results": "38", "hashOfConfig": "34"}, {"size": 7644, "mtime": 1750838937946, "results": "39", "hashOfConfig": "34"}, {"size": 12336, "mtime": 1750775057314, "results": "40", "hashOfConfig": "34"}, {"size": 3466, "mtime": 1750768701081, "results": "41", "hashOfConfig": "34"}, {"size": 7868, "mtime": 1750774457559, "results": "42", "hashOfConfig": "34"}, {"size": 7686, "mtime": 1750774497155, "results": "43", "hashOfConfig": "34"}, {"size": 9384, "mtime": 1750774543945, "results": "44", "hashOfConfig": "34"}, {"size": 9809, "mtime": 1750839016634, "results": "45", "hashOfConfig": "34"}, {"size": 826, "mtime": 1750776245084, "results": "46", "hashOfConfig": "34"}, {"size": 1104, "mtime": 1750776230119, "results": "47", "hashOfConfig": "34"}, {"size": 5719, "mtime": 1750776029978, "results": "48", "hashOfConfig": "34"}, {"size": 5990, "mtime": 1750776119596, "results": "49", "hashOfConfig": "34"}, {"size": 15313, "mtime": 1750776195115, "results": "50", "hashOfConfig": "34"}, {"size": 19413, "mtime": 1750845671080, "results": "51", "hashOfConfig": "34"}, {"size": 8451, "mtime": 1750836275026, "results": "52", "hashOfConfig": "34"}, {"size": 35890, "mtime": 1751890589794, "results": "53", "hashOfConfig": "34"}, {"size": 41381, "mtime": 1750845915689, "results": "54", "hashOfConfig": "34"}, {"size": 49797, "mtime": 1750846153362, "results": "55", "hashOfConfig": "34"}, {"size": 15060, "mtime": 1751890963536, "results": "56", "hashOfConfig": "34"}, {"size": 22604, "mtime": 1750842751821, "results": "57", "hashOfConfig": "34"}, {"size": 13396, "mtime": 1750837589801, "results": "58", "hashOfConfig": "34"}, {"size": 8336, "mtime": 1751889112576, "results": "59", "hashOfConfig": "34"}, {"size": 7155, "mtime": 1751889071654, "results": "60", "hashOfConfig": "34"}, {"size": 40570, "mtime": 1751896906882, "results": "61", "hashOfConfig": "34"}, {"size": 18821, "mtime": 1751899547544, "results": "62", "hashOfConfig": "34"}, {"size": 10117, "mtime": 1751897840236, "results": "63", "hashOfConfig": "34"}, {"size": 5380, "mtime": 1751897793393, "results": "64", "hashOfConfig": "34"}, {"size": 8775, "mtime": 1751897887984, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11zifcu", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js", [], ["162"], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js", ["163", "164"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js", ["165"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js", ["166", "167", "168", "169", "170", "171", "172", "173"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js", ["174", "175"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\SegmentPanel.js", ["176", "177"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\AdvancedFilters.js", ["178", "179", "180", "181"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ModernMap.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\RoutesModern.js", ["182", "183", "184", "185", "186", "187", "188", "189"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ActivityFeed.js", ["190", "191", "192", "193", "194", "195", "196"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\StravaHeader.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\HeatmapLayer.js", ["197"], [], {"ruleId": "198", "severity": 1, "message": "199", "line": 34, "column": 6, "nodeType": "200", "endLine": 34, "endColumn": 16, "suggestions": "201", "suppressions": "202"}, {"ruleId": "203", "severity": 1, "message": "204", "line": 33, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 33, "endColumn": 19}, {"ruleId": "203", "severity": 1, "message": "207", "line": 33, "column": 21, "nodeType": "205", "messageId": "206", "endLine": 33, "endColumn": 33}, {"ruleId": "203", "severity": 1, "message": "208", "line": 18, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 18, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "209", "line": 15, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 15, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "210", "line": 17, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 17, "endColumn": 11}, {"ruleId": "203", "severity": 1, "message": "211", "line": 18, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 18, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "212", "line": 19, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 19, "endColumn": 13}, {"ruleId": "198", "severity": 1, "message": "213", "line": 108, "column": 6, "nodeType": "200", "endLine": 108, "endColumn": 12, "suggestions": "214"}, {"ruleId": "203", "severity": 1, "message": "215", "line": 273, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 273, "endColumn": 20}, {"ruleId": "203", "severity": 1, "message": "216", "line": 293, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 293, "endColumn": 21}, {"ruleId": "203", "severity": 1, "message": "217", "line": 311, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 311, "endColumn": 19}, {"ruleId": "203", "severity": 1, "message": "218", "line": 454, "column": 7, "nodeType": "205", "messageId": "206", "endLine": 454, "endColumn": 36}, {"ruleId": "203", "severity": 1, "message": "219", "line": 465, "column": 7, "nodeType": "205", "messageId": "206", "endLine": 465, "endColumn": 33}, {"ruleId": "203", "severity": 1, "message": "220", "line": 5, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 5, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "208", "line": 6, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 6, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "221", "line": 5, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 5, "endColumn": 16}, {"ruleId": "203", "severity": 1, "message": "220", "line": 9, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 9, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "222", "line": 14, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 14, "endColumn": 16}, {"ruleId": "203", "severity": 1, "message": "223", "line": 14, "column": 18, "nodeType": "205", "messageId": "206", "endLine": 14, "endColumn": 27}, {"ruleId": "203", "severity": 1, "message": "224", "line": 1, "column": 38, "nodeType": "205", "messageId": "206", "endLine": 1, "endColumn": 49}, {"ruleId": "203", "severity": 1, "message": "225", "line": 3, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 3, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "226", "line": 11, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 11, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "227", "line": 13, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 13, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "210", "line": 14, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 14, "endColumn": 11}, {"ruleId": "203", "severity": 1, "message": "228", "line": 19, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 19, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "229", "line": 20, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 20, "endColumn": 9}, {"ruleId": "203", "severity": 1, "message": "230", "line": 43, "column": 22, "nodeType": "205", "messageId": "206", "endLine": 43, "endColumn": 35}, {"ruleId": "203", "severity": 1, "message": "220", "line": 8, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 8, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "231", "line": 9, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 9, "endColumn": 15}, {"ruleId": "203", "severity": 1, "message": "232", "line": 10, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 10, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "233", "line": 12, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 12, "endColumn": 11}, {"ruleId": "203", "severity": 1, "message": "234", "line": 18, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 18, "endColumn": 18}, {"ruleId": "203", "severity": 1, "message": "235", "line": 18, "column": 20, "nodeType": "205", "messageId": "206", "endLine": 18, "endColumn": 31}, {"ruleId": "203", "severity": 1, "message": "236", "line": 60, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 60, "endColumn": 19}, {"ruleId": "198", "severity": 1, "message": "237", "line": 164, "column": 6, "nodeType": "200", "endLine": 164, "endColumn": 86, "suggestions": "238"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'resetTimer'. Either include it or remove the dependency array.", "ArrayExpression", ["239"], ["240"], "no-unused-vars", "'isLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setIsLoading' is assigned a value but never used.", "'FiActivity' is defined but never used.", "'FiEdit3' is defined but never used.", "'FiShare2' is defined but never used.", "'FiHeart' is defined but never used.", "'FiThumbsUp' is defined but never used.", "React Hook useEffect has missing dependencies: 'challenges.length' and 'friendships.length'. Either include them or remove the dependency array.", ["241"], "'addActivity' is assigned a value but never used.", "'likeActivity' is assigned a value but never used.", "'addComment' is assigned a value but never used.", "'calculateTotalSegmentDistance' is assigned a value but never used.", "'generateSegmentLeaderboard' is assigned a value but never used.", "'FiClock' is defined but never used.", "'FiChevronDown' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'useCallback' is defined but never used.", "'FiMap' is defined but never used.", "'FiSettings' is defined but never used.", "'FiBookmark' is defined but never used.", "'FiEye' is defined but never used.", "'FiPlay' is defined but never used.", "'setActivities' is assigned a value but never used.", "'FiTrendingUp' is defined but never used.", "'FiZap' is defined but never used.", "'FiCamera' is defined but never used.", "'comments' is assigned a value but never used.", "'setComments' is assigned a value but never used.", "'formatPace' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultGradients', 'filterData', and 'heatLayer'. Either include them or remove the dependency array.", ["242"], {"desc": "243", "fix": "244"}, {"kind": "245", "justification": "246"}, {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, "Update the dependencies array to be: [resetTimer, settings]", {"range": "251", "text": "252"}, "directive", "", "Update the dependencies array to be: [challenges.length, friendships.length, user]", {"range": "253", "text": "254"}, "Update the dependencies array to be: [map, data, intensity, radius, blur, maxZoom, gradient, activityType, timeRange, heatLayer, filterData, defaultGradients]", {"range": "255", "text": "256"}, [1205, 1215], "[resetTimer, settings]", [3206, 3212], "[challenges.length, friendships.length, user]", [4252, 4332], "[map, data, intensity, radius, blur, maxZoom, gradient, activityType, timeRange, heatLayer, filterData, defaultGradients]"]