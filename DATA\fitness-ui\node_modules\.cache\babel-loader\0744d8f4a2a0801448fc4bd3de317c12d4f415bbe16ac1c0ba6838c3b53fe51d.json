{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\HeatmapLayer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport 'leaflet.heat';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HeatmapLayer = ({\n  data = [],\n  intensity = 0.6,\n  radius = 25,\n  blur = 15,\n  maxZoom = 18,\n  gradient = null,\n  activityType = 'all',\n  timeRange = 'all'\n}) => {\n  _s();\n  const map = useMap();\n  const [heatLayer, setHeatLayer] = useState(null);\n\n  // Gradients prédéfinis pour différents types d'activités\n  const defaultGradients = {\n    running: {\n      0.0: '#000080',\n      0.2: '#0000FF',\n      0.4: '#00FFFF',\n      0.6: '#00FF00',\n      0.8: '#FFFF00',\n      1.0: '#FF0000'\n    },\n    cycling: {\n      0.0: '#000040',\n      0.2: '#0040FF',\n      0.4: '#0080FF',\n      0.6: '#40C0FF',\n      0.8: '#80E0FF',\n      1.0: '#C0F0FF'\n    },\n    hiking: {\n      0.0: '#004000',\n      0.2: '#008000',\n      0.4: '#40A000',\n      0.6: '#80C000',\n      0.8: '#C0E000',\n      1.0: '#FFFF00'\n    },\n    all: {\n      0.0: '#330066',\n      0.2: '#660099',\n      0.4: '#9900CC',\n      0.6: '#CC00FF',\n      0.8: '#FF3399',\n      1.0: '#FF6666'\n    }\n  };\n\n  // Générer des données de heatmap fictives si aucune n'est fournie\n  const generateMockHeatmapData = (centerLat, centerLng, count = 1000) => {\n    const points = [];\n    for (let i = 0; i < count; i++) {\n      // Créer des clusters autour de points populaires\n      const clusterCenters = [{\n        lat: centerLat + 0.01,\n        lng: centerLng + 0.01,\n        weight: 0.8\n      }, {\n        lat: centerLat - 0.005,\n        lng: centerLng + 0.015,\n        weight: 0.6\n      }, {\n        lat: centerLat + 0.02,\n        lng: centerLng - 0.01,\n        weight: 0.7\n      }, {\n        lat: centerLat - 0.015,\n        lng: centerLng - 0.005,\n        weight: 0.5\n      }];\n      const cluster = clusterCenters[Math.floor(Math.random() * clusterCenters.length)];\n\n      // Ajouter du bruit gaussien autour du cluster\n      const lat = cluster.lat + (Math.random() - 0.5) * 0.02;\n      const lng = cluster.lng + (Math.random() - 0.5) * 0.02;\n      const intensity = cluster.weight * (0.3 + Math.random() * 0.7);\n      points.push([lat, lng, intensity]);\n    }\n    return points;\n  };\n\n  // Filtrer les données selon le type d'activité et la période\n  const filterData = rawData => {\n    if (!rawData || rawData.length === 0) {\n      // Générer des données fictives centrées sur la carte\n      const center = map.getCenter();\n      return generateMockHeatmapData(center.lat, center.lng);\n    }\n    let filtered = rawData;\n\n    // Filtrer par type d'activité\n    if (activityType !== 'all') {\n      filtered = filtered.filter(point => point.activityType === activityType || !point.activityType);\n    }\n\n    // Filtrer par période (implémentation basique)\n    if (timeRange !== 'all') {\n      const now = new Date();\n      const cutoffDate = new Date();\n      switch (timeRange) {\n        case 'week':\n          cutoffDate.setDate(now.getDate() - 7);\n          break;\n        case 'month':\n          cutoffDate.setMonth(now.getMonth() - 1);\n          break;\n        case 'year':\n          cutoffDate.setFullYear(now.getFullYear() - 1);\n          break;\n        default:\n          break;\n      }\n      filtered = filtered.filter(point => !point.timestamp || new Date(point.timestamp) >= cutoffDate);\n    }\n    return filtered.map(point => [point.lat || point[0], point.lng || point[1], point.intensity || point[2] || 0.5]);\n  };\n  useEffect(() => {\n    if (!map) return;\n\n    // Supprimer l'ancienne couche\n    if (heatLayer) {\n      map.removeLayer(heatLayer);\n    }\n\n    // Préparer les données\n    const filteredData = filterData(data);\n    if (filteredData.length === 0) return;\n\n    // Créer la nouvelle couche heatmap\n    const options = {\n      radius: radius,\n      blur: blur,\n      maxZoom: maxZoom,\n      gradient: gradient || defaultGradients[activityType] || defaultGradients.all\n    };\n    const newHeatLayer = L.heatLayer(filteredData, options);\n\n    // Ajouter à la carte\n    newHeatLayer.addTo(map);\n    setHeatLayer(newHeatLayer);\n\n    // Ajuster l'opacité\n    newHeatLayer.setOptions({\n      opacity: intensity\n    });\n    return () => {\n      if (newHeatLayer) {\n        map.removeLayer(newHeatLayer);\n      }\n    };\n  }, [map, data, intensity, radius, blur, maxZoom, gradient, activityType, timeRange]);\n\n  // Mettre à jour l'intensité\n  useEffect(() => {\n    if (heatLayer) {\n      heatLayer.setOptions({\n        opacity: intensity\n      });\n    }\n  }, [intensity, heatLayer]);\n  return null;\n};\n\n// Composant de contrôle pour la heatmap\n_s(HeatmapLayer, \"OcPycAYkKmYTa5I+76YJjwL7V5A=\", false, function () {\n  return [useMap];\n});\n_c = HeatmapLayer;\nexport const HeatmapControls = ({\n  intensity,\n  onIntensityChange,\n  radius,\n  onRadiusChange,\n  activityType,\n  onActivityTypeChange,\n  timeRange,\n  onTimeRangeChange,\n  visible,\n  onVisibilityChange\n}) => {\n  const activityTypes = [{\n    value: 'all',\n    label: 'Toutes activités',\n    color: '#9900CC'\n  }, {\n    value: 'running',\n    label: 'Course',\n    color: '#FF0000'\n  }, {\n    value: 'cycling',\n    label: 'Vélo',\n    color: '#0080FF'\n  }, {\n    value: 'hiking',\n    label: 'Randonnée',\n    color: '#00FF00'\n  }];\n  const timeRanges = [{\n    value: 'all',\n    label: 'Toute période'\n  }, {\n    value: 'week',\n    label: 'Cette semaine'\n  }, {\n    value: 'month',\n    label: 'Ce mois'\n  }, {\n    value: 'year',\n    label: 'Cette année'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-lg border border-gray-200 p-4 space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Carte de chaleur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: visible,\n          onChange: e => onVisibilityChange(e.target.checked),\n          className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Afficher\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), visible && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [\"Intensit\\xE9: \", Math.round(intensity * 100), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"1\",\n          step: \"0.1\",\n          value: intensity,\n          onChange: e => onIntensityChange(parseFloat(e.target.value)),\n          className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [\"Rayon: \", radius, \"px\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"10\",\n          max: \"50\",\n          step: \"5\",\n          value: radius,\n          onChange: e => onRadiusChange(parseInt(e.target.value)),\n          className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Type d'activit\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-2 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"activityType\",\n              value: type.value,\n              checked: activityType === type.value,\n              onChange: e => onActivityTypeChange(e.target.value),\n              className: \"text-blue-600 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 rounded-full\",\n              style: {\n                backgroundColor: type.color\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-700\",\n              children: type.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)]\n          }, type.value, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"P\\xE9riode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: timeRange,\n          onChange: e => onTimeRangeChange(e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: timeRanges.map(range => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: range.value,\n            children: range.label\n          }, range.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n};\n_c2 = HeatmapControls;\nexport default HeatmapLayer;\nvar _c, _c2;\n$RefreshReg$(_c, \"HeatmapLayer\");\n$RefreshReg$(_c2, \"HeatmapControls\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useMap", "L", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Heatmap<PERSON>ayer", "data", "intensity", "radius", "blur", "max<PERSON><PERSON>", "gradient", "activityType", "timeRange", "_s", "map", "heatLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultGradients", "running", "cycling", "hiking", "all", "generateMockHeatmapData", "centerLat", "centerLng", "count", "points", "i", "clusterCenters", "lat", "lng", "weight", "cluster", "Math", "floor", "random", "length", "push", "filterData", "rawData", "center", "getCenter", "filtered", "filter", "point", "now", "Date", "cutoffDate", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "timestamp", "<PERSON><PERSON><PERSON>er", "filteredData", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addTo", "setOptions", "opacity", "_c", "HeatmapControls", "onIntensityChange", "onRadiusChange", "onActivityTypeChange", "onTimeRangeChange", "visible", "onVisibilityChange", "activityTypes", "value", "label", "color", "timeRanges", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "e", "target", "round", "min", "max", "step", "parseFloat", "parseInt", "name", "style", "backgroundColor", "range", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/HeatmapLayer.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport 'leaflet.heat';\n\nconst HeatmapLayer = ({ \n  data = [], \n  intensity = 0.6, \n  radius = 25, \n  blur = 15,\n  maxZoom = 18,\n  gradient = null,\n  activityType = 'all',\n  timeRange = 'all'\n}) => {\n  const map = useMap();\n  const [heatLayer, setHeatLayer] = useState(null);\n\n  // Gradients prédéfinis pour différents types d'activités\n  const defaultGradients = {\n    running: {\n      0.0: '#000080',\n      0.2: '#0000FF',\n      0.4: '#00FFFF',\n      0.6: '#00FF00',\n      0.8: '#FFFF00',\n      1.0: '#FF0000'\n    },\n    cycling: {\n      0.0: '#000040',\n      0.2: '#0040FF',\n      0.4: '#0080FF',\n      0.6: '#40C0FF',\n      0.8: '#80E0FF',\n      1.0: '#C0F0FF'\n    },\n    hiking: {\n      0.0: '#004000',\n      0.2: '#008000',\n      0.4: '#40A000',\n      0.6: '#80C000',\n      0.8: '#C0E000',\n      1.0: '#FFFF00'\n    },\n    all: {\n      0.0: '#330066',\n      0.2: '#660099',\n      0.4: '#9900CC',\n      0.6: '#CC00FF',\n      0.8: '#FF3399',\n      1.0: '#FF6666'\n    }\n  };\n\n  // Générer des données de heatmap fictives si aucune n'est fournie\n  const generateMockHeatmapData = (centerLat, centerLng, count = 1000) => {\n    const points = [];\n    \n    for (let i = 0; i < count; i++) {\n      // Créer des clusters autour de points populaires\n      const clusterCenters = [\n        { lat: centerLat + 0.01, lng: centerLng + 0.01, weight: 0.8 },\n        { lat: centerLat - 0.005, lng: centerLng + 0.015, weight: 0.6 },\n        { lat: centerLat + 0.02, lng: centerLng - 0.01, weight: 0.7 },\n        { lat: centerLat - 0.015, lng: centerLng - 0.005, weight: 0.5 }\n      ];\n      \n      const cluster = clusterCenters[Math.floor(Math.random() * clusterCenters.length)];\n      \n      // Ajouter du bruit gaussien autour du cluster\n      const lat = cluster.lat + (Math.random() - 0.5) * 0.02;\n      const lng = cluster.lng + (Math.random() - 0.5) * 0.02;\n      const intensity = cluster.weight * (0.3 + Math.random() * 0.7);\n      \n      points.push([lat, lng, intensity]);\n    }\n    \n    return points;\n  };\n\n  // Filtrer les données selon le type d'activité et la période\n  const filterData = (rawData) => {\n    if (!rawData || rawData.length === 0) {\n      // Générer des données fictives centrées sur la carte\n      const center = map.getCenter();\n      return generateMockHeatmapData(center.lat, center.lng);\n    }\n\n    let filtered = rawData;\n\n    // Filtrer par type d'activité\n    if (activityType !== 'all') {\n      filtered = filtered.filter(point => \n        point.activityType === activityType || !point.activityType\n      );\n    }\n\n    // Filtrer par période (implémentation basique)\n    if (timeRange !== 'all') {\n      const now = new Date();\n      const cutoffDate = new Date();\n      \n      switch (timeRange) {\n        case 'week':\n          cutoffDate.setDate(now.getDate() - 7);\n          break;\n        case 'month':\n          cutoffDate.setMonth(now.getMonth() - 1);\n          break;\n        case 'year':\n          cutoffDate.setFullYear(now.getFullYear() - 1);\n          break;\n        default:\n          break;\n      }\n      \n      filtered = filtered.filter(point => \n        !point.timestamp || new Date(point.timestamp) >= cutoffDate\n      );\n    }\n\n    return filtered.map(point => [\n      point.lat || point[0],\n      point.lng || point[1],\n      point.intensity || point[2] || 0.5\n    ]);\n  };\n\n  useEffect(() => {\n    if (!map) return;\n\n    // Supprimer l'ancienne couche\n    if (heatLayer) {\n      map.removeLayer(heatLayer);\n    }\n\n    // Préparer les données\n    const filteredData = filterData(data);\n    \n    if (filteredData.length === 0) return;\n\n    // Créer la nouvelle couche heatmap\n    const options = {\n      radius: radius,\n      blur: blur,\n      maxZoom: maxZoom,\n      gradient: gradient || defaultGradients[activityType] || defaultGradients.all\n    };\n\n    const newHeatLayer = L.heatLayer(filteredData, options);\n    \n    // Ajouter à la carte\n    newHeatLayer.addTo(map);\n    setHeatLayer(newHeatLayer);\n\n    // Ajuster l'opacité\n    newHeatLayer.setOptions({ opacity: intensity });\n\n    return () => {\n      if (newHeatLayer) {\n        map.removeLayer(newHeatLayer);\n      }\n    };\n  }, [map, data, intensity, radius, blur, maxZoom, gradient, activityType, timeRange]);\n\n  // Mettre à jour l'intensité\n  useEffect(() => {\n    if (heatLayer) {\n      heatLayer.setOptions({ opacity: intensity });\n    }\n  }, [intensity, heatLayer]);\n\n  return null;\n};\n\n// Composant de contrôle pour la heatmap\nexport const HeatmapControls = ({ \n  intensity, \n  onIntensityChange,\n  radius,\n  onRadiusChange,\n  activityType,\n  onActivityTypeChange,\n  timeRange,\n  onTimeRangeChange,\n  visible,\n  onVisibilityChange\n}) => {\n  const activityTypes = [\n    { value: 'all', label: 'Toutes activités', color: '#9900CC' },\n    { value: 'running', label: 'Course', color: '#FF0000' },\n    { value: 'cycling', label: 'Vélo', color: '#0080FF' },\n    { value: 'hiking', label: 'Randonnée', color: '#00FF00' }\n  ];\n\n  const timeRanges = [\n    { value: 'all', label: 'Toute période' },\n    { value: 'week', label: 'Cette semaine' },\n    { value: 'month', label: 'Ce mois' },\n    { value: 'year', label: 'Cette année' }\n  ];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg border border-gray-200 p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Carte de chaleur</h3>\n        <label className=\"flex items-center space-x-2\">\n          <input\n            type=\"checkbox\"\n            checked={visible}\n            onChange={(e) => onVisibilityChange(e.target.checked)}\n            className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n          />\n          <span className=\"text-sm text-gray-600\">Afficher</span>\n        </label>\n      </div>\n\n      {visible && (\n        <>\n          {/* Intensité */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Intensité: {Math.round(intensity * 100)}%\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={intensity}\n              onChange={(e) => onIntensityChange(parseFloat(e.target.value))}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n            />\n          </div>\n\n          {/* Rayon */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Rayon: {radius}px\n            </label>\n            <input\n              type=\"range\"\n              min=\"10\"\n              max=\"50\"\n              step=\"5\"\n              value={radius}\n              onChange={(e) => onRadiusChange(parseInt(e.target.value))}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n            />\n          </div>\n\n          {/* Type d'activité */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Type d'activité\n            </label>\n            <div className=\"space-y-2\">\n              {activityTypes.map(type => (\n                <label key={type.value} className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"activityType\"\n                    value={type.value}\n                    checked={activityType === type.value}\n                    onChange={(e) => onActivityTypeChange(e.target.value)}\n                    className=\"text-blue-600 focus:ring-blue-500\"\n                  />\n                  <div \n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: type.color }}\n                  ></div>\n                  <span className=\"text-sm text-gray-700\">{type.label}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Période */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Période\n            </label>\n            <select\n              value={timeRange}\n              onChange={(e) => onTimeRangeChange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {timeRanges.map(range => (\n                <option key={range.value} value={range.value}>\n                  {range.label}\n                </option>\n              ))}\n            </select>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default HeatmapLayer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,YAAY,GAAGA,CAAC;EACpBC,IAAI,GAAG,EAAE;EACTC,SAAS,GAAG,GAAG;EACfC,MAAM,GAAG,EAAE;EACXC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,EAAE;EACZC,QAAQ,GAAG,IAAI;EACfC,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,GAAG,GAAGhB,MAAM,CAAC,CAAC;EACpB,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMoB,gBAAgB,GAAG;IACvBC,OAAO,EAAE;MACP,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE;IACP,CAAC;IACDC,OAAO,EAAE;MACP,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAE;MACN,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE;IACP,CAAC;IACDC,GAAG,EAAE;MACH,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE;IACP;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,GAAG,IAAI,KAAK;IACtE,MAAMC,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;MAC9B;MACA,MAAMC,cAAc,GAAG,CACrB;QAAEC,GAAG,EAAEN,SAAS,GAAG,IAAI;QAAEO,GAAG,EAAEN,SAAS,GAAG,IAAI;QAAEO,MAAM,EAAE;MAAI,CAAC,EAC7D;QAAEF,GAAG,EAAEN,SAAS,GAAG,KAAK;QAAEO,GAAG,EAAEN,SAAS,GAAG,KAAK;QAAEO,MAAM,EAAE;MAAI,CAAC,EAC/D;QAAEF,GAAG,EAAEN,SAAS,GAAG,IAAI;QAAEO,GAAG,EAAEN,SAAS,GAAG,IAAI;QAAEO,MAAM,EAAE;MAAI,CAAC,EAC7D;QAAEF,GAAG,EAAEN,SAAS,GAAG,KAAK;QAAEO,GAAG,EAAEN,SAAS,GAAG,KAAK;QAAEO,MAAM,EAAE;MAAI,CAAC,CAChE;MAED,MAAMC,OAAO,GAAGJ,cAAc,CAACK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGP,cAAc,CAACQ,MAAM,CAAC,CAAC;;MAEjF;MACA,MAAMP,GAAG,GAAGG,OAAO,CAACH,GAAG,GAAG,CAACI,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACtD,MAAML,GAAG,GAAGE,OAAO,CAACF,GAAG,GAAG,CAACG,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACtD,MAAM7B,SAAS,GAAG0B,OAAO,CAACD,MAAM,IAAI,GAAG,GAAGE,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;MAE9DT,MAAM,CAACW,IAAI,CAAC,CAACR,GAAG,EAAEC,GAAG,EAAExB,SAAS,CAAC,CAAC;IACpC;IAEA,OAAOoB,MAAM;EACf,CAAC;;EAED;EACA,MAAMY,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,MAAMI,MAAM,GAAG1B,GAAG,CAAC2B,SAAS,CAAC,CAAC;MAC9B,OAAOnB,uBAAuB,CAACkB,MAAM,CAACX,GAAG,EAAEW,MAAM,CAACV,GAAG,CAAC;IACxD;IAEA,IAAIY,QAAQ,GAAGH,OAAO;;IAEtB;IACA,IAAI5B,YAAY,KAAK,KAAK,EAAE;MAC1B+B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACjC,YAAY,KAAKA,YAAY,IAAI,CAACiC,KAAK,CAACjC,YAChD,CAAC;IACH;;IAEA;IACA,IAAIC,SAAS,KAAK,KAAK,EAAE;MACvB,MAAMiC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAAC,CAAC;MAE7B,QAAQlC,SAAS;QACf,KAAK,MAAM;UACTmC,UAAU,CAACC,OAAO,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;UACrC;QACF,KAAK,OAAO;UACVF,UAAU,CAACG,QAAQ,CAACL,GAAG,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UACvC;QACF,KAAK,MAAM;UACTJ,UAAU,CAACK,WAAW,CAACP,GAAG,CAACQ,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7C;QACF;UACE;MACJ;MAEAX,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAC9B,CAACA,KAAK,CAACU,SAAS,IAAI,IAAIR,IAAI,CAACF,KAAK,CAACU,SAAS,CAAC,IAAIP,UACnD,CAAC;IACH;IAEA,OAAOL,QAAQ,CAAC5B,GAAG,CAAC8B,KAAK,IAAI,CAC3BA,KAAK,CAACf,GAAG,IAAIe,KAAK,CAAC,CAAC,CAAC,EACrBA,KAAK,CAACd,GAAG,IAAIc,KAAK,CAAC,CAAC,CAAC,EACrBA,KAAK,CAACtC,SAAS,IAAIsC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CACnC,CAAC;EACJ,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,GAAG,EAAE;;IAEV;IACA,IAAIC,SAAS,EAAE;MACbD,GAAG,CAACyC,WAAW,CAACxC,SAAS,CAAC;IAC5B;;IAEA;IACA,MAAMyC,YAAY,GAAGlB,UAAU,CAACjC,IAAI,CAAC;IAErC,IAAImD,YAAY,CAACpB,MAAM,KAAK,CAAC,EAAE;;IAE/B;IACA,MAAMqB,OAAO,GAAG;MACdlD,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA,QAAQ,IAAIO,gBAAgB,CAACN,YAAY,CAAC,IAAIM,gBAAgB,CAACI;IAC3E,CAAC;IAED,MAAMqC,YAAY,GAAG3D,CAAC,CAACgB,SAAS,CAACyC,YAAY,EAAEC,OAAO,CAAC;;IAEvD;IACAC,YAAY,CAACC,KAAK,CAAC7C,GAAG,CAAC;IACvBE,YAAY,CAAC0C,YAAY,CAAC;;IAE1B;IACAA,YAAY,CAACE,UAAU,CAAC;MAAEC,OAAO,EAAEvD;IAAU,CAAC,CAAC;IAE/C,OAAO,MAAM;MACX,IAAIoD,YAAY,EAAE;QAChB5C,GAAG,CAACyC,WAAW,CAACG,YAAY,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAAC5C,GAAG,EAAET,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,CAAC,CAAC;;EAEpF;EACAhB,SAAS,CAAC,MAAM;IACd,IAAImB,SAAS,EAAE;MACbA,SAAS,CAAC6C,UAAU,CAAC;QAAEC,OAAO,EAAEvD;MAAU,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE,CAACA,SAAS,EAAES,SAAS,CAAC,CAAC;EAE1B,OAAO,IAAI;AACb,CAAC;;AAED;AAAAF,EAAA,CA1KMT,YAAY;EAAA,QAUJN,MAAM;AAAA;AAAAgE,EAAA,GAVd1D,YAAY;AA2KlB,OAAO,MAAM2D,eAAe,GAAGA,CAAC;EAC9BzD,SAAS;EACT0D,iBAAiB;EACjBzD,MAAM;EACN0D,cAAc;EACdtD,YAAY;EACZuD,oBAAoB;EACpBtD,SAAS;EACTuD,iBAAiB;EACjBC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC1D;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACxC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACzC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAc,CAAC,CACxC;EAED,oBACEvE,OAAA;IAAK0E,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjF3E,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3E,OAAA;QAAI0E,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzE/E,OAAA;QAAO0E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC5C3E,OAAA;UACEgF,IAAI,EAAC,UAAU;UACfC,OAAO,EAAEd,OAAQ;UACjBe,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAACe,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACtDP,SAAS,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACF/E,OAAA;UAAM0E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELZ,OAAO,iBACNnE,OAAA,CAAAE,SAAA;MAAAyE,QAAA,gBAEE3E,OAAA;QAAA2E,QAAA,gBACE3E,OAAA;UAAO0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,gBACnD,EAAC3C,IAAI,CAACqD,KAAK,CAAChF,SAAS,GAAG,GAAG,CAAC,EAAC,GAC1C;QAAA;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UACEgF,IAAI,EAAC,OAAO;UACZM,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,KAAK;UACVlB,KAAK,EAAEjE,SAAU;UACjB6E,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC0B,UAAU,CAACN,CAAC,CAACC,MAAM,CAACd,KAAK,CAAC,CAAE;UAC/DI,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/E,OAAA;QAAA2E,QAAA,gBACE3E,OAAA;UAAO0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,SACvD,EAACrE,MAAM,EAAC,IACjB;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UACEgF,IAAI,EAAC,OAAO;UACZM,GAAG,EAAC,IAAI;UACRC,GAAG,EAAC,IAAI;UACRC,IAAI,EAAC,GAAG;UACRlB,KAAK,EAAEhE,MAAO;UACd4E,QAAQ,EAAGC,CAAC,IAAKnB,cAAc,CAAC0B,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACd,KAAK,CAAC,CAAE;UAC1DI,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/E,OAAA;QAAA2E,QAAA,gBACE3E,OAAA;UAAO0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UAAK0E,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBN,aAAa,CAACxD,GAAG,CAACmE,IAAI,iBACrBhF,OAAA;YAAwB0E,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC5E3E,OAAA;cACEgF,IAAI,EAAC,OAAO;cACZW,IAAI,EAAC,cAAc;cACnBrB,KAAK,EAAEU,IAAI,CAACV,KAAM;cAClBW,OAAO,EAAEvE,YAAY,KAAKsE,IAAI,CAACV,KAAM;cACrCY,QAAQ,EAAGC,CAAC,IAAKlB,oBAAoB,CAACkB,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;cACtDI,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACF/E,OAAA;cACE0E,SAAS,EAAC,sBAAsB;cAChCkB,KAAK,EAAE;gBAAEC,eAAe,EAAEb,IAAI,CAACR;cAAM;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACP/E,OAAA;cAAM0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEK,IAAI,CAACT;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAbjDC,IAAI,CAACV,KAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcf,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAA2E,QAAA,gBACE3E,OAAA;UAAO0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UACEsE,KAAK,EAAE3D,SAAU;UACjBuE,QAAQ,EAAGC,CAAC,IAAKjB,iBAAiB,CAACiB,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;UACnDI,SAAS,EAAC,wGAAwG;UAAAC,QAAA,EAEjHF,UAAU,CAAC5D,GAAG,CAACiF,KAAK,iBACnB9F,OAAA;YAA0BsE,KAAK,EAAEwB,KAAK,CAACxB,KAAM;YAAAK,QAAA,EAC1CmB,KAAK,CAACvB;UAAK,GADDuB,KAAK,CAACxB,KAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACgB,GAAA,GA1HWjC,eAAe;AA4H5B,eAAe3D,YAAY;AAAC,IAAA0D,EAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAAnC,EAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}