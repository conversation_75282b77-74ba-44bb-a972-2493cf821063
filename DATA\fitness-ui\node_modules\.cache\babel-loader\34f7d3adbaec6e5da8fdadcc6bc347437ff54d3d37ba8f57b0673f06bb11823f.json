{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\RoutesModern.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { FiMap, FiActivity, FiTrendingUp, FiUsers, FiSearch, FiFilter, FiGrid, FiList, FiSettings, FiHeart, FiBookmark, FiShare2, FiMapPin, FiClock, FiTarget, FiZap, FiEye, FiPlay, FiStar } from 'react-icons/fi';\nimport ModernMap from '../components/ModernMap';\nimport StravaHeader from '../components/StravaHeader';\nimport ActivityFeed from '../components/ActivityFeed';\nimport HeatmapLayer, { HeatmapControls } from '../components/HeatmapLayer';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\nimport { generatePopularRoutes, generatePOIs, generateSegments, DEFAULT_POSITION } from '../utils/mapUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoutesModern = () => {\n  _s();\n  // États principaux\n  const [activeView, setActiveView] = useState('explore');\n  const [viewMode, setViewMode] = useState('grid');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n\n  // États pour les données\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [activities, setActivities] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n\n  // États pour les filtres\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n\n  // États pour la heatmap\n  const [showHeatmap, setShowHeatmap] = useState(false);\n  const [heatmapIntensity, setHeatmapIntensity] = useState(0.6);\n  const [heatmapRadius, setHeatmapRadius] = useState(25);\n  const [heatmapActivityType, setHeatmapActivityType] = useState('all');\n  const [heatmapTimeRange, setHeatmapTimeRange] = useState('all');\n\n  // Initialisation des données\n  useEffect(() => {\n    const initializeData = () => {\n      const generatedRoutes = generatePopularRoutes(userPosition.lat, userPosition.lng, 20);\n      const generatedPOIs = generatePOIs(userPosition.lat, userPosition.lng, 15);\n      const generatedSegments = generateSegments(generatedRoutes, 10);\n      setRoutes(generatedRoutes);\n      setPois(generatedPOIs);\n      setSegments(generatedSegments);\n    };\n    initializeData();\n  }, [userPosition]);\n\n  // Géolocalisation\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        setUserPosition({\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        });\n      }, error => {\n        console.log('Erreur de géolocalisation:', error);\n      });\n    }\n  }, []);\n\n  // Filtrage des routes\n  const filteredRoutes = routes.filter(route => {\n    if (searchTerm && !route.name.toLowerCase().includes(searchTerm.toLowerCase())) {\n      return false;\n    }\n    if (filters.sport !== 'all' && route.type !== filters.sport) {\n      return false;\n    }\n    if (filters.difficulty !== 'all' && route.difficulty !== filters.difficulty) {\n      return false;\n    }\n    if (filters.distance !== 'all') {\n      const [min, max] = filters.distance.split('-').map(Number);\n      if (max && (route.distance < min || route.distance > max)) {\n        return false;\n      }\n      if (!max && route.distance < min) {\n        return false;\n      }\n    }\n    return true;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setActiveView('explore');\n  };\n  const handleSegmentSelect = segment => {\n    setSelectedSegment(segment);\n  };\n  const handleActivitySelect = activity => {\n    // Logique pour afficher l'activité sur la carte\n    setActiveView('explore');\n  };\n  const renderExploreView = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col md:flex-row h-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full md:w-80 lg:w-96 xl:w-1/4 bg-white border-b md:border-b-0 md:border-r border-gray-200 flex flex-col h-auto md:h-full overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 md:p-4 border-b border-gray-100 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Rechercher des parcours...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col xs:flex-row items-stretch xs:items-center justify-between gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `btn-interactive flex items-center justify-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm min-h-[40px] ${showFilters ? 'bg-orange-50 text-orange-600 focus:ring-orange-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filtres\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowHeatmap(!showHeatmap),\n              className: `btn-interactive flex items-center justify-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm min-h-[40px] ${showHeatmap ? 'bg-blue-50 text-blue-600 focus:ring-blue-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Heatmap\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex bg-gray-100 rounded-lg p-1 self-start xs:self-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('grid'),\n              className: `btn-interactive p-2.5 rounded transition-colors focus:ring-gray-500 min-h-[40px] min-w-[40px] flex items-center justify-center ${viewMode === 'grid' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'}`,\n              title: \"Vue grille\",\n              children: /*#__PURE__*/_jsxDEV(FiGrid, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('list'),\n              className: `btn-interactive p-2.5 rounded transition-colors focus:ring-gray-500 min-h-[40px] min-w-[40px] flex items-center justify-center ${viewMode === 'list' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'}`,\n              title: \"Vue liste\",\n              children: /*#__PURE__*/_jsxDEV(FiList, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(AdvancedFilters, {\n          filters: filters,\n          onFiltersChange: setFilters,\n          onClose: () => setShowFilters(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), showHeatmap && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 lg:p-4 border-b border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(HeatmapControls, {\n          intensity: heatmapIntensity,\n          onIntensityChange: setHeatmapIntensity,\n          radius: heatmapRadius,\n          onRadiusChange: setHeatmapRadius,\n          activityType: heatmapActivityType,\n          onActivityTypeChange: setHeatmapActivityType,\n          timeRange: heatmapTimeRange,\n          onTimeRangeChange: setHeatmapTimeRange,\n          visible: showHeatmap,\n          onVisibilityChange: setShowHeatmap\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto custom-scrollbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 md:p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-base md:text-lg font-semibold text-gray-900\",\n              children: \"Parcours populaires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n              children: filteredRoutes.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: viewMode === 'grid' ? 'grid grid-cols-1 gap-3 md:gap-4' : 'space-y-3',\n            children: filteredRoutes.length > 0 ? filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(RouteCard, {\n              route: route,\n              viewMode: viewMode,\n              onSelect: handleRouteSelect,\n              isSelected: (selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id\n            }, route.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8 text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Aucun parcours trouv\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 relative h-64 md:h-auto md:min-h-0\",\n      children: [/*#__PURE__*/_jsxDEV(ModernMap, {\n        center: userPosition,\n        routes: filteredRoutes,\n        pois: pois,\n        segments: segments,\n        selectedRoute: selectedRoute,\n        selectedSegment: selectedSegment,\n        onRouteSelect: handleRouteSelect,\n        onSegmentSelect: handleSegmentSelect,\n        userPosition: userPosition,\n        children: showHeatmap && /*#__PURE__*/_jsxDEV(HeatmapLayer, {\n          intensity: heatmapIntensity,\n          radius: heatmapRadius,\n          activityType: heatmapActivityType,\n          timeRange: heatmapTimeRange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), selectedSegment && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2 md:top-4 md:right-4 w-64 md:w-72 lg:w-80 max-w-[calc(100vw-1rem)]\",\n        children: /*#__PURE__*/_jsxDEV(SegmentPanel, {\n          segments: segments,\n          selectedSegment: selectedSegment,\n          onSegmentSelect: handleSegmentSelect,\n          userPosition: userPosition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n  const renderFeedView = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto px-4 py-6\",\n    children: /*#__PURE__*/_jsxDEV(ActivityFeed, {\n      activities: activities,\n      onActivitySelect: handleActivitySelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n  const renderSegmentsView = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col md:flex-row h-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full md:w-80 lg:w-96 xl:w-1/4 bg-white border-b md:border-b-0 md:border-r border-gray-200 h-auto md:h-full overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 md:p-4 h-full flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-base md:text-lg font-semibold text-gray-900\",\n            children: \"Segments populaires\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n            children: segments.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto custom-scrollbar space-y-3 max-h-64 md:max-h-none\",\n          children: segments.length > 0 ? segments.map(segment => /*#__PURE__*/_jsxDEV(SegmentCard, {\n            segment: segment,\n            onSelect: handleSegmentSelect,\n            isSelected: (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id\n          }, segment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n              className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Aucun segment trouv\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 relative h-64 md:h-auto md:min-h-0\",\n      children: /*#__PURE__*/_jsxDEV(ModernMap, {\n        center: userPosition,\n        routes: [],\n        pois: [],\n        segments: segments,\n        selectedSegment: selectedSegment,\n        onSegmentSelect: handleSegmentSelect,\n        userPosition: userPosition\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(StravaHeader, {\n      activeView: activeView,\n      onViewChange: setActiveView,\n      searchTerm: searchTerm,\n      onSearchChange: setSearchTerm,\n      showFilters: showFilters,\n      onToggleFilters: () => setShowFilters(!showFilters),\n      viewMode: viewMode,\n      onViewModeChange: setViewMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden\",\n      children: [activeView === 'feed' && renderFeedView(), activeView === 'explore' && renderExploreView(), activeView === 'segments' && renderSegmentsView(), activeView === 'athletes' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"h-12 w-12 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-2\",\n            children: \"Communaut\\xE9 d'athl\\xE8tes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Fonctionnalit\\xE9 en cours de d\\xE9veloppement\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 355,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant RouteCard\n_s(RoutesModern, \"n69/yGT6vr5XirAwdeBKtVWRs4Q=\");\n_c = RoutesModern;\nconst RouteCard = ({\n  route,\n  viewMode,\n  onSelect,\n  isSelected\n}) => {\n  var _route$elevation2, _route$rating2;\n  const getDifficultyColor = difficulty => {\n    const colors = {\n      'facile': 'text-green-600 bg-green-50',\n      'modéré': 'text-yellow-600 bg-yellow-50',\n      'difficile': 'text-red-600 bg-red-50'\n    };\n    return colors[difficulty] || 'text-gray-600 bg-gray-50';\n  };\n  const getActivityIcon = type => {\n    const icons = {\n      running: '🏃‍♂️',\n      cycling: '🚴‍♂️',\n      hiking: '🥾',\n      walking: '🚶‍♂️'\n    };\n    return icons[type] || '🏃‍♂️';\n  };\n  if (viewMode === 'list') {\n    var _route$elevation, _route$rating;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => onSelect(route),\n      className: `flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-all ${isSelected ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl\",\n        children: getActivityIcon(route.type)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-medium text-gray-900 truncate\",\n          children: route.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [route.distance.toFixed(1), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [((_route$elevation = route.elevation) === null || _route$elevation === void 0 ? void 0 : _route$elevation.gain) || 0, \"m D+\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `px-2 py-1 rounded-full text-xs ${getDifficultyColor(route.difficulty)}`,\n            children: route.difficulty\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-yellow-500\",\n          children: [/*#__PURE__*/_jsxDEV(FiStar, {\n            className: \"h-4 w-4 fill-current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm ml-1\",\n            children: ((_route$rating = route.rating) === null || _route$rating === void 0 ? void 0 : _route$rating.toFixed(1)) || '4.5'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    onClick: () => onSelect(route),\n    className: `btn-interactive bg-white rounded-lg border cursor-pointer transition-all hover:shadow-md ${isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: getActivityIcon(route.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-900 text-sm\",\n            children: route.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-interactive touch-button-small text-gray-400 hover:text-red-500 transition-colors p-1 rounded focus:ring-red-500\",\n          children: /*#__PURE__*/_jsxDEV(FiHeart, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-2 mb-3 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [route.distance.toFixed(1), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [((_route$elevation2 = route.elevation) === null || _route$elevation2 === void 0 ? void 0 : _route$elevation2.gain) || 0, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(FiClock, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Math.round(route.distance * 6), \" min\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: route.completions || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n          children: route.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-yellow-500\",\n          children: [/*#__PURE__*/_jsxDEV(FiStar, {\n            className: \"h-3 w-3 fill-current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs ml-1\",\n            children: ((_route$rating2 = route.rating) === null || _route$rating2 === void 0 ? void 0 : _route$rating2.toFixed(1)) || '4.5'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 438,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant SegmentCard\n_c2 = RouteCard;\nconst SegmentCard = ({\n  segment,\n  onSelect,\n  isSelected\n}) => {\n  const getDifficultyColor = difficulty => {\n    const colors = {\n      'facile': 'text-green-600 bg-green-50',\n      'modéré': 'text-yellow-600 bg-yellow-50',\n      'difficile': 'text-red-600 bg-red-50'\n    };\n    return colors[difficulty] || 'text-gray-600 bg-gray-50';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    onClick: () => onSelect(segment),\n    className: `btn-interactive bg-white rounded-lg border p-4 cursor-pointer transition-all hover:shadow-md ${isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-semibold text-gray-900 text-sm\",\n        children: segment.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-orange-500\",\n        children: \"\\uD83C\\uDFC6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2 mb-3 text-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n          className: \"h-3 w-3 mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [(segment.distance / 1000).toFixed(1), \" km\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n          className: \"h-3 w-3 mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [segment.elevation, \"m D+\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n          className: \"h-3 w-3 mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [segment.attempts || 156, \" tentatives\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(FiActivity, {\n          className: \"h-3 w-3 mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"KOM: 12:34\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`,\n        children: segment.difficulty\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-interactive touch-button-small text-orange-600 hover:text-orange-700 text-xs font-medium px-2 py-1 rounded focus:ring-orange-500\",\n        children: \"Tenter \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 500,\n    columnNumber: 5\n  }, this);\n};\n_c3 = SegmentCard;\nexport default RoutesModern;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RoutesModern\");\n$RefreshReg$(_c2, \"RouteCard\");\n$RefreshReg$(_c3, \"SegmentCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "FiMap", "FiActivity", "FiTrendingUp", "FiUsers", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiList", "FiSettings", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiShare2", "FiMapPin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiZap", "FiEye", "FiPlay", "FiStar", "ModernMap", "StravaHeader", "ActivityFeed", "Heatmap<PERSON>ayer", "HeatmapControls", "AdvancedFilters", "SegmentPanel", "generatePopularRoutes", "generatePOIs", "generateSegments", "DEFAULT_POSITION", "jsxDEV", "_jsxDEV", "RoutesModern", "_s", "activeView", "setActiveView", "viewMode", "setViewMode", "searchTerm", "setSearchTerm", "showFilters", "setShowFilters", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "segments", "setSegments", "activities", "setActivities", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedSegment", "setSelectedSegment", "filters", "setFilters", "type", "difficulty", "distance", "sport", "surface", "showHeatmap", "setShowHeatmap", "heatmapIntensity", "setHeatmapIntensity", "heatmapRadius", "setHeatmapRadius", "heatmapActivityType", "setHeatmapActivityType", "heatmapTimeRange", "setHeatmapTimeRange", "initializeData", "generatedRoutes", "lat", "lng", "generatedPOIs", "generatedSegments", "navigator", "geolocation", "getCurrentPosition", "position", "coords", "latitude", "longitude", "error", "console", "log", "filteredRoutes", "filter", "route", "name", "toLowerCase", "includes", "min", "max", "split", "map", "Number", "handleRouteSelect", "handleSegmentSelect", "segment", "handleActivitySelect", "activity", "renderExploreView", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "e", "target", "onClick", "title", "onFiltersChange", "onClose", "intensity", "onIntensityChange", "radius", "onRadiusChange", "activityType", "onActivityTypeChange", "timeRange", "onTimeRangeChange", "visible", "onVisibilityChange", "length", "RouteCard", "onSelect", "isSelected", "id", "center", "onRouteSelect", "onSegmentSelect", "renderFeedView", "onActivitySelect", "renderSegmentsView", "SegmentCard", "onViewChange", "onSearchChange", "onToggleFilters", "onViewModeChange", "_c", "_route$elevation2", "_route$rating2", "getDifficultyColor", "colors", "getActivityIcon", "icons", "running", "cycling", "hiking", "walking", "_route$elevation", "_route$rating", "toFixed", "elevation", "gain", "rating", "Math", "round", "completions", "_c2", "attempts", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/RoutesModern.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  FiMap,\n  FiActivity,\n  FiTrendingUp,\n  FiUsers,\n  FiSearch,\n  FiFilter,\n  FiGrid,\n  FiList,\n  FiSettings,\n  FiHeart,\n  FiBookmark,\n  FiShare2,\n  FiMapPin,\n  FiClock,\n  FiTarget,\n  FiZap,\n  FiEye,\n  FiPlay,\n  FiStar\n} from 'react-icons/fi';\nimport ModernMap from '../components/ModernMap';\nimport StravaHeader from '../components/StravaHeader';\nimport ActivityFeed from '../components/ActivityFeed';\nimport HeatmapLayer, { HeatmapControls } from '../components/HeatmapLayer';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\nimport { generatePopularRoutes, generatePOIs, generateSegments, DEFAULT_POSITION } from '../utils/mapUtils';\n\nconst RoutesModern = () => {\n  // États principaux\n  const [activeView, setActiveView] = useState('explore');\n  const [viewMode, setViewMode] = useState('grid');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  \n  // États pour les données\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [activities, setActivities] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  \n  // États pour les filtres\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  \n  // États pour la heatmap\n  const [showHeatmap, setShowHeatmap] = useState(false);\n  const [heatmapIntensity, setHeatmapIntensity] = useState(0.6);\n  const [heatmapRadius, setHeatmapRadius] = useState(25);\n  const [heatmapActivityType, setHeatmapActivityType] = useState('all');\n  const [heatmapTimeRange, setHeatmapTimeRange] = useState('all');\n\n  // Initialisation des données\n  useEffect(() => {\n    const initializeData = () => {\n      const generatedRoutes = generatePopularRoutes(userPosition.lat, userPosition.lng, 20);\n      const generatedPOIs = generatePOIs(userPosition.lat, userPosition.lng, 15);\n      const generatedSegments = generateSegments(generatedRoutes, 10);\n      \n      setRoutes(generatedRoutes);\n      setPois(generatedPOIs);\n      setSegments(generatedSegments);\n    };\n\n    initializeData();\n  }, [userPosition]);\n\n  // Géolocalisation\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          setUserPosition({\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          });\n        },\n        (error) => {\n          console.log('Erreur de géolocalisation:', error);\n        }\n      );\n    }\n  }, []);\n\n  // Filtrage des routes\n  const filteredRoutes = routes.filter(route => {\n    if (searchTerm && !route.name.toLowerCase().includes(searchTerm.toLowerCase())) {\n      return false;\n    }\n    if (filters.sport !== 'all' && route.type !== filters.sport) {\n      return false;\n    }\n    if (filters.difficulty !== 'all' && route.difficulty !== filters.difficulty) {\n      return false;\n    }\n    if (filters.distance !== 'all') {\n      const [min, max] = filters.distance.split('-').map(Number);\n      if (max && (route.distance < min || route.distance > max)) {\n        return false;\n      }\n      if (!max && route.distance < min) {\n        return false;\n      }\n    }\n    return true;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setActiveView('explore');\n  };\n\n  const handleSegmentSelect = (segment) => {\n    setSelectedSegment(segment);\n  };\n\n  const handleActivitySelect = (activity) => {\n    // Logique pour afficher l'activité sur la carte\n    setActiveView('explore');\n  };\n\n  const renderExploreView = () => (\n    <div className=\"flex flex-col md:flex-row h-full\">\n      {/* Sidebar - Responsive */}\n      <div className=\"w-full md:w-80 lg:w-96 xl:w-1/4 bg-white border-b md:border-b-0 md:border-r border-gray-200 flex flex-col h-auto md:h-full overflow-hidden\">\n        {/* Search & Filters - Mobile Optimized */}\n        <div className=\"p-3 md:p-4 border-b border-gray-100 space-y-3\">\n          {/* Search Bar */}\n          <div className=\"relative\">\n            <FiSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher des parcours...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm\"\n            />\n          </div>\n\n          {/* Controls Row */}\n          <div className=\"flex flex-col xs:flex-row items-stretch xs:items-center justify-between gap-3\">\n            {/* Filter Buttons */}\n            <div className=\"flex flex-wrap items-center gap-2\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`btn-interactive flex items-center justify-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm min-h-[40px] ${\n                  showFilters ? 'bg-orange-50 text-orange-600 focus:ring-orange-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'\n                }`}\n              >\n                <FiFilter className=\"h-4 w-4\" />\n                <span>Filtres</span>\n              </button>\n\n              <button\n                onClick={() => setShowHeatmap(!showHeatmap)}\n                className={`btn-interactive flex items-center justify-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm min-h-[40px] ${\n                  showHeatmap ? 'bg-blue-50 text-blue-600 focus:ring-blue-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'\n                }`}\n              >\n                <FiZap className=\"h-4 w-4\" />\n                <span>Heatmap</span>\n              </button>\n            </div>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1 self-start xs:self-auto\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`btn-interactive p-2.5 rounded transition-colors focus:ring-gray-500 min-h-[40px] min-w-[40px] flex items-center justify-center ${\n                  viewMode === 'grid' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'\n                }`}\n                title=\"Vue grille\"\n              >\n                <FiGrid className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`btn-interactive p-2.5 rounded transition-colors focus:ring-gray-500 min-h-[40px] min-w-[40px] flex items-center justify-center ${\n                  viewMode === 'list' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'\n                }`}\n                title=\"Vue liste\"\n              >\n                <FiList className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters Panel */}\n        {showFilters && (\n          <div className=\"border-b border-gray-100\">\n            <AdvancedFilters\n              filters={filters}\n              onFiltersChange={setFilters}\n              onClose={() => setShowFilters(false)}\n            />\n          </div>\n        )}\n\n        {/* Heatmap Controls */}\n        {showHeatmap && (\n          <div className=\"p-3 lg:p-4 border-b border-gray-100\">\n            <HeatmapControls\n              intensity={heatmapIntensity}\n              onIntensityChange={setHeatmapIntensity}\n              radius={heatmapRadius}\n              onRadiusChange={setHeatmapRadius}\n              activityType={heatmapActivityType}\n              onActivityTypeChange={setHeatmapActivityType}\n              timeRange={heatmapTimeRange}\n              onTimeRangeChange={setHeatmapTimeRange}\n              visible={showHeatmap}\n              onVisibilityChange={setShowHeatmap}\n            />\n          </div>\n        )}\n\n        {/* Routes List */}\n        <div className=\"flex-1 overflow-y-auto custom-scrollbar\">\n          <div className=\"p-3 md:p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-base md:text-lg font-semibold text-gray-900\">\n                Parcours populaires\n              </h2>\n              <span className=\"text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n                {filteredRoutes.length}\n              </span>\n            </div>\n\n            <div className={viewMode === 'grid' ? 'grid grid-cols-1 gap-3 md:gap-4' : 'space-y-3'}>\n              {filteredRoutes.length > 0 ? (\n                filteredRoutes.map((route) => (\n                  <RouteCard\n                    key={route.id}\n                    route={route}\n                    viewMode={viewMode}\n                    onSelect={handleRouteSelect}\n                    isSelected={selectedRoute?.id === route.id}\n                  />\n                ))\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <FiMapPin className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucun parcours trouvé</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Map - Responsive */}\n      <div className=\"flex-1 relative h-64 md:h-auto md:min-h-0\">\n        <ModernMap\n          center={userPosition}\n          routes={filteredRoutes}\n          pois={pois}\n          segments={segments}\n          selectedRoute={selectedRoute}\n          selectedSegment={selectedSegment}\n          onRouteSelect={handleRouteSelect}\n          onSegmentSelect={handleSegmentSelect}\n          userPosition={userPosition}\n        >\n          {showHeatmap && (\n            <HeatmapLayer\n              intensity={heatmapIntensity}\n              radius={heatmapRadius}\n              activityType={heatmapActivityType}\n              timeRange={heatmapTimeRange}\n            />\n          )}\n        </ModernMap>\n\n        {selectedSegment && (\n          <div className=\"absolute top-2 right-2 md:top-4 md:right-4 w-64 md:w-72 lg:w-80 max-w-[calc(100vw-1rem)]\">\n            <SegmentPanel\n              segments={segments}\n              selectedSegment={selectedSegment}\n              onSegmentSelect={handleSegmentSelect}\n              userPosition={userPosition}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderFeedView = () => (\n    <div className=\"max-w-4xl mx-auto px-4 py-6\">\n      <ActivityFeed\n        activities={activities}\n        onActivitySelect={handleActivitySelect}\n      />\n    </div>\n  );\n\n  const renderSegmentsView = () => (\n    <div className=\"flex flex-col md:flex-row h-full\">\n      <div className=\"w-full md:w-80 lg:w-96 xl:w-1/4 bg-white border-b md:border-b-0 md:border-r border-gray-200 h-auto md:h-full overflow-hidden\">\n        <div className=\"p-3 md:p-4 h-full flex flex-col\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-base md:text-lg font-semibold text-gray-900\">\n              Segments populaires\n            </h2>\n            <span className=\"text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n              {segments.length}\n            </span>\n          </div>\n          <div className=\"flex-1 overflow-y-auto custom-scrollbar space-y-3 max-h-64 md:max-h-none\">\n            {segments.length > 0 ? (\n              segments.map((segment) => (\n                <SegmentCard\n                  key={segment.id}\n                  segment={segment}\n                  onSelect={handleSegmentSelect}\n                  isSelected={selectedSegment?.id === segment.id}\n                />\n              ))\n            ) : (\n              <div className=\"text-center py-8 text-gray-500\">\n                <FiTrendingUp className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                <p>Aucun segment trouvé</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex-1 relative h-64 md:h-auto md:min-h-0\">\n        <ModernMap\n          center={userPosition}\n          routes={[]}\n          pois={[]}\n          segments={segments}\n          selectedSegment={selectedSegment}\n          onSegmentSelect={handleSegmentSelect}\n          userPosition={userPosition}\n        />\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50\">\n      <StravaHeader\n        activeView={activeView}\n        onViewChange={setActiveView}\n        searchTerm={searchTerm}\n        onSearchChange={setSearchTerm}\n        showFilters={showFilters}\n        onToggleFilters={() => setShowFilters(!showFilters)}\n        viewMode={viewMode}\n        onViewModeChange={setViewMode}\n      />\n      \n      <div className=\"flex-1 overflow-hidden\">\n        {activeView === 'feed' && renderFeedView()}\n        {activeView === 'explore' && renderExploreView()}\n        {activeView === 'segments' && renderSegmentsView()}\n        {activeView === 'athletes' && (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center text-gray-500\">\n              <FiUsers className=\"h-12 w-12 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium mb-2\">Communauté d'athlètes</h3>\n              <p>Fonctionnalité en cours de développement</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Composant RouteCard\nconst RouteCard = ({ route, viewMode, onSelect, isSelected }) => {\n  const getDifficultyColor = (difficulty) => {\n    const colors = {\n      'facile': 'text-green-600 bg-green-50',\n      'modéré': 'text-yellow-600 bg-yellow-50',\n      'difficile': 'text-red-600 bg-red-50'\n    };\n    return colors[difficulty] || 'text-gray-600 bg-gray-50';\n  };\n\n  const getActivityIcon = (type) => {\n    const icons = {\n      running: '🏃‍♂️',\n      cycling: '🚴‍♂️',\n      hiking: '🥾',\n      walking: '🚶‍♂️'\n    };\n    return icons[type] || '🏃‍♂️';\n  };\n\n  if (viewMode === 'list') {\n    return (\n      <div\n        onClick={() => onSelect(route)}\n        className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-all ${\n          isSelected\n            ? 'border-orange-500 bg-orange-50'\n            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n        }`}\n      >\n        <div className=\"text-2xl\">{getActivityIcon(route.type)}</div>\n        <div className=\"flex-1 min-w-0\">\n          <h3 className=\"font-medium text-gray-900 truncate\">{route.name}</h3>\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <span>{route.distance.toFixed(1)} km</span>\n            <span>{route.elevation?.gain || 0}m D+</span>\n            <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(route.difficulty)}`}>\n              {route.difficulty}\n            </span>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"flex items-center text-yellow-500\">\n            <FiStar className=\"h-4 w-4 fill-current\" />\n            <span className=\"text-sm ml-1\">{route.rating?.toFixed(1) || '4.5'}</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      onClick={() => onSelect(route)}\n      className={`btn-interactive bg-white rounded-lg border cursor-pointer transition-all hover:shadow-md ${\n        isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'\n      }`}\n    >\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-xl\">{getActivityIcon(route.type)}</span>\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{route.name}</h3>\n          </div>\n          <button className=\"btn-interactive touch-button-small text-gray-400 hover:text-red-500 transition-colors p-1 rounded focus:ring-red-500\">\n            <FiHeart className=\"h-4 w-4\" />\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-2 mb-3 text-sm\">\n          <div className=\"flex items-center text-gray-600\">\n            <FiMapPin className=\"h-3 w-3 mr-1\" />\n            <span>{route.distance.toFixed(1)} km</span>\n          </div>\n          <div className=\"flex items-center text-gray-600\">\n            <FiTrendingUp className=\"h-3 w-3 mr-1\" />\n            <span>{route.elevation?.gain || 0}m</span>\n          </div>\n          <div className=\"flex items-center text-gray-600\">\n            <FiClock className=\"h-3 w-3 mr-1\" />\n            <span>{Math.round(route.distance * 6)} min</span>\n          </div>\n          <div className=\"flex items-center text-gray-600\">\n            <FiUsers className=\"h-3 w-3 mr-1\" />\n            <span>{route.completions || 0}</span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n            {route.difficulty}\n          </span>\n          <div className=\"flex items-center text-yellow-500\">\n            <FiStar className=\"h-3 w-3 fill-current\" />\n            <span className=\"text-xs ml-1\">{route.rating?.toFixed(1) || '4.5'}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant SegmentCard\nconst SegmentCard = ({ segment, onSelect, isSelected }) => {\n  const getDifficultyColor = (difficulty) => {\n    const colors = {\n      'facile': 'text-green-600 bg-green-50',\n      'modéré': 'text-yellow-600 bg-yellow-50',\n      'difficile': 'text-red-600 bg-red-50'\n    };\n    return colors[difficulty] || 'text-gray-600 bg-gray-50';\n  };\n\n  return (\n    <div\n      onClick={() => onSelect(segment)}\n      className={`btn-interactive bg-white rounded-lg border p-4 cursor-pointer transition-all hover:shadow-md ${\n        isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'\n      }`}\n    >\n      <div className=\"flex items-start justify-between mb-3\">\n        <h3 className=\"font-semibold text-gray-900 text-sm\">{segment.name}</h3>\n        <span className=\"text-orange-500\">🏆</span>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-2 mb-3 text-sm\">\n        <div className=\"flex items-center text-gray-600\">\n          <FiMapPin className=\"h-3 w-3 mr-1\" />\n          <span>{(segment.distance / 1000).toFixed(1)} km</span>\n        </div>\n        <div className=\"flex items-center text-gray-600\">\n          <FiTrendingUp className=\"h-3 w-3 mr-1\" />\n          <span>{segment.elevation}m D+</span>\n        </div>\n        <div className=\"flex items-center text-gray-600\">\n          <FiTarget className=\"h-3 w-3 mr-1\" />\n          <span>{segment.attempts || 156} tentatives</span>\n        </div>\n        <div className=\"flex items-center text-gray-600\">\n          <FiActivity className=\"h-3 w-3 mr-1\" />\n          <span>KOM: 12:34</span>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between\">\n        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>\n          {segment.difficulty}\n        </span>\n        <button className=\"btn-interactive touch-button-small text-orange-600 hover:text-orange-700 text-xs font-medium px-2 py-1 rounded focus:ring-orange-500\">\n          Tenter →\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default RoutesModern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,IAAIC,eAAe,QAAQ,4BAA4B;AAC1E,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5G,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAACgC,gBAAgB,CAAC;;EAElE;EACA,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,IAAI,EAAEC,OAAO,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC;IACrC6D,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,GAAG,CAAC;EAC7D,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2E,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,eAAe,GAAGhD,qBAAqB,CAACgB,YAAY,CAACiC,GAAG,EAAEjC,YAAY,CAACkC,GAAG,EAAE,EAAE,CAAC;MACrF,MAAMC,aAAa,GAAGlD,YAAY,CAACe,YAAY,CAACiC,GAAG,EAAEjC,YAAY,CAACkC,GAAG,EAAE,EAAE,CAAC;MAC1E,MAAME,iBAAiB,GAAGlD,gBAAgB,CAAC8C,eAAe,EAAE,EAAE,CAAC;MAE/D7B,SAAS,CAAC6B,eAAe,CAAC;MAC1B3B,OAAO,CAAC8B,aAAa,CAAC;MACtB5B,WAAW,CAAC6B,iBAAiB,CAAC;IAChC,CAAC;IAEDL,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC/B,YAAY,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,IAAIiF,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZvC,eAAe,CAAC;UACdgC,GAAG,EAAEO,QAAQ,CAACC,MAAM,CAACC,QAAQ;UAC7BR,GAAG,EAAEM,QAAQ,CAACC,MAAM,CAACE;QACvB,CAAC,CAAC;MACJ,CAAC,EACAC,KAAK,IAAK;QACTC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,KAAK,CAAC;MAClD,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,cAAc,GAAG7C,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAI;IAC5C,IAAIrD,UAAU,IAAI,CAACqD,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxD,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,EAAE;MAC9E,OAAO,KAAK;IACd;IACA,IAAIrC,OAAO,CAACK,KAAK,KAAK,KAAK,IAAI8B,KAAK,CAACjC,IAAI,KAAKF,OAAO,CAACK,KAAK,EAAE;MAC3D,OAAO,KAAK;IACd;IACA,IAAIL,OAAO,CAACG,UAAU,KAAK,KAAK,IAAIgC,KAAK,CAAChC,UAAU,KAAKH,OAAO,CAACG,UAAU,EAAE;MAC3E,OAAO,KAAK;IACd;IACA,IAAIH,OAAO,CAACI,QAAQ,KAAK,KAAK,EAAE;MAC9B,MAAM,CAACmC,GAAG,EAAEC,GAAG,CAAC,GAAGxC,OAAO,CAACI,QAAQ,CAACqC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;MAC1D,IAAIH,GAAG,KAAKL,KAAK,CAAC/B,QAAQ,GAAGmC,GAAG,IAAIJ,KAAK,CAAC/B,QAAQ,GAAGoC,GAAG,CAAC,EAAE;QACzD,OAAO,KAAK;MACd;MACA,IAAI,CAACA,GAAG,IAAIL,KAAK,CAAC/B,QAAQ,GAAGmC,GAAG,EAAE;QAChC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,MAAMK,iBAAiB,GAAIT,KAAK,IAAK;IACnCtC,gBAAgB,CAACsC,KAAK,CAAC;IACvBxD,aAAa,CAAC,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMkE,mBAAmB,GAAIC,OAAO,IAAK;IACvC/C,kBAAkB,CAAC+C,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMC,oBAAoB,GAAIC,QAAQ,IAAK;IACzC;IACArE,aAAa,CAAC,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMsE,iBAAiB,GAAGA,CAAA,kBACxB1E,OAAA;IAAK2E,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAE/C5E,OAAA;MAAK2E,SAAS,EAAC,4IAA4I;MAAAC,QAAA,gBAEzJ5E,OAAA;QAAK2E,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAE5D5E,OAAA;UAAK2E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5E,OAAA,CAAC3B,QAAQ;YAACsG,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjGhF,OAAA;YACE2B,IAAI,EAAC,MAAM;YACXsD,WAAW,EAAC,4BAA4B;YACxCC,KAAK,EAAE3E,UAAW;YAClB4E,QAAQ,EAAGC,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CP,SAAS,EAAC;UAA8J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhF,OAAA;UAAK2E,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAE5F5E,OAAA;YAAK2E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5E,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAM5E,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CkE,SAAS,EAAE,0HACTlE,WAAW,GAAG,oDAAoD,GAAG,oDAAoD,EACxH;cAAAmE,QAAA,gBAEH5E,OAAA,CAAC1B,QAAQ;gBAACqG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChChF,OAAA;gBAAA4E,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAEThF,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAMrD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C2C,SAAS,EAAE,0HACT3C,WAAW,GAAG,8CAA8C,GAAG,oDAAoD,EAClH;cAAA4C,QAAA,gBAEH5E,OAAA,CAAChB,KAAK;gBAAC2F,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7BhF,OAAA;gBAAA4E,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhF,OAAA;YAAK2E,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtE5E,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,MAAM,CAAE;cACnCqE,SAAS,EAAE,kIACTtE,QAAQ,KAAK,MAAM,GAAG,kCAAkC,GAAG,mCAAmC,EAC7F;cACHkF,KAAK,EAAC,YAAY;cAAAX,QAAA,eAElB5E,OAAA,CAACzB,MAAM;gBAACoG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACThF,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,MAAM,CAAE;cACnCqE,SAAS,EAAE,kIACTtE,QAAQ,KAAK,MAAM,GAAG,kCAAkC,GAAG,mCAAmC,EAC7F;cACHkF,KAAK,EAAC,WAAW;cAAAX,QAAA,eAEjB5E,OAAA,CAACxB,MAAM;gBAACmG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLvE,WAAW,iBACVT,OAAA;QAAK2E,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvC5E,OAAA,CAACP,eAAe;UACdgC,OAAO,EAAEA,OAAQ;UACjB+D,eAAe,EAAE9D,UAAW;UAC5B+D,OAAO,EAAEA,CAAA,KAAM/E,cAAc,CAAC,KAAK;QAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAhD,WAAW,iBACVhC,OAAA;QAAK2E,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD5E,OAAA,CAACR,eAAe;UACdkG,SAAS,EAAExD,gBAAiB;UAC5ByD,iBAAiB,EAAExD,mBAAoB;UACvCyD,MAAM,EAAExD,aAAc;UACtByD,cAAc,EAAExD,gBAAiB;UACjCyD,YAAY,EAAExD,mBAAoB;UAClCyD,oBAAoB,EAAExD,sBAAuB;UAC7CyD,SAAS,EAAExD,gBAAiB;UAC5ByD,iBAAiB,EAAExD,mBAAoB;UACvCyD,OAAO,EAAElE,WAAY;UACrBmE,kBAAkB,EAAElE;QAAe;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDhF,OAAA;QAAK2E,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACtD5E,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5E,OAAA;YAAK2E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5E,OAAA;cAAI2E,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhF,OAAA;cAAM2E,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAClFlB,cAAc,CAAC0C;YAAM;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAEtE,QAAQ,KAAK,MAAM,GAAG,iCAAiC,GAAG,WAAY;YAAAuE,QAAA,EACnFlB,cAAc,CAAC0C,MAAM,GAAG,CAAC,GACxB1C,cAAc,CAACS,GAAG,CAAEP,KAAK,iBACvB5D,OAAA,CAACqG,SAAS;cAERzC,KAAK,EAAEA,KAAM;cACbvD,QAAQ,EAAEA,QAAS;cACnBiG,QAAQ,EAAEjC,iBAAkB;cAC5BkC,UAAU,EAAE,CAAAlF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmF,EAAE,MAAK5C,KAAK,CAAC4C;YAAG,GAJtC5C,KAAK,CAAC4C,EAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKd,CACF,CAAC,gBAEFhF,OAAA;cAAK2E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C5E,OAAA,CAACnB,QAAQ;gBAAC8F,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DhF,OAAA;gBAAA4E,QAAA,EAAG;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,2CAA2C;MAAAC,QAAA,gBACxD5E,OAAA,CAACZ,SAAS;QACRqH,MAAM,EAAE9F,YAAa;QACrBE,MAAM,EAAE6C,cAAe;QACvB3C,IAAI,EAAEA,IAAK;QACXE,QAAQ,EAAEA,QAAS;QACnBI,aAAa,EAAEA,aAAc;QAC7BE,eAAe,EAAEA,eAAgB;QACjCmF,aAAa,EAAErC,iBAAkB;QACjCsC,eAAe,EAAErC,mBAAoB;QACrC3D,YAAY,EAAEA,YAAa;QAAAiE,QAAA,EAE1B5C,WAAW,iBACVhC,OAAA,CAACT,YAAY;UACXmG,SAAS,EAAExD,gBAAiB;UAC5B0D,MAAM,EAAExD,aAAc;UACtB0D,YAAY,EAAExD,mBAAoB;UAClC0D,SAAS,EAAExD;QAAiB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEXzD,eAAe,iBACdvB,OAAA;QAAK2E,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACvG5E,OAAA,CAACN,YAAY;UACXuB,QAAQ,EAAEA,QAAS;UACnBM,eAAe,EAAEA,eAAgB;UACjCoF,eAAe,EAAErC,mBAAoB;UACrC3D,YAAY,EAAEA;QAAa;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM4B,cAAc,GAAGA,CAAA,kBACrB5G,OAAA;IAAK2E,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1C5E,OAAA,CAACV,YAAY;MACX6B,UAAU,EAAEA,UAAW;MACvB0F,gBAAgB,EAAErC;IAAqB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAM8B,kBAAkB,GAAGA,CAAA,kBACzB9G,OAAA;IAAK2E,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAC/C5E,OAAA;MAAK2E,SAAS,EAAC,8HAA8H;MAAAC,QAAA,eAC3I5E,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C5E,OAAA;UAAK2E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5E,OAAA;YAAI2E,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAM2E,SAAS,EAAC,qEAAqE;YAAAC,QAAA,EAClF3D,QAAQ,CAACmF;UAAM;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EACtF3D,QAAQ,CAACmF,MAAM,GAAG,CAAC,GAClBnF,QAAQ,CAACkD,GAAG,CAAEI,OAAO,iBACnBvE,OAAA,CAAC+G,WAAW;YAEVxC,OAAO,EAAEA,OAAQ;YACjB+B,QAAQ,EAAEhC,mBAAoB;YAC9BiC,UAAU,EAAE,CAAAhF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiF,EAAE,MAAKjC,OAAO,CAACiC;UAAG,GAH1CjC,OAAO,CAACiC,EAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIhB,CACF,CAAC,gBAEFhF,OAAA;YAAK2E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5E,OAAA,CAAC7B,YAAY;cAACwG,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEhF,OAAA;cAAA4E,QAAA,EAAG;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxD5E,OAAA,CAACZ,SAAS;QACRqH,MAAM,EAAE9F,YAAa;QACrBE,MAAM,EAAE,EAAG;QACXE,IAAI,EAAE,EAAG;QACTE,QAAQ,EAAEA,QAAS;QACnBM,eAAe,EAAEA,eAAgB;QACjCoF,eAAe,EAAErC,mBAAoB;QACrC3D,YAAY,EAAEA;MAAa;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChD5E,OAAA,CAACX,YAAY;MACXc,UAAU,EAAEA,UAAW;MACvB6G,YAAY,EAAE5G,aAAc;MAC5BG,UAAU,EAAEA,UAAW;MACvB0G,cAAc,EAAEzG,aAAc;MAC9BC,WAAW,EAAEA,WAAY;MACzByG,eAAe,EAAEA,CAAA,KAAMxG,cAAc,CAAC,CAACD,WAAW,CAAE;MACpDJ,QAAQ,EAAEA,QAAS;MACnB8G,gBAAgB,EAAE7G;IAAY;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAEFhF,OAAA;MAAK2E,SAAS,EAAC,wBAAwB;MAAAC,QAAA,GACpCzE,UAAU,KAAK,MAAM,IAAIyG,cAAc,CAAC,CAAC,EACzCzG,UAAU,KAAK,SAAS,IAAIuE,iBAAiB,CAAC,CAAC,EAC/CvE,UAAU,KAAK,UAAU,IAAI2G,kBAAkB,CAAC,CAAC,EACjD3G,UAAU,KAAK,UAAU,iBACxBH,OAAA;QAAK2E,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACtD5E,OAAA;UAAK2E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC5E,OAAA,CAAC5B,OAAO;YAACuG,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ChF,OAAA;YAAI2E,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEhF,OAAA;YAAA4E,QAAA,EAAG;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA9E,EAAA,CAlWMD,YAAY;AAAAmH,EAAA,GAAZnH,YAAY;AAmWlB,MAAMoG,SAAS,GAAGA,CAAC;EAAEzC,KAAK;EAAEvD,QAAQ;EAAEiG,QAAQ;EAAEC;AAAW,CAAC,KAAK;EAAA,IAAAc,iBAAA,EAAAC,cAAA;EAC/D,MAAMC,kBAAkB,GAAI3F,UAAU,IAAK;IACzC,MAAM4F,MAAM,GAAG;MACb,QAAQ,EAAE,4BAA4B;MACtC,QAAQ,EAAE,8BAA8B;MACxC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAAC5F,UAAU,CAAC,IAAI,0BAA0B;EACzD,CAAC;EAED,MAAM6F,eAAe,GAAI9F,IAAI,IAAK;IAChC,MAAM+F,KAAK,GAAG;MACZC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAC;IACD,OAAOJ,KAAK,CAAC/F,IAAI,CAAC,IAAI,OAAO;EAC/B,CAAC;EAED,IAAItB,QAAQ,KAAK,MAAM,EAAE;IAAA,IAAA0H,gBAAA,EAAAC,aAAA;IACvB,oBACEhI,OAAA;MACEsF,OAAO,EAAEA,CAAA,KAAMgB,QAAQ,CAAC1C,KAAK,CAAE;MAC/Be,SAAS,EAAE,mFACT4B,UAAU,GACN,gCAAgC,GAChC,wDAAwD,EAC3D;MAAA3B,QAAA,gBAEH5E,OAAA;QAAK2E,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAE6C,eAAe,CAAC7D,KAAK,CAACjC,IAAI;MAAC;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7DhF,OAAA;QAAK2E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B5E,OAAA;UAAI2E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAEhB,KAAK,CAACC;QAAI;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpEhF,OAAA;UAAK2E,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE5E,OAAA;YAAA4E,QAAA,GAAOhB,KAAK,CAAC/B,QAAQ,CAACoG,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3ChF,OAAA;YAAA4E,QAAA,GAAO,EAAAmD,gBAAA,GAAAnE,KAAK,CAACsE,SAAS,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,IAAI,KAAI,CAAC,EAAC,MAAI;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7ChF,OAAA;YAAM2E,SAAS,EAAE,kCAAkC4C,kBAAkB,CAAC3D,KAAK,CAAChC,UAAU,CAAC,EAAG;YAAAgD,QAAA,EACvFhB,KAAK,CAAChC;UAAU;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhF,OAAA;QAAK2E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C5E,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA,CAACb,MAAM;YAACwF,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ChF,OAAA;YAAM2E,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE,EAAAoD,aAAA,GAAApE,KAAK,CAACwE,MAAM,cAAAJ,aAAA,uBAAZA,aAAA,CAAcC,OAAO,CAAC,CAAC,CAAC,KAAI;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhF,OAAA;IACEsF,OAAO,EAAEA,CAAA,KAAMgB,QAAQ,CAAC1C,KAAK,CAAE;IAC/Be,SAAS,EAAE,4FACT4B,UAAU,GAAG,0CAA0C,GAAG,uCAAuC,EAChG;IAAA3B,QAAA,eAEH5E,OAAA;MAAK2E,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClB5E,OAAA;QAAK2E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD5E,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5E,OAAA;YAAM2E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAE6C,eAAe,CAAC7D,KAAK,CAACjC,IAAI;UAAC;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DhF,OAAA;YAAI2E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAEhB,KAAK,CAACC;UAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNhF,OAAA;UAAQ2E,SAAS,EAAC,sHAAsH;UAAAC,QAAA,eACtI5E,OAAA,CAACtB,OAAO;YAACiG,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClD5E,OAAA;UAAK2E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C5E,OAAA,CAACnB,QAAQ;YAAC8F,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrChF,OAAA;YAAA4E,QAAA,GAAOhB,KAAK,CAAC/B,QAAQ,CAACoG,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C5E,OAAA,CAAC7B,YAAY;YAACwG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzChF,OAAA;YAAA4E,QAAA,GAAO,EAAAyC,iBAAA,GAAAzD,KAAK,CAACsE,SAAS,cAAAb,iBAAA,uBAAfA,iBAAA,CAAiBc,IAAI,KAAI,CAAC,EAAC,GAAC;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C5E,OAAA,CAAClB,OAAO;YAAC6F,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpChF,OAAA;YAAA4E,QAAA,GAAOyD,IAAI,CAACC,KAAK,CAAC1E,KAAK,CAAC/B,QAAQ,GAAG,CAAC,CAAC,EAAC,MAAI;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C5E,OAAA,CAAC5B,OAAO;YAACuG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpChF,OAAA;YAAA4E,QAAA,EAAOhB,KAAK,CAAC2E,WAAW,IAAI;UAAC;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5E,OAAA;UAAM2E,SAAS,EAAE,8CAA8C4C,kBAAkB,CAAC3D,KAAK,CAAChC,UAAU,CAAC,EAAG;UAAAgD,QAAA,EACnGhB,KAAK,CAAChC;QAAU;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACPhF,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA,CAACb,MAAM;YAACwF,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ChF,OAAA;YAAM2E,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE,EAAA0C,cAAA,GAAA1D,KAAK,CAACwE,MAAM,cAAAd,cAAA,uBAAZA,cAAA,CAAcW,OAAO,CAAC,CAAC,CAAC,KAAI;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAwD,GAAA,GAtGMnC,SAAS;AAuGf,MAAMU,WAAW,GAAGA,CAAC;EAAExC,OAAO;EAAE+B,QAAQ;EAAEC;AAAW,CAAC,KAAK;EACzD,MAAMgB,kBAAkB,GAAI3F,UAAU,IAAK;IACzC,MAAM4F,MAAM,GAAG;MACb,QAAQ,EAAE,4BAA4B;MACtC,QAAQ,EAAE,8BAA8B;MACxC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAAC5F,UAAU,CAAC,IAAI,0BAA0B;EACzD,CAAC;EAED,oBACE5B,OAAA;IACEsF,OAAO,EAAEA,CAAA,KAAMgB,QAAQ,CAAC/B,OAAO,CAAE;IACjCI,SAAS,EAAE,gGACT4B,UAAU,GAAG,0CAA0C,GAAG,uCAAuC,EAChG;IAAA3B,QAAA,gBAEH5E,OAAA;MAAK2E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD5E,OAAA;QAAI2E,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAEL,OAAO,CAACV;MAAI;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvEhF,OAAA;QAAM2E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClD5E,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C5E,OAAA,CAACnB,QAAQ;UAAC8F,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrChF,OAAA;UAAA4E,QAAA,GAAO,CAACL,OAAO,CAAC1C,QAAQ,GAAG,IAAI,EAAEoG,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACNhF,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C5E,OAAA,CAAC7B,YAAY;UAACwG,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzChF,OAAA;UAAA4E,QAAA,GAAOL,OAAO,CAAC2D,SAAS,EAAC,MAAI;QAAA;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNhF,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C5E,OAAA,CAACjB,QAAQ;UAAC4F,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrChF,OAAA;UAAA4E,QAAA,GAAOL,OAAO,CAACkE,QAAQ,IAAI,GAAG,EAAC,aAAW;QAAA;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACNhF,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C5E,OAAA,CAAC9B,UAAU;UAACyG,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvChF,OAAA;UAAA4E,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5E,OAAA;QAAM2E,SAAS,EAAE,8CAA8C4C,kBAAkB,CAAChD,OAAO,CAAC3C,UAAU,CAAC,EAAG;QAAAgD,QAAA,EACrGL,OAAO,CAAC3C;MAAU;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACPhF,OAAA;QAAQ2E,SAAS,EAAC,sIAAsI;QAAAC,QAAA,EAAC;MAEzJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC0D,GAAA,GAnDI3B,WAAW;AAqDjB,eAAe9G,YAAY;AAAC,IAAAmH,EAAA,EAAAoB,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}