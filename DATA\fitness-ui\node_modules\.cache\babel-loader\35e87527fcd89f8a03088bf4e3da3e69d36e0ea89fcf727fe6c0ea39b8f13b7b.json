{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Routes.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport ModernMap from '../components/ModernMap';\nimport { FiMap, FiNavigation, FiMapPin, FiSearch, FiPlus, FiStar, FiUsers, FiTarget, FiBookmark, FiTrash2, FiPlay, FiClock, FiAlertCircle, FiTrendingUp, FiActivity } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generatePopularRoutes, generatePOIs, generateSegments, calculateDistance, optimizeRouteWithPOIs, DEFAULT_POSITION } from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Routes = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const pos = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        };\n        setUserPosition(pos);\n        loadRoutesAndPOIs(pos);\n      }, error => {\n        console.warn('Géolocalisation échouée:', error);\n        setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n        loadRoutesAndPOIs(DEFAULT_POSITION);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      });\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, [loadRoutesAndPOIs]);\n  const loadRoutesAndPOIs = useCallback(position => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  }, [savedRoutes]);\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) || route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || filters.distance === 'short' && route.distance <= 5 || filters.distance === 'medium' && route.distance > 5 && route.distance <= 15 || filters.distance === 'long' && route.distance > 15;\n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n  const handleSegmentSelect = segment => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n  const handleFiltersChange = newFilters => {\n    setFilters(newFilters);\n  };\n  const handlePOIToggle = poi => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n  const handleMapClick = e => {\n    if (isPlanning) {\n      const newPoint = {\n        lat: e.latlng.lat,\n        lng: e.latlng.lng,\n        elevation: 100 // Valeur par défaut\n      };\n      setPlanningPoints(prev => [...prev, newPoint]);\n    }\n  };\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n  const calculateTotalDistance = points => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n    }\n    return total;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const saveRoute = route => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = {\n        ...route,\n        savedAt: new Date().toISOString()\n      };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n  const unsaveRoute = routeId => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n  const deleteCustomRoute = routeId => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if ((selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n  const startRoute = route => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Cartes et Itin\\xE9raires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez de nouveaux parcours, planifiez vos itin\\xE9raires et explorez les points d'int\\xE9r\\xEAt autour de vous.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-yellow-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: \"Chargement des donn\\xE9es...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('discover'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'discover' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMap, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), \"D\\xE9couvrir\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('segments'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'segments' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), \"Segments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('saved'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'saved' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), \"Sauvegard\\xE9es (\", savedRoutes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('plan'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'plan' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiNavigation, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), \"Planifier\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('pois'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'pois' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), \"Points d'int\\xE9r\\xEAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [activeTab === 'discover' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes populaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPOIs(!showPOIs),\n                className: `px-3 py-1 rounded-md text-sm ${showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                children: \"POIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un parcours...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.type,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    type: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Tous types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"running\",\n                    children: \"Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"cycling\",\n                    children: \"V\\xE9lo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hiking\",\n                    children: \"Randonn\\xE9e\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"walking\",\n                    children: \"Marche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.difficulty,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    difficulty: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Toutes difficult\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.completions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 27\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 25\n                  }, this), savedRoutes.find(r => r.id === route.id) ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => saveRoute(route),\n                    className: \"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\",\n                    title: \"Sauvegarder\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 23\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), activeTab === 'segments' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(AdvancedFilters, {\n              filters: filters,\n              onFiltersChange: handleFiltersChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900\",\n                  children: \"Segments populaires\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowSegments(!showSegments),\n                  className: `px-3 py-1 rounded-md text-sm ${showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                  children: \"Afficher sur la carte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                children: segments.map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleSegmentSelect(segment),\n                  className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs font-medium ${segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' : segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' : segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' : 'bg-purple-100 text-purple-800'}`,\n                      children: segment.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 mb-3\",\n                    children: segment.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 583,\n                        columnNumber: 29\n                      }, this), segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 29\n                      }, this), segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 29\n                      }, this), segment.attempts, \" tentatives\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 29\n                      }, this), \"Record: \", segment.recordTime]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 25\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this), activeTab === 'saved' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes sauvegard\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [savedRoutes.length, \" route\", savedRoutes.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), savedRoutes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mb-2\",\n                children: \"Aucune route sauvegard\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Sauvegardez vos routes pr\\xE9f\\xE9r\\xE9es pour les retrouver facilement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: savedRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 639,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 29\n                    }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\",\n                      children: \"Personnalis\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: route.savedAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Sauvegard\\xE9 le \", new Date(route.savedAt).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 25\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this), activeTab === 'plan' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Planificateur d'itin\\xE9raire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 17\n            }, this), !isPlanning ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startRoutePlanning,\n                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 23\n                }, this), \"Cr\\xE9er un nouveau parcours\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: \"Instructions :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Cr\\xE9er un nouveau parcours\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur la carte pour ajouter des points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Minimum 2 points requis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Terminer\\\" pour sauvegarder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium mb-2\",\n                  children: \"Mode planification actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Points ajout\\xE9s : \", planningPoints.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 23\n                }, this), planningPoints.length >= 2 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Distance : \", calculateTotalDistance(planningPoints).toFixed(1), \" km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: finishPlanning,\n                  disabled: planningPoints.length < 2,\n                  className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                  children: \"Terminer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setIsPlanning(false);\n                    setPlanningPoints([]);\n                  },\n                  className: \"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                  children: \"Annuler\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 15\n          }, this), activeTab === 'pois' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Points d'int\\xE9r\\xEAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 17\n            }, this), selectedRoute && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700 mb-2\",\n                children: \"S\\xE9lectionnez des POIs pour optimiser votre parcours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: optimizeRoute,\n                disabled: selectedPOIs.length === 0,\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                  className: \"inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 23\n                }, this), \"Optimiser l'itin\\xE9raire (\", selectedPOIs.length, \" POIs)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 max-h-96 overflow-y-auto\",\n              children: pois.map(poi => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handlePOIToggle(poi),\n                className: `p-3 border rounded-lg cursor-pointer transition-colors ${selectedPOIs.find(p => p.id === poi.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: poi.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: poi.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: poi.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 798,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: poi.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 799,\n                        columnNumber: 29\n                      }, this), poi.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 text-xs text-green-600\",\n                        children: \"\\u2713 V\\xE9rifi\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 23\n                }, this)\n              }, poi.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(ModernMap, {\n            center: [userPosition.lat, userPosition.lng],\n            zoom: 13,\n            userPosition: userPosition,\n            routes: filteredRoutes,\n            segments: segments,\n            pois: pois,\n            selectedRoute: selectedRoute,\n            selectedSegment: selectedSegment,\n            selectedPOIs: selectedPOIs,\n            optimizedRoute: optimizedRoute,\n            planningPoints: planningPoints,\n            showPOIs: showPOIs,\n            showSegments: showSegments || activeTab === 'segments',\n            onMapClick: handleMapClick,\n            onRouteSelect: handleRouteSelect,\n            onSegmentSelect: handleSegmentSelect,\n            onPOISelect: poi => {\n              if (selectedPOIs.find(p => p.id === poi.id)) {\n                setSelectedPOIs(prev => prev.filter(p => p.id !== poi.id));\n              } else {\n                setSelectedPOIs(prev => [...prev, poi]);\n              }\n            },\n            height: \"h-96 lg:h-[600px]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), selectedSegment && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(SegmentPanel, {\n          segments: segments,\n          selectedSegment: selectedSegment,\n          onSegmentSelect: handleSegmentSelect,\n          userPosition: userPosition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n};\n_s(Routes, \"dSUeV5nDD2fKRb7x+HFn0QRycwI=\", false, function () {\n  return [useAuth];\n});\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "ModernMap", "FiMap", "FiNavigation", "FiMapPin", "FiSearch", "FiPlus", "FiStar", "FiUsers", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiTrash2", "FiPlay", "<PERSON><PERSON><PERSON>", "FiAlertCircle", "FiTrendingUp", "FiActivity", "useAuth", "generatePopularRoutes", "generatePOIs", "generateSegments", "calculateDistance", "optimizeRouteWithPOIs", "DEFAULT_POSITION", "AdvancedFilters", "SegmentPanel", "jsxDEV", "_jsxDEV", "Routes", "_s", "user", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedPOIs", "setSelectedPOIs", "optimizedRoute", "setOptimizedRoute", "isPlanning", "setIsPlanning", "planningPoints", "setPlanningPoints", "filters", "setFilters", "type", "difficulty", "distance", "sport", "surface", "searchTerm", "setSearchTerm", "showPOIs", "setShowPOIs", "activeTab", "setActiveTab", "savedRoutes", "setSavedRoutes", "routeHistory", "setRouteHistory", "segments", "setSegments", "selectedSegment", "setSelectedSegment", "showSegments", "setShowSegments", "isLoading", "setIsLoading", "error", "setError", "saved", "JSON", "parse", "localStorage", "getItem", "history", "savedSegments", "console", "navigator", "geolocation", "getCurrentPosition", "position", "pos", "lat", "coords", "latitude", "lng", "longitude", "loadRoutesAndPOIs", "warn", "enableHighAccuracy", "timeout", "maximumAge", "popularRoutes", "nearbyPOIs", "nearbySegments", "allRoutes", "filteredRoutes", "filter", "route", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesType", "matchesDifficulty", "matchesDistance", "handleRouteSelect", "handleSegmentSelect", "segment", "handleFiltersChange", "newFilters", "handlePOIToggle", "poi", "prev", "isSelected", "find", "p", "id", "optimizeRoute", "length", "startPoint", "points", "endPoint", "optimized", "startRoutePlanning", "handleMapClick", "e", "newPoint", "latlng", "elevation", "finishPlanning", "newRoute", "Date", "now", "calculateTotalDistance", "rating", "completions", "created<PERSON>y", "firstName", "tags", "createdAt", "toISOString", "isCustom", "updatedSavedRoutes", "setItem", "stringify", "total", "i", "getDifficultyColor", "getTypeIcon", "saveRoute", "r", "routeToSave", "savedAt", "unsaveRoute", "routeId", "deleteCustomRoute", "startRoute", "historyEntry", "startedAt", "status", "updatedHistory", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "target", "map", "toFixed", "stopPropagation", "title", "onFiltersChange", "attempts", "recordTime", "toLocaleDateString", "disabled", "icon", "verified", "center", "zoom", "onMapClick", "onRouteSelect", "onSegmentSelect", "onPOISelect", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Routes.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport ModernMap from '../components/ModernMap';\nimport {\n  FiMap,\n  FiNavigation,\n  FiMapPin,\n  FiSearch,\n  FiPlus,\n  FiStar,\n  FiUsers,\n  FiTarget,\n  FiBookmark,\n  FiTrash2,\n  FiPlay,\n  FiClock,\n  FiAlertCircle,\n  FiTrendingUp,\n  FiActivity\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generatePopularRoutes,\n  generatePOIs,\n  generateSegments,\n  calculateDistance,\n  optimizeRouteWithPOIs,\n  DEFAULT_POSITION\n} from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\n\n\n\nconst Routes = () => {\n  const { user } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const pos = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          };\n          setUserPosition(pos);\n          loadRoutesAndPOIs(pos);\n        },\n        (error) => {\n          console.warn('Géolocalisation échouée:', error);\n          setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n          loadRoutesAndPOIs(DEFAULT_POSITION);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 300000 // 5 minutes\n        }\n      );\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, [loadRoutesAndPOIs]);\n\n  const loadRoutesAndPOIs = useCallback((position) => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  }, [savedRoutes]);\n\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || \n                           (filters.distance === 'short' && route.distance <= 5) ||\n                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||\n                           (filters.distance === 'long' && route.distance > 15);\n    \n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n\n  const handleSegmentSelect = (segment) => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n\n  const handleFiltersChange = (newFilters) => {\n    setFilters(newFilters);\n  };\n\n  const handlePOIToggle = (poi) => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n\n  const handleMapClick = (e) => {\n    if (isPlanning) {\n      const newPoint = {\n        lat: e.latlng.lat,\n        lng: e.latlng.lng,\n        elevation: 100 // Valeur par défaut\n      };\n      setPlanningPoints(prev => [...prev, newPoint]);\n    }\n  };\n\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: user?.firstName || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n\n  const calculateTotalDistance = (points) => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(\n        points[i-1].lat, points[i-1].lng,\n        points[i].lat, points[i].lng\n      );\n    }\n    return total;\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const saveRoute = (route) => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = { ...route, savedAt: new Date().toISOString() };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n\n  const unsaveRoute = (routeId) => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n\n  const deleteCustomRoute = (routeId) => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if (selectedRoute?.id === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n\n  const startRoute = (route) => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Cartes et Itinéraires\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez\n            les points d'intérêt autour de vous.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                <p className=\"text-sm text-yellow-800\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Indicateur de chargement */}\n          {isLoading && (\n            <div className=\"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"></div>\n                <p className=\"text-sm text-blue-800\">Chargement des données...</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('discover')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'discover'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMap className=\"inline mr-2\" />\n            Découvrir\n          </button>\n          <button\n            onClick={() => setActiveTab('segments')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'segments'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Segments\n          </button>\n          <button\n            onClick={() => setActiveTab('saved')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'saved'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiBookmark className=\"inline mr-2\" />\n            Sauvegardées ({savedRoutes.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('plan')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'plan'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiNavigation className=\"inline mr-2\" />\n            Planifier\n          </button>\n          <button\n            onClick={() => setActiveTab('pois')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'pois'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMapPin className=\"inline mr-2\" />\n            Points d'intérêt\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {activeTab === 'discover' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes populaires\n                  </h2>\n                  <button\n                    onClick={() => setShowPOIs(!showPOIs)}\n                    className={`px-3 py-1 rounded-md text-sm ${\n                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    POIs\n                  </button>\n                </div>\n\n                {/* Search and Filters */}\n                <div className=\"space-y-4 mb-6\">\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un parcours...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <select\n                      value={filters.type}\n                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Tous types</option>\n                      <option value=\"running\">Course</option>\n                      <option value=\"cycling\">Vélo</option>\n                      <option value=\"hiking\">Randonnée</option>\n                      <option value=\"walking\">Marche</option>\n                    </select>\n\n                    <select\n                      value={filters.difficulty}\n                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Toutes difficultés</option>\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n\n                {/* Routes List */}\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {filteredRoutes.map(route => (\n                    <div\n                      key={route.id}\n                      onClick={() => handleRouteSelect(route)}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedRoute?.id === route.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                          <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                        </div>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                          {route.difficulty}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 mb-2\">\n                        {route.description}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                        <span>{route.distance.toFixed(1)} km</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span>{route.rating.toFixed(1)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <FiUsers className=\"h-3 w-3 mr-1\" />\n                            <span>{route.completions}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Boutons d'action */}\n                      <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                        <button\n                          onClick={() => startRoute(route)}\n                          className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                        >\n                          <FiPlay className=\"h-3 w-3 mr-1\" />\n                          Démarrer\n                        </button>\n\n                        {savedRoutes.find(r => r.id === route.id) ? (\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => saveRoute(route)}\n                            className=\"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\"\n                            title=\"Sauvegarder\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        )}\n\n                        {route.isCustom && (\n                          <button\n                            onClick={() => deleteCustomRoute(route.id)}\n                            className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                            title=\"Supprimer\"\n                          >\n                            <FiTrash2 className=\"h-3 w-3\" />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'segments' && (\n              <div className=\"space-y-6\">\n                {/* Filtres avancés */}\n                <AdvancedFilters\n                  filters={filters}\n                  onFiltersChange={handleFiltersChange}\n                />\n\n                {/* Liste des segments */}\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Segments populaires\n                    </h2>\n                    <button\n                      onClick={() => setShowSegments(!showSegments)}\n                      className={`px-3 py-1 rounded-md text-sm ${\n                        showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      Afficher sur la carte\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {segments.map(segment => (\n                      <div\n                        key={segment.id}\n                        onClick={() => handleSegmentSelect(segment)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedSegment?.id === segment.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' :\n                            segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' :\n                            segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' :\n                            'bg-purple-100 text-purple-800'\n                          }`}>\n                            {segment.difficulty}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-600 mb-3\">{segment.description}</p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <FiMapPin className=\"h-4 w-4 mr-1\" />\n                            {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiTrendingUp className=\"h-4 w-4 mr-1\" />\n                            {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiUsers className=\"h-4 w-4 mr-1\" />\n                            {segment.attempts} tentatives\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiClock className=\"h-4 w-4 mr-1\" />\n                            Record: {segment.recordTime}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'saved' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes sauvegardées\n                  </h2>\n                  <span className=\"text-sm text-gray-500\">\n                    {savedRoutes.length} route{savedRoutes.length !== 1 ? 's' : ''}\n                  </span>\n                </div>\n\n                {savedRoutes.length === 0 ? (\n                  <div className=\"text-center py-8\">\n                    <FiBookmark className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                    <p className=\"text-gray-500 mb-2\">Aucune route sauvegardée</p>\n                    <p className=\"text-sm text-gray-400\">\n                      Sauvegardez vos routes préférées pour les retrouver facilement\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {savedRoutes.map(route => (\n                      <div\n                        key={route.id}\n                        onClick={() => handleRouteSelect(route)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedRoute?.id === route.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                            <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                            {route.isCustom && (\n                              <span className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\">\n                                Personnalisé\n                              </span>\n                            )}\n                          </div>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                            {route.difficulty}\n                          </span>\n                        </div>\n\n                        <div className=\"text-sm text-gray-600 mb-2\">\n                          {route.description}\n                        </div>\n\n                        <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                          <span>{route.distance.toFixed(1)} km</span>\n                          <div className=\"flex items-center space-x-2\">\n                            {route.savedAt && (\n                              <span>Sauvegardé le {new Date(route.savedAt).toLocaleDateString()}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Boutons d'action */}\n                        <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                          <button\n                            onClick={() => startRoute(route)}\n                            className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                          >\n                            <FiPlay className=\"h-3 w-3 mr-1\" />\n                            Démarrer\n                          </button>\n\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n\n                          {route.isCustom && (\n                            <button\n                              onClick={() => deleteCustomRoute(route.id)}\n                              className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                              title=\"Supprimer\"\n                            >\n                              <FiTrash2 className=\"h-3 w-3\" />\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'plan' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Planificateur d'itinéraire\n                </h2>\n                \n                {!isPlanning ? (\n                  <div className=\"space-y-4\">\n                    <button\n                      onClick={startRoutePlanning}\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n                    >\n                      <FiPlus className=\"mr-2\" />\n                      Créer un nouveau parcours\n                    </button>\n                    \n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"mb-2\">Instructions :</p>\n                      <ul className=\"list-disc list-inside space-y-1\">\n                        <li>Cliquez sur \"Créer un nouveau parcours\"</li>\n                        <li>Cliquez sur la carte pour ajouter des points</li>\n                        <li>Minimum 2 points requis</li>\n                        <li>Cliquez sur \"Terminer\" pour sauvegarder</li>\n                      </ul>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">Mode planification actif</p>\n                      <p>Points ajoutés : {planningPoints.length}</p>\n                      {planningPoints.length >= 2 && (\n                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={finishPlanning}\n                        disabled={planningPoints.length < 2}\n                        className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Terminer\n                      </button>\n                      <button\n                        onClick={() => {\n                          setIsPlanning(false);\n                          setPlanningPoints([]);\n                        }}\n                        className=\"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\"\n                      >\n                        Annuler\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'pois' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Points d'intérêt\n                </h2>\n                \n                {selectedRoute && (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <p className=\"text-sm text-blue-700 mb-2\">\n                      Sélectionnez des POIs pour optimiser votre parcours\n                    </p>\n                    <button\n                      onClick={optimizeRoute}\n                      disabled={selectedPOIs.length === 0}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\"\n                    >\n                      <FiTarget className=\"inline mr-2\" />\n                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)\n                    </button>\n                  </div>\n                )}\n                \n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n                  {pois.map(poi => (\n                    <div\n                      key={poi.id}\n                      onClick={() => handlePOIToggle(poi)}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedPOIs.find(p => p.id === poi.id)\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{poi.icon}</span>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{poi.name}</h4>\n                          <p className=\"text-xs text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-xs text-gray-500\">{poi.rating.toFixed(1)}</span>\n                            {poi.verified && (\n                              <span className=\"ml-2 text-xs text-green-600\">✓ Vérifié</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Modern Map */}\n          <div className=\"lg:col-span-2\">\n            <ModernMap\n              center={[userPosition.lat, userPosition.lng]}\n              zoom={13}\n              userPosition={userPosition}\n              routes={filteredRoutes}\n              segments={segments}\n              pois={pois}\n              selectedRoute={selectedRoute}\n              selectedSegment={selectedSegment}\n              selectedPOIs={selectedPOIs}\n              optimizedRoute={optimizedRoute}\n              planningPoints={planningPoints}\n              showPOIs={showPOIs}\n              showSegments={showSegments || activeTab === 'segments'}\n              onMapClick={handleMapClick}\n              onRouteSelect={handleRouteSelect}\n              onSegmentSelect={handleSegmentSelect}\n              onPOISelect={(poi) => {\n                if (selectedPOIs.find(p => p.id === poi.id)) {\n                  setSelectedPOIs(prev => prev.filter(p => p.id !== poi.id));\n                } else {\n                  setSelectedPOIs(prev => [...prev, poi]);\n                }\n              }}\n              height=\"h-96 lg:h-[600px]\"\n            />\n          </div>\n        </div>\n\n        {/* Panneau de segment sélectionné */}\n        {selectedSegment && (\n          <div className=\"mt-8\">\n            <SegmentPanel\n              segments={segments}\n              selectedSegment={selectedSegment}\n              onSegmentSelect={handleSegmentSelect}\n              userPosition={userPosition}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SACEC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,EACrBC,gBAAgB,QACX,mBAAmB;AAC1B,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAItD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAACyB,gBAAgB,CAAC;EAClE,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqC,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrCmD,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAM2E,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACrE,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MACxE,MAAME,aAAa,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;MAC1EjB,cAAc,CAACa,KAAK,CAAC;MACrBX,eAAe,CAACgB,OAAO,CAAC;MACxBd,WAAW,CAACe,aAAa,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAENzE,SAAS,CAAC,MAAM;IACdwE,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIS,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAMC,GAAG,GAAG;UACVC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG;QACvB,CAAC;QACD3D,eAAe,CAACsD,GAAG,CAAC;QACpBM,iBAAiB,CAACN,GAAG,CAAC;MACxB,CAAC,EACAd,KAAK,IAAK;QACTS,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAErB,KAAK,CAAC;QAC/CC,QAAQ,CAAC,8EAA8E,CAAC;QACxFmB,iBAAiB,CAACrE,gBAAgB,CAAC;MACrC,CAAC,EACD;QACEuE,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,MAAM,CAAC;MACrB,CACF,CAAC;IACH,CAAC,MAAM;MACLvB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DmB,iBAAiB,CAACrE,gBAAgB,CAAC;IACrC;EACF,CAAC,EAAE,CAACqE,iBAAiB,CAAC,CAAC;EAEvB,MAAMA,iBAAiB,GAAG5F,WAAW,CAAEqF,QAAQ,IAAK;IAClD,IAAI;MACF,MAAMY,aAAa,GAAG/E,qBAAqB,CAACmE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC3E,MAAMQ,UAAU,GAAG/E,YAAY,CAACkE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC/D,MAAMS,cAAc,GAAG/E,gBAAgB,CAACiE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,CAAC,CAAC;;MAEtE;MACA,MAAMU,SAAS,GAAG,CAAC,GAAGxC,WAAW,EAAE,GAAGqC,aAAa,CAAC;MACpD/D,SAAS,CAACkE,SAAS,CAAC;MACpBhE,OAAO,CAAC8D,UAAU,CAAC;MACnBjC,WAAW,CAACkC,cAAc,CAAC;MAC3B5B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;EAEjB,MAAMyC,cAAc,GAAGpE,MAAM,CAACqE,MAAM,CAACC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGD,KAAK,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC5DH,KAAK,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,WAAW,GAAG9D,OAAO,CAACE,IAAI,KAAK,KAAK,IAAIsD,KAAK,CAACtD,IAAI,KAAKF,OAAO,CAACE,IAAI;IACzE,MAAM6D,iBAAiB,GAAG/D,OAAO,CAACG,UAAU,KAAK,KAAK,IAAIqD,KAAK,CAACrD,UAAU,KAAKH,OAAO,CAACG,UAAU;IACjG,MAAM6D,eAAe,GAAGhE,OAAO,CAACI,QAAQ,KAAK,KAAK,IAC1BJ,OAAO,CAACI,QAAQ,KAAK,OAAO,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,CAAE,IACpDJ,OAAO,CAACI,QAAQ,KAAK,QAAQ,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,CAAC,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,EAAG,IAC5EJ,OAAO,CAACI,QAAQ,KAAK,MAAM,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,EAAG;IAE3E,OAAOqD,aAAa,IAAIK,WAAW,IAAIC,iBAAiB,IAAIC,eAAe;EAC7E,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnCjE,gBAAgB,CAACiE,KAAK,CAAC;IACvB7D,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;IACnB2B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8C,mBAAmB,GAAIC,OAAO,IAAK;IACvC/C,kBAAkB,CAAC+C,OAAO,CAAC;IAC3B5E,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM2E,mBAAmB,GAAIC,UAAU,IAAK;IAC1CpE,UAAU,CAACoE,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,eAAe,GAAIC,GAAG,IAAK;IAC/B9E,eAAe,CAAC+E,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAClD,IAAIH,UAAU,EAAE;QACd,OAAOD,IAAI,CAACjB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGJ,IAAI,EAAED,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIvF,aAAa,IAAIE,YAAY,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMC,UAAU,GAAGzF,aAAa,CAAC0F,MAAM,CAAC,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG3F,aAAa,CAAC0F,MAAM,CAAC1F,aAAa,CAAC0F,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMI,SAAS,GAAG3G,qBAAqB,CAACwG,UAAU,EAAEE,QAAQ,EAAEzF,YAAY,CAAC;MAC3EG,iBAAiB,CAACuF,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtF,aAAa,CAAC,IAAI,CAAC;IACnBE,iBAAiB,CAAC,EAAE,CAAC;IACrBR,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyF,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIzF,UAAU,EAAE;MACd,MAAM0F,QAAQ,GAAG;QACf9C,GAAG,EAAE6C,CAAC,CAACE,MAAM,CAAC/C,GAAG;QACjBG,GAAG,EAAE0C,CAAC,CAACE,MAAM,CAAC5C,GAAG;QACjB6C,SAAS,EAAE,GAAG,CAAC;MACjB,CAAC;MACDzF,iBAAiB,CAACyE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEc,QAAQ,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3F,cAAc,CAACgF,MAAM,IAAI,CAAC,EAAE;MAC9B,MAAMY,QAAQ,GAAG;QACfd,EAAE,EAAE,UAAUe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1BlC,IAAI,EAAE,2BAA2B;QACjCxD,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAEyF,sBAAsB,CAAC/F,cAAc,CAAC;QAChDkF,MAAM,EAAElF,cAAc;QACtBK,UAAU,EAAE,QAAQ;QACpB2F,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkH,SAAS,KAAI,KAAK;QACnCC,IAAI,EAAE,CAAC,QAAQ,CAAC;QAChBrC,WAAW,EAAE,kCAAkC;QAC/CsC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,kBAAkB,GAAG,CAACZ,QAAQ,EAAE,GAAG7E,WAAW,CAAC;MACrDC,cAAc,CAACwF,kBAAkB,CAAC;MAClCxE,YAAY,CAACyE,OAAO,CAAC,aAAa,EAAE3E,IAAI,CAAC4E,SAAS,CAACF,kBAAkB,CAAC,CAAC;MAEvEnH,SAAS,CAACqF,IAAI,IAAI,CAACkB,QAAQ,EAAE,GAAGlB,IAAI,CAAC,CAAC;MACtCjF,gBAAgB,CAACmG,QAAQ,CAAC;MAC1B7F,aAAa,CAAC,KAAK,CAAC;MACpBE,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAM8F,sBAAsB,GAAIb,MAAM,IAAK;IACzC,IAAIyB,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,CAACF,MAAM,EAAE4B,CAAC,EAAE,EAAE;MACtCD,KAAK,IAAInI,iBAAiB,CACxB0G,MAAM,CAAC0B,CAAC,GAAC,CAAC,CAAC,CAAClE,GAAG,EAAEwC,MAAM,CAAC0B,CAAC,GAAC,CAAC,CAAC,CAAC/D,GAAG,EAChCqC,MAAM,CAAC0B,CAAC,CAAC,CAAClE,GAAG,EAAEwC,MAAM,CAAC0B,CAAC,CAAC,CAAC/D,GAC3B,CAAC;IACH;IACA,OAAO8D,KAAK;EACd,CAAC;EAED,MAAME,kBAAkB,GAAIxG,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMyG,WAAW,GAAI1G,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAM2G,SAAS,GAAIrD,KAAK,IAAK;IAC3B,IAAI,CAAC3C,WAAW,CAAC6D,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,EAAE;MAC7C,MAAMmC,WAAW,GAAG;QAAE,GAAGvD,KAAK;QAAEwD,OAAO,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MAAE,CAAC;MACnE,MAAME,kBAAkB,GAAG,CAACS,WAAW,EAAE,GAAGlG,WAAW,CAAC;MACxDC,cAAc,CAACwF,kBAAkB,CAAC;MAClCxE,YAAY,CAACyE,OAAO,CAAC,aAAa,EAAE3E,IAAI,CAAC4E,SAAS,CAACF,kBAAkB,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMW,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMZ,kBAAkB,GAAGzF,WAAW,CAAC0C,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKsC,OAAO,CAAC;IACpEpG,cAAc,CAACwF,kBAAkB,CAAC;IAClCxE,YAAY,CAACyE,OAAO,CAAC,aAAa,EAAE3E,IAAI,CAAC4E,SAAS,CAACF,kBAAkB,CAAC,CAAC;EACzE,CAAC;EAED,MAAMa,iBAAiB,GAAID,OAAO,IAAK;IACrC;IACA,MAAMZ,kBAAkB,GAAGzF,WAAW,CAAC0C,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKsC,OAAO,CAAC;IACpEpG,cAAc,CAACwF,kBAAkB,CAAC;IAClCxE,YAAY,CAACyE,OAAO,CAAC,aAAa,EAAE3E,IAAI,CAAC4E,SAAS,CAACF,kBAAkB,CAAC,CAAC;;IAEvE;IACAnH,SAAS,CAACqF,IAAI,IAAIA,IAAI,CAACjB,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKsC,OAAO,CAAC,CAAC;;IAErD;IACA,IAAI,CAAA5H,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKsC,OAAO,EAAE;MACjC3H,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM6H,UAAU,GAAI5D,KAAK,IAAK;IAC5B,MAAM6D,YAAY,GAAG;MACnBzC,EAAE,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC;MACdpC,KAAK,EAAEA,KAAK;MACZ8D,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCmB,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,cAAc,GAAG,CAACH,YAAY,EAAE,GAAGtG,YAAY,CAAC;IACtDC,eAAe,CAACwG,cAAc,CAAC;IAC/B1F,YAAY,CAACyE,OAAO,CAAC,cAAc,EAAE3E,IAAI,CAAC4E,SAAS,CAACgB,cAAc,CAAC,CAAC;EACtE,CAAC;EAED,oBACE5I,OAAA;IAAK6I,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC9I,OAAA;MAAK6I,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9I,OAAA;QAAK6I,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9I,OAAA;UAAI6I,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlJ,OAAA;UAAG6I,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHrG,KAAK,iBACJ7C,OAAA;UAAK6I,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzF9I,OAAA;YAAK6I,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9I,OAAA,CAACb,aAAa;cAAC0J,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlJ,OAAA;cAAG6I,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEjG;YAAK;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAvG,SAAS,iBACR3C,OAAA;UAAK6I,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrF9I,OAAA;YAAK6I,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C9I,OAAA;cAAK6I,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzFlJ,OAAA;cAAG6I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlJ,OAAA;QAAK6I,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D9I,OAAA;UACEmJ,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,UAAU,CAAE;UACxC6G,SAAS,EAAE,qEACT9G,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA+G,QAAA,gBAEH9I,OAAA,CAACzB,KAAK;YAACsK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlJ,OAAA;UACEmJ,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,UAAU,CAAE;UACxC6G,SAAS,EAAE,qEACT9G,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA+G,QAAA,gBAEH9I,OAAA,CAAClB,QAAQ;YAAC+J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlJ,OAAA;UACEmJ,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,OAAO,CAAE;UACrC6G,SAAS,EAAE,qEACT9G,SAAS,KAAK,OAAO,GACjB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA+G,QAAA,gBAEH9I,OAAA,CAACjB,UAAU;YAAC8J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACxB,EAACjH,WAAW,CAACiE,MAAM,EAAC,GACpC;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlJ,OAAA;UACEmJ,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,MAAM,CAAE;UACpC6G,SAAS,EAAE,qEACT9G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA+G,QAAA,gBAEH9I,OAAA,CAACxB,YAAY;YAACqK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlJ,OAAA;UACEmJ,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,MAAM,CAAE;UACpC6G,SAAS,EAAE,qEACT9G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA+G,QAAA,gBAEH9I,OAAA,CAACvB,QAAQ;YAACoK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlJ,OAAA;QAAK6I,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD9I,OAAA;UAAK6I,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3B/G,SAAS,KAAK,UAAU,iBACvB/B,OAAA;YAAK6I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9I,OAAA;cAAK6I,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9I,OAAA;gBAAI6I,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlJ,OAAA;gBACEmJ,OAAO,EAAEA,CAAA,KAAMrH,WAAW,CAAC,CAACD,QAAQ,CAAE;gBACtCgH,SAAS,EAAE,gCACThH,QAAQ,GAAG,2BAA2B,GAAG,2BAA2B,EACnE;gBAAAiH,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNlJ,OAAA;cAAK6I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9I,OAAA;gBAAK6I,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9I,OAAA,CAACtB,QAAQ;kBAACmK,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpElJ,OAAA;kBACEsB,IAAI,EAAC,MAAM;kBACX8H,WAAW,EAAC,2BAA2B;kBACvCC,KAAK,EAAE1H,UAAW;kBAClB2H,QAAQ,EAAG7C,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAA8G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlJ,OAAA;gBAAK6I,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9I,OAAA;kBACEqJ,KAAK,EAAEjI,OAAO,CAACE,IAAK;kBACpBgI,QAAQ,EAAG7C,CAAC,IAAKpF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtE,IAAI,EAAEmF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACzER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH9I,OAAA;oBAAQqJ,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvClJ,OAAA;oBAAQqJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvClJ,OAAA;oBAAQqJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrClJ,OAAA;oBAAQqJ,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzClJ,OAAA;oBAAQqJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAETlJ,OAAA;kBACEqJ,KAAK,EAAEjI,OAAO,CAACG,UAAW;kBAC1B+H,QAAQ,EAAG7C,CAAC,IAAKpF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErE,UAAU,EAAEkF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/ER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH9I,OAAA;oBAAQqJ,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/ClJ,OAAA;oBAAQqJ,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtClJ,OAAA;oBAAQqJ,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtClJ,OAAA;oBAAQqJ,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlJ,OAAA;cAAK6I,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDpE,cAAc,CAAC8E,GAAG,CAAC5E,KAAK,iBACvB5E,OAAA;gBAEEmJ,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAACT,KAAK,CAAE;gBACxCiE,SAAS,EAAE,0DACT,CAAAnI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA8C,QAAA,gBAEH9I,OAAA;kBAAK6I,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD9I,OAAA;oBAAK6I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C9I,OAAA;sBAAM6I,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACpD,KAAK,CAACtD,IAAI;oBAAC;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DlJ,OAAA;sBAAI6I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElE,KAAK,CAACE;oBAAI;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACNlJ,OAAA;oBAAM6I,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACnD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAuH,QAAA,EACnGlE,KAAK,CAACrD;kBAAU;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENlJ,OAAA;kBAAK6I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxClE,KAAK,CAACK;gBAAW;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENlJ,OAAA;kBAAK6I,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3E9I,OAAA;oBAAA8I,QAAA,GAAOlE,KAAK,CAACpD,QAAQ,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3ClJ,OAAA;oBAAK6I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C9I,OAAA;sBAAK6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC9I,OAAA,CAACpB,MAAM;wBAACiK,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDlJ,OAAA;wBAAA8I,QAAA,EAAOlE,KAAK,CAACsC,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNlJ,OAAA;sBAAK6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC9I,OAAA,CAACnB,OAAO;wBAACgK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpClJ,OAAA;wBAAA8I,QAAA,EAAOlE,KAAK,CAACuC;sBAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlJ,OAAA;kBAAK6I,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/E9I,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC5D,KAAK,CAAE;oBACjCiE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I9I,OAAA,CAACf,MAAM;sBAAC4J,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERjH,WAAW,CAAC6D,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,gBACvChG,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACzD,KAAK,CAACoB,EAAE,CAAE;oBACrC6C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3B9I,OAAA,CAACjB,UAAU;sBAAC8J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,gBAETlJ,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMlB,SAAS,CAACrD,KAAK,CAAE;oBAChCiE,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,aAAa;oBAAAb,QAAA,eAEnB9I,OAAA,CAACjB,UAAU;sBAAC8J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACT,EAEAtE,KAAK,CAAC6C,QAAQ,iBACbzH,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC3D,KAAK,CAACoB,EAAE,CAAE;oBAC3C6C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjB9I,OAAA,CAAChB,QAAQ;sBAAC6J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzEDtE,KAAK,CAACoB,EAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0EV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAnH,SAAS,KAAK,UAAU,iBACvB/B,OAAA;YAAK6I,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB9I,OAAA,CAACH,eAAe;cACduB,OAAO,EAAEA,OAAQ;cACjBwI,eAAe,EAAEpE;YAAoB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAGFlJ,OAAA;cAAK6I,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD9I,OAAA;gBAAK6I,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD9I,OAAA;kBAAI6I,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAEpD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlJ,OAAA;kBACEmJ,OAAO,EAAEA,CAAA,KAAMzG,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CoG,SAAS,EAAE,gCACTpG,YAAY,GAAG,2BAA2B,GAAG,2BAA2B,EACvE;kBAAAqG,QAAA,EACJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlJ,OAAA;gBAAK6I,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChDzG,QAAQ,CAACmH,GAAG,CAACjE,OAAO,iBACnBvF,OAAA;kBAEEmJ,OAAO,EAAEA,CAAA,KAAM7D,mBAAmB,CAACC,OAAO,CAAE;kBAC5CsD,SAAS,EAAE,0DACT,CAAAtG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAC9B,4BAA4B,GAC5B,uCAAuC,EAC1C;kBAAA8C,QAAA,gBAEH9I,OAAA;oBAAK6I,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD9I,OAAA;sBAAI6I,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAEvD,OAAO,CAACT;oBAAI;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/DlJ,OAAA;sBAAM6I,SAAS,EAAE,8CACftD,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,6BAA6B,GAC/DgE,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,+BAA+B,GACjEgE,OAAO,CAAChE,UAAU,KAAK,WAAW,GAAG,yBAAyB,GAC9D,+BAA+B,EAC9B;sBAAAuH,QAAA,EACAvD,OAAO,CAAChE;oBAAU;sBAAAwH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlJ,OAAA;oBAAG6I,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEvD,OAAO,CAACN;kBAAW;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnElJ,OAAA;oBAAK6I,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChE9I,OAAA;sBAAM6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC9I,OAAA,CAACvB,QAAQ;wBAACoK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpC3D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,GAAG,GAAG+D,OAAO,CAAC/D,QAAQ,GAAG,GAAG,GAAG,CAAC+D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,EAAEiI,OAAO,CAAC,CAAC,CAAC,IAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACPlJ,OAAA;sBAAM6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC9I,OAAA,CAACZ,YAAY;wBAACyJ,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACxC3D,OAAO,CAACqB,SAAS,GAAG,CAAC,GAAG,IAAIrB,OAAO,CAACqB,SAAS,GAAG,GAAG,GAAGrB,OAAO,CAACqB,SAAS,GAAG;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACPlJ,OAAA;sBAAM6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC9I,OAAA,CAACnB,OAAO;wBAACgK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACnC3D,OAAO,CAACsE,QAAQ,EAAC,aACpB;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPlJ,OAAA;sBAAM6I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC9I,OAAA,CAACd,OAAO;wBAAC2J,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAC5B,EAAC3D,OAAO,CAACuE,UAAU;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArCD3D,OAAO,CAACS,EAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAnH,SAAS,KAAK,OAAO,iBACpB/B,OAAA;YAAK6I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9I,OAAA;cAAK6I,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9I,OAAA;gBAAI6I,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlJ,OAAA;gBAAM6I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpC7G,WAAW,CAACiE,MAAM,EAAC,QAAM,EAACjE,WAAW,CAACiE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELjH,WAAW,CAACiE,MAAM,KAAK,CAAC,gBACvBlG,OAAA;cAAK6I,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9I,OAAA,CAACjB,UAAU;gBAAC8J,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DlJ,OAAA;gBAAG6I,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9DlJ,OAAA;gBAAG6I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAENlJ,OAAA;cAAK6I,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD7G,WAAW,CAACuH,GAAG,CAAC5E,KAAK,iBACpB5E,OAAA;gBAEEmJ,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAACT,KAAK,CAAE;gBACxCiE,SAAS,EAAE,0DACT,CAAAnI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA8C,QAAA,gBAEH9I,OAAA;kBAAK6I,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD9I,OAAA;oBAAK6I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C9I,OAAA;sBAAM6I,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACpD,KAAK,CAACtD,IAAI;oBAAC;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DlJ,OAAA;sBAAI6I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElE,KAAK,CAACE;oBAAI;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAClEtE,KAAK,CAAC6C,QAAQ,iBACbzH,OAAA;sBAAM6I,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,EAAC;oBAE/E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlJ,OAAA;oBAAM6I,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACnD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAuH,QAAA,EACnGlE,KAAK,CAACrD;kBAAU;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENlJ,OAAA;kBAAK6I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxClE,KAAK,CAACK;gBAAW;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENlJ,OAAA;kBAAK6I,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3E9I,OAAA;oBAAA8I,QAAA,GAAOlE,KAAK,CAACpD,QAAQ,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3ClJ,OAAA;oBAAK6I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EACzClE,KAAK,CAACwD,OAAO,iBACZpI,OAAA;sBAAA8I,QAAA,GAAM,mBAAc,EAAC,IAAI/B,IAAI,CAACnC,KAAK,CAACwD,OAAO,CAAC,CAAC2B,kBAAkB,CAAC,CAAC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBACzE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlJ,OAAA;kBAAK6I,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/E9I,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC5D,KAAK,CAAE;oBACjCiE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I9I,OAAA,CAACf,MAAM;sBAAC4J,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETlJ,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACzD,KAAK,CAACoB,EAAE,CAAE;oBACrC6C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3B9I,OAAA,CAACjB,UAAU;sBAAC8J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,EAERtE,KAAK,CAAC6C,QAAQ,iBACbzH,OAAA;oBACEmJ,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC3D,KAAK,CAACoB,EAAE,CAAE;oBAC3C6C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjB9I,OAAA,CAAChB,QAAQ;sBAAC6J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/DDtE,KAAK,CAACoB,EAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEAnH,SAAS,KAAK,MAAM,iBACnB/B,OAAA;YAAK6I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9I,OAAA;cAAI6I,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ,CAAClI,UAAU,gBACVhB,OAAA;cAAK6I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9I,OAAA;gBACEmJ,OAAO,EAAE5C,kBAAmB;gBAC5BsC,SAAS,EAAC,yHAAyH;gBAAAC,QAAA,gBAEnI9I,OAAA,CAACrB,MAAM;kBAACkK,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETlJ,OAAA;gBAAK6I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC9I,OAAA;kBAAG6I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtClJ,OAAA;kBAAI6I,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7C9I,OAAA;oBAAA8I,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChDlJ,OAAA;oBAAA8I,QAAA,EAAI;kBAA4C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrDlJ,OAAA;oBAAA8I,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChClJ,OAAA;oBAAA8I,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENlJ,OAAA;cAAK6I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9I,OAAA;gBAAK6I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC9I,OAAA;kBAAG6I,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DlJ,OAAA;kBAAA8I,QAAA,GAAG,sBAAiB,EAAC5H,cAAc,CAACgF,MAAM;gBAAA;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9ChI,cAAc,CAACgF,MAAM,IAAI,CAAC,iBACzBlG,OAAA;kBAAA8I,QAAA,GAAG,aAAW,EAAC7B,sBAAsB,CAAC/F,cAAc,CAAC,CAACuI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlJ,OAAA;gBAAK6I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B9I,OAAA;kBACEmJ,OAAO,EAAEtC,cAAe;kBACxBmD,QAAQ,EAAE9I,cAAc,CAACgF,MAAM,GAAG,CAAE;kBACpC2C,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EACtJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlJ,OAAA;kBACEmJ,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,KAAK,CAAC;oBACpBE,iBAAiB,CAAC,EAAE,CAAC;kBACvB,CAAE;kBACF0H,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,EACnG;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEAnH,SAAS,KAAK,MAAM,iBACnB/B,OAAA;YAAK6I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9I,OAAA;cAAI6I,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJxI,aAAa,iBACZV,OAAA;cAAK6I,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C9I,OAAA;gBAAG6I,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlJ,OAAA;gBACEmJ,OAAO,EAAElD,aAAc;gBACvB+D,QAAQ,EAAEpJ,YAAY,CAACsF,MAAM,KAAK,CAAE;gBACpC2C,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,gBAE3J9I,OAAA,CAAClB,QAAQ;kBAAC+J,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BACZ,EAACtI,YAAY,CAACsF,MAAM,EAAC,QAC/C;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAEDlJ,OAAA;cAAK6I,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDtI,IAAI,CAACgJ,GAAG,CAAC7D,GAAG,iBACX3F,OAAA;gBAEEmJ,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACC,GAAG,CAAE;gBACpCkD,SAAS,EAAE,0DACTjI,YAAY,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,GACnC,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA8C,QAAA,eAEH9I,OAAA;kBAAK6I,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9I,OAAA;oBAAM6I,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEnD,GAAG,CAACsE;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3ClJ,OAAA;oBAAK6I,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB9I,OAAA;sBAAI6I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnD,GAAG,CAACb;oBAAI;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjElJ,OAAA;sBAAG6I,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEnD,GAAG,CAACV;oBAAW;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1DlJ,OAAA;sBAAK6I,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrC9I,OAAA,CAACpB,MAAM;wBAACiK,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDlJ,OAAA;wBAAM6I,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEnD,GAAG,CAACuB,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACrEvD,GAAG,CAACuE,QAAQ,iBACXlK,OAAA;wBAAM6I,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC9D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArBDvD,GAAG,CAACK,EAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlJ,OAAA;UAAK6I,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9I,OAAA,CAAC1B,SAAS;YACR6L,MAAM,EAAE,CAAC/J,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;YAC7CqG,IAAI,EAAE,EAAG;YACThK,YAAY,EAAEA,YAAa;YAC3BE,MAAM,EAAEoE,cAAe;YACvBrC,QAAQ,EAAEA,QAAS;YACnB7B,IAAI,EAAEA,IAAK;YACXE,aAAa,EAAEA,aAAc;YAC7B6B,eAAe,EAAEA,eAAgB;YACjC3B,YAAY,EAAEA,YAAa;YAC3BE,cAAc,EAAEA,cAAe;YAC/BI,cAAc,EAAEA,cAAe;YAC/BW,QAAQ,EAAEA,QAAS;YACnBY,YAAY,EAAEA,YAAY,IAAIV,SAAS,KAAK,UAAW;YACvDsI,UAAU,EAAE7D,cAAe;YAC3B8D,aAAa,EAAEjF,iBAAkB;YACjCkF,eAAe,EAAEjF,mBAAoB;YACrCkF,WAAW,EAAG7E,GAAG,IAAK;cACpB,IAAI/E,YAAY,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,EAAE;gBAC3CnF,eAAe,CAAC+E,IAAI,IAAIA,IAAI,CAACjB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,CAAC;cAC5D,CAAC,MAAM;gBACLnF,eAAe,CAAC+E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC;cACzC;YACF,CAAE;YACF8E,MAAM,EAAC;UAAmB;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3G,eAAe,iBACdvC,OAAA;QAAK6I,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9I,OAAA,CAACF,YAAY;UACXuC,QAAQ,EAAEA,QAAS;UACnBE,eAAe,EAAEA,eAAgB;UACjCgI,eAAe,EAAEjF,mBAAoB;UACrClF,YAAY,EAAEA;QAAa;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChJ,EAAA,CAxzBID,MAAM;EAAA,QACOX,OAAO;AAAA;AAAAoL,EAAA,GADpBzK,MAAM;AA0zBZ,eAAeA,MAAM;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}