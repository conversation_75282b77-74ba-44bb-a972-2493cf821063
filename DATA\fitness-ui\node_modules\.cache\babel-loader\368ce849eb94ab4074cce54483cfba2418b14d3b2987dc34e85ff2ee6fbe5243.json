{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\App.js\";\nimport React from 'react'; // eslint-disable-line no-unused-vars\nimport { Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport Auth from './pages/Auth';\nimport Dashboard from './pages/Dashboard';\nimport Timer from './pages/Timer';\nimport Programs from './pages/Programs';\nimport Progress from './pages/Progress';\nimport Performance from './pages/Performance';\nimport RoutesPage from './pages/Routes';\nimport RoutesModern from './pages/RoutesModern';\nimport Social from './pages/Social';\nimport Exercises from './pages/Exercises';\nimport Features from './pages/Features';\nimport About from './pages/About';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-grow\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/auth\",\n            element: /*#__PURE__*/_jsxDEV(Auth, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/features\",\n            element: /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/about\",\n            element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/timer\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Timer, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/programs\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Programs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/progress\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Progress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/performance\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Performance, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/routes\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(RoutesModern, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/routes-old\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(RoutesPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/social\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Social, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/exercises\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Exercises, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Footer", "ProtectedRoute", "Home", "<PERSON><PERSON>", "Dashboard", "Timer", "Programs", "Progress", "Performance", "RoutesPage", "RoutesModern", "Social", "Exercises", "Features", "About", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/App.js"], "sourcesContent": ["import React from 'react'; // eslint-disable-line no-unused-vars\r\nimport { Routes, Route } from 'react-router-dom';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\nimport Navbar from './components/Navbar';\r\nimport Footer from './components/Footer';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport Home from './pages/Home';\r\nimport Auth from './pages/Auth';\r\nimport Dashboard from './pages/Dashboard';\r\nimport Timer from './pages/Timer';\r\nimport Programs from './pages/Programs';\r\nimport Progress from './pages/Progress';\r\nimport Performance from './pages/Performance';\r\nimport RoutesPage from './pages/Routes';\r\nimport RoutesModern from './pages/RoutesModern';\r\nimport Social from './pages/Social';\r\nimport Exercises from './pages/Exercises';\r\nimport Features from './pages/Features';\r\nimport About from './pages/About';\r\n\r\nfunction App() {\r\n  return (\r\n    <AuthProvider>\r\n      <div className=\"flex flex-col min-h-screen\">\r\n        <Navbar />\r\n        <main className=\"flex-grow\">\r\n          <Routes>\r\n            <Route path=\"/\" element={<Home />} />\r\n            <Route path=\"/auth\" element={<Auth />} />\r\n            <Route path=\"/features\" element={<Features />} />\r\n            <Route path=\"/about\" element={<About />} />\r\n\r\n            {/* Protected Routes */}\r\n            <Route path=\"/dashboard\" element={\r\n              <ProtectedRoute>\r\n                <Dashboard />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/timer\" element={\r\n              <ProtectedRoute>\r\n                <Timer />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/programs\" element={\r\n              <ProtectedRoute>\r\n                <Programs />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/progress\" element={\r\n              <ProtectedRoute>\r\n                <Progress />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/performance\" element={\r\n              <ProtectedRoute>\r\n                <Performance />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/routes\" element={\r\n              <ProtectedRoute>\r\n                <RoutesModern />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/routes-old\" element={\r\n              <ProtectedRoute>\r\n                <RoutesPage />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/social\" element={\r\n              <ProtectedRoute>\r\n                <Social />\r\n              </ProtectedRoute>\r\n            } />\r\n            <Route path=\"/exercises\" element={\r\n              <ProtectedRoute>\r\n                <Exercises />\r\n              </ProtectedRoute>\r\n            } />\r\n          </Routes>\r\n        </main>\r\n        <Footer />\r\n      </div>\r\n    </AuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO,CAAC,CAAC;AAC3B,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAAClB,YAAY;IAAAoB,QAAA,eACXF,OAAA;MAAKG,SAAS,EAAC,4BAA4B;MAAAD,QAAA,gBACzCF,OAAA,CAACjB,MAAM;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAMG,SAAS,EAAC,WAAW;QAAAD,QAAA,eACzBF,OAAA,CAACpB,MAAM;UAAAsB,QAAA,gBACLF,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACd,IAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,OAAO;YAACC,OAAO,eAAET,OAAA,CAACb,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAET,OAAA,CAACH,QAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAET,OAAA,CAACF,KAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG3CP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,YAAY;YAACC,OAAO,eAC9BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACZ,SAAS;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACX,KAAK;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,WAAW;YAACC,OAAO,eAC7BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACV,QAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,WAAW;YAACC,OAAO,eAC7BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACT,QAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,cAAc;YAACC,OAAO,eAChCT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACR,WAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,SAAS;YAACC,OAAO,eAC3BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACN,YAAY;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,aAAa;YAACC,OAAO,eAC/BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACP,UAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,SAAS;YAACC,OAAO,eAC3BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACL,MAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACnB,KAAK;YAAC2B,IAAI,EAAC,YAAY;YAACC,OAAO,eAC9BT,OAAA,CAACf,cAAc;cAAAiB,QAAA,eACbF,OAAA,CAACJ,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPP,OAAA,CAAChB,MAAM;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB;AAACG,EAAA,GAhEQT,GAAG;AAkEZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}