{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Routes.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiMap, FiNavigation, FiMapPin, FiSearch, FiPlus, FiStar, FiUsers, FiTarget, FiBookmark, FiTrash2, FiPlay, FiClock, FiAlertCircle } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generatePopularRoutes, generatePOIs, generateSegments, calculateDistance, optimizeRouteWithPOIs, DEFAULT_POSITION } from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\n\n// Configuration des icônes Leaflet\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\nconst Routes = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    user\n  } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const pos = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        };\n        setUserPosition(pos);\n        loadRoutesAndPOIs(pos);\n      }, error => {\n        console.warn('Géolocalisation échouée:', error);\n        setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n        loadRoutesAndPOIs(DEFAULT_POSITION);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      });\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n  const loadRoutesAndPOIs = position => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) || route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || filters.distance === 'short' && route.distance <= 5 || filters.distance === 'medium' && route.distance > 5 && route.distance <= 15 || filters.distance === 'long' && route.distance > 15;\n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n  const handleSegmentSelect = segment => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n  const handleFiltersChange = newFilters => {\n    setFilters(newFilters);\n  };\n  const handlePOIToggle = poi => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n  const MapClickHandler = () => {\n    _s();\n    useMapEvents({\n      click: e => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n  _s(MapClickHandler, \"Ld/tk8Iz8AdZhC1l7acENaOEoCo=\", false, function () {\n    return [useMapEvents];\n  });\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n  const calculateTotalDistance = points => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n    }\n    return total;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const saveRoute = route => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = {\n        ...route,\n        savedAt: new Date().toISOString()\n      };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n  const unsaveRoute = routeId => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n  const deleteCustomRoute = routeId => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if ((selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n  const startRoute = route => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Cartes et Itin\\xE9raires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez de nouveaux parcours, planifiez vos itin\\xE9raires et explorez les points d'int\\xE9r\\xEAt autour de vous.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-yellow-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: \"Chargement des donn\\xE9es...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('discover'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'discover' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMap, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), \"D\\xE9couvrir\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('segments'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'segments' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), \"Segments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('saved'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'saved' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), \"Sauvegard\\xE9es (\", savedRoutes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('plan'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'plan' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiNavigation, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), \"Planifier\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('pois'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'pois' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), \"Points d'int\\xE9r\\xEAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [activeTab === 'discover' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes populaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPOIs(!showPOIs),\n                className: `px-3 py-1 rounded-md text-sm ${showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                children: \"POIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un parcours...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.type,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    type: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Tous types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"running\",\n                    children: \"Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"cycling\",\n                    children: \"V\\xE9lo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hiking\",\n                    children: \"Randonn\\xE9e\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"walking\",\n                    children: \"Marche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.difficulty,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    difficulty: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Toutes difficult\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.completions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 495,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 27\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this), savedRoutes.find(r => r.id === route.id) ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => saveRoute(route),\n                    className: \"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\",\n                    title: \"Sauvegarder\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 23\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), activeTab === 'segments' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(AdvancedFilters, {\n              filters: filters,\n              onFiltersChange: handleFiltersChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900\",\n                  children: \"Segments populaires\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowSegments(!showSegments),\n                  className: `px-3 py-1 rounded-md text-sm ${showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                  children: \"Afficher sur la carte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                children: segments.map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleSegmentSelect(segment),\n                  className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs font-medium ${segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' : segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' : segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' : 'bg-purple-100 text-purple-800'}`,\n                      children: segment.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 mb-3\",\n                    children: segment.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 29\n                      }, this), segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 29\n                      }, this), segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 29\n                      }, this), segment.attempts, \" tentatives\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 29\n                      }, this), \"Record: \", segment.recordTime]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 25\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), activeTab === 'saved' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes sauvegard\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [savedRoutes.length, \" route\", savedRoutes.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), savedRoutes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mb-2\",\n                children: \"Aucune route sauvegard\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Sauvegardez vos routes pr\\xE9f\\xE9r\\xE9es pour les retrouver facilement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: savedRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 29\n                    }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\",\n                      children: \"Personnalis\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: route.savedAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Sauvegard\\xE9 le \", new Date(route.savedAt).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 29\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 25\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this), activeTab === 'plan' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Planificateur d'itin\\xE9raire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this), !isPlanning ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startRoutePlanning,\n                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 23\n                }, this), \"Cr\\xE9er un nouveau parcours\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: \"Instructions :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Cr\\xE9er un nouveau parcours\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur la carte pour ajouter des points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Minimum 2 points requis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Terminer\\\" pour sauvegarder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium mb-2\",\n                  children: \"Mode planification actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Points ajout\\xE9s : \", planningPoints.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 23\n                }, this), planningPoints.length >= 2 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Distance : \", calculateTotalDistance(planningPoints).toFixed(1), \" km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: finishPlanning,\n                  disabled: planningPoints.length < 2,\n                  className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                  children: \"Terminer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setIsPlanning(false);\n                    setPlanningPoints([]);\n                  },\n                  className: \"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                  children: \"Annuler\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 15\n          }, this), activeTab === 'pois' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Points d'int\\xE9r\\xEAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this), selectedRoute && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700 mb-2\",\n                children: \"S\\xE9lectionnez des POIs pour optimiser votre parcours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: optimizeRoute,\n                disabled: selectedPOIs.length === 0,\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                  className: \"inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 23\n                }, this), \"Optimiser l'itin\\xE9raire (\", selectedPOIs.length, \" POIs)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 max-h-96 overflow-y-auto\",\n              children: pois.map(poi => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handlePOIToggle(poi),\n                className: `p-3 border rounded-lg cursor-pointer transition-colors ${selectedPOIs.find(p => p.id === poi.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: poi.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: poi.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: poi.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 808,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: poi.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 809,\n                        columnNumber: 29\n                      }, this), poi.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 text-xs text-green-600\",\n                        children: \"\\u2713 V\\xE9rifi\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 807,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 23\n                }, this)\n              }, poi.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-96 lg:h-[600px]\",\n              children: /*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [userPosition.lat, userPosition.lng],\n                zoom: 13,\n                style: {\n                  height: '100%',\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MapClickHandler, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [userPosition.lat, userPosition.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: \"Votre position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n                  color: \"blue\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 21\n                }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: optimizedRoute.map(p => [p.lat, p.lng]),\n                  color: \"red\",\n                  weight: 4,\n                  opacity: 0.8\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 21\n                }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [point.lat, point.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: [\"Point \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 21\n                }, this)), planningPoints.length > 1 && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: planningPoints.map(p => [p.lat, p.lng]),\n                  color: \"green\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                    className: 'custom-poi-marker',\n                    iconSize: [30, 30]\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: poi.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 894,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: poi.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 895,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                          className: \"h-3 w-3 text-yellow-400 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 897,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm\",\n                          children: poi.rating.toFixed(1)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 898,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 896,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this)\n                }, poi.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                    className: 'custom-selected-poi-marker',\n                    iconSize: [35, 35]\n                  })\n                }, `selected-${poi.id}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)), (showSegments || activeTab === 'segments') && segments.map(segment => /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: segment.points.map(point => [point.lat, point.lng]),\n                  color: (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? '#3B82F6' : segment.type === 'climb' ? '#EF4444' : segment.type === 'sprint' ? '#10B981' : segment.type === 'descent' ? '#F59E0B' : '#8B5CF6',\n                  weight: (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? 6 : 4,\n                  opacity: 0.8,\n                  eventHandlers: {\n                    click: () => handleSegmentSelect(segment)\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900 mb-1\",\n                        children: segment.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 mb-2\",\n                        children: segment.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-1 text-xs text-gray-500\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Distance: \", segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 939,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"D\\xE9nivel\\xE9: \", segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Record: \", segment.recordTime]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 941,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Tentatives: \", segment.attempts]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 938,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 23\n                  }, this)\n                }, segment.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), selectedSegment && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(SegmentPanel, {\n          segments: segments,\n          selectedSegment: selectedSegment,\n          onSegmentSelect: handleSegmentSelect,\n          userPosition: userPosition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 957,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 310,\n    columnNumber: 5\n  }, this);\n};\n_s2(Routes, \"/GAdmxt/KEQN28cZpn0Cj9L97BY=\", false, function () {\n  return [useAuth];\n});\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "L", "FiMap", "FiNavigation", "FiMapPin", "FiSearch", "FiPlus", "FiStar", "FiUsers", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiTrash2", "FiPlay", "<PERSON><PERSON><PERSON>", "FiAlertCircle", "useAuth", "generatePopularRoutes", "generatePOIs", "generateSegments", "calculateDistance", "optimizeRouteWithPOIs", "DEFAULT_POSITION", "AdvancedFilters", "SegmentPanel", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "Routes", "_s2", "_s", "$RefreshSig$", "user", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedPOIs", "setSelectedPOIs", "optimizedRoute", "setOptimizedRoute", "isPlanning", "setIsPlanning", "planningPoints", "setPlanningPoints", "filters", "setFilters", "type", "difficulty", "distance", "sport", "surface", "searchTerm", "setSearchTerm", "showPOIs", "setShowPOIs", "activeTab", "setActiveTab", "savedRoutes", "setSavedRoutes", "routeHistory", "setRouteHistory", "segments", "setSegments", "selectedSegment", "setSelectedSegment", "showSegments", "setShowSegments", "isLoading", "setIsLoading", "error", "setError", "saved", "JSON", "parse", "localStorage", "getItem", "history", "savedSegments", "console", "navigator", "geolocation", "getCurrentPosition", "position", "pos", "lat", "coords", "latitude", "lng", "longitude", "loadRoutesAndPOIs", "warn", "enableHighAccuracy", "timeout", "maximumAge", "popularRoutes", "nearbyPOIs", "nearbySegments", "allRoutes", "filteredRoutes", "filter", "route", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesType", "matchesDifficulty", "matchesDistance", "handleRouteSelect", "handleSegmentSelect", "segment", "handleFiltersChange", "newFilters", "handlePOIToggle", "poi", "prev", "isSelected", "find", "p", "id", "optimizeRoute", "length", "startPoint", "points", "endPoint", "optimized", "startRoutePlanning", "MapClickHandler", "click", "e", "newPoint", "latlng", "elevation", "finishPlanning", "newRoute", "Date", "now", "calculateTotalDistance", "rating", "completions", "created<PERSON>y", "firstName", "tags", "createdAt", "toISOString", "isCustom", "updatedSavedRoutes", "setItem", "stringify", "total", "i", "getDifficultyColor", "getTypeIcon", "saveRoute", "r", "routeToSave", "savedAt", "unsaveRoute", "routeId", "deleteCustomRoute", "startRoute", "historyEntry", "startedAt", "status", "updatedHistory", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "target", "map", "toFixed", "stopPropagation", "title", "onFiltersChange", "FiTrendingUp", "attempts", "recordTime", "toLocaleDateString", "disabled", "icon", "verified", "center", "zoom", "style", "height", "width", "url", "attribution", "positions", "color", "weight", "opacity", "point", "index", "divIcon", "html", "iconSize", "eventHandlers", "onSegmentSelect", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Routes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiMap,\n  FiNavigation,\n  FiMapPin,\n  FiSearch,\n  FiPlus,\n  FiStar,\n  FiUsers,\n  FiTarget,\n  FiBookmark,\n  FiTrash2,\n  FiPlay,\n  FiClock,\n  FiAlertCircle\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generatePopularRoutes,\n  generatePOIs,\n  generateSegments,\n  calculateDistance,\n  optimizeRouteWithPOIs,\n  DEFAULT_POSITION\n} from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\n\n// Configuration des icônes Leaflet\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\nconst Routes = () => {\n  const { user } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const pos = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          };\n          setUserPosition(pos);\n          loadRoutesAndPOIs(pos);\n        },\n        (error) => {\n          console.warn('Géolocalisation échouée:', error);\n          setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n          loadRoutesAndPOIs(DEFAULT_POSITION);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 300000 // 5 minutes\n        }\n      );\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n\n  const loadRoutesAndPOIs = (position) => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || \n                           (filters.distance === 'short' && route.distance <= 5) ||\n                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||\n                           (filters.distance === 'long' && route.distance > 15);\n    \n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n\n  const handleSegmentSelect = (segment) => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n\n  const handleFiltersChange = (newFilters) => {\n    setFilters(newFilters);\n  };\n\n  const handlePOIToggle = (poi) => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n\n  const MapClickHandler = () => {\n    useMapEvents({\n      click: (e) => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: user?.firstName || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n\n  const calculateTotalDistance = (points) => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(\n        points[i-1].lat, points[i-1].lng,\n        points[i].lat, points[i].lng\n      );\n    }\n    return total;\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const saveRoute = (route) => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = { ...route, savedAt: new Date().toISOString() };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n\n  const unsaveRoute = (routeId) => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n\n  const deleteCustomRoute = (routeId) => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if (selectedRoute?.id === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n\n  const startRoute = (route) => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Cartes et Itinéraires\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez\n            les points d'intérêt autour de vous.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                <p className=\"text-sm text-yellow-800\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Indicateur de chargement */}\n          {isLoading && (\n            <div className=\"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"></div>\n                <p className=\"text-sm text-blue-800\">Chargement des données...</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('discover')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'discover'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMap className=\"inline mr-2\" />\n            Découvrir\n          </button>\n          <button\n            onClick={() => setActiveTab('segments')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'segments'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Segments\n          </button>\n          <button\n            onClick={() => setActiveTab('saved')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'saved'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiBookmark className=\"inline mr-2\" />\n            Sauvegardées ({savedRoutes.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('plan')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'plan'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiNavigation className=\"inline mr-2\" />\n            Planifier\n          </button>\n          <button\n            onClick={() => setActiveTab('pois')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'pois'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMapPin className=\"inline mr-2\" />\n            Points d'intérêt\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {activeTab === 'discover' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes populaires\n                  </h2>\n                  <button\n                    onClick={() => setShowPOIs(!showPOIs)}\n                    className={`px-3 py-1 rounded-md text-sm ${\n                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    POIs\n                  </button>\n                </div>\n\n                {/* Search and Filters */}\n                <div className=\"space-y-4 mb-6\">\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un parcours...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <select\n                      value={filters.type}\n                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Tous types</option>\n                      <option value=\"running\">Course</option>\n                      <option value=\"cycling\">Vélo</option>\n                      <option value=\"hiking\">Randonnée</option>\n                      <option value=\"walking\">Marche</option>\n                    </select>\n\n                    <select\n                      value={filters.difficulty}\n                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Toutes difficultés</option>\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n\n                {/* Routes List */}\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {filteredRoutes.map(route => (\n                    <div\n                      key={route.id}\n                      onClick={() => handleRouteSelect(route)}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedRoute?.id === route.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                          <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                        </div>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                          {route.difficulty}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 mb-2\">\n                        {route.description}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                        <span>{route.distance.toFixed(1)} km</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span>{route.rating.toFixed(1)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <FiUsers className=\"h-3 w-3 mr-1\" />\n                            <span>{route.completions}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Boutons d'action */}\n                      <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                        <button\n                          onClick={() => startRoute(route)}\n                          className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                        >\n                          <FiPlay className=\"h-3 w-3 mr-1\" />\n                          Démarrer\n                        </button>\n\n                        {savedRoutes.find(r => r.id === route.id) ? (\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => saveRoute(route)}\n                            className=\"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\"\n                            title=\"Sauvegarder\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        )}\n\n                        {route.isCustom && (\n                          <button\n                            onClick={() => deleteCustomRoute(route.id)}\n                            className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                            title=\"Supprimer\"\n                          >\n                            <FiTrash2 className=\"h-3 w-3\" />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'segments' && (\n              <div className=\"space-y-6\">\n                {/* Filtres avancés */}\n                <AdvancedFilters\n                  filters={filters}\n                  onFiltersChange={handleFiltersChange}\n                />\n\n                {/* Liste des segments */}\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Segments populaires\n                    </h2>\n                    <button\n                      onClick={() => setShowSegments(!showSegments)}\n                      className={`px-3 py-1 rounded-md text-sm ${\n                        showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      Afficher sur la carte\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {segments.map(segment => (\n                      <div\n                        key={segment.id}\n                        onClick={() => handleSegmentSelect(segment)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedSegment?.id === segment.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' :\n                            segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' :\n                            segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' :\n                            'bg-purple-100 text-purple-800'\n                          }`}>\n                            {segment.difficulty}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-600 mb-3\">{segment.description}</p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <FiMapPin className=\"h-4 w-4 mr-1\" />\n                            {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiTrendingUp className=\"h-4 w-4 mr-1\" />\n                            {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiUsers className=\"h-4 w-4 mr-1\" />\n                            {segment.attempts} tentatives\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiClock className=\"h-4 w-4 mr-1\" />\n                            Record: {segment.recordTime}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'saved' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes sauvegardées\n                  </h2>\n                  <span className=\"text-sm text-gray-500\">\n                    {savedRoutes.length} route{savedRoutes.length !== 1 ? 's' : ''}\n                  </span>\n                </div>\n\n                {savedRoutes.length === 0 ? (\n                  <div className=\"text-center py-8\">\n                    <FiBookmark className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                    <p className=\"text-gray-500 mb-2\">Aucune route sauvegardée</p>\n                    <p className=\"text-sm text-gray-400\">\n                      Sauvegardez vos routes préférées pour les retrouver facilement\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {savedRoutes.map(route => (\n                      <div\n                        key={route.id}\n                        onClick={() => handleRouteSelect(route)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedRoute?.id === route.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                            <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                            {route.isCustom && (\n                              <span className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\">\n                                Personnalisé\n                              </span>\n                            )}\n                          </div>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                            {route.difficulty}\n                          </span>\n                        </div>\n\n                        <div className=\"text-sm text-gray-600 mb-2\">\n                          {route.description}\n                        </div>\n\n                        <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                          <span>{route.distance.toFixed(1)} km</span>\n                          <div className=\"flex items-center space-x-2\">\n                            {route.savedAt && (\n                              <span>Sauvegardé le {new Date(route.savedAt).toLocaleDateString()}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Boutons d'action */}\n                        <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                          <button\n                            onClick={() => startRoute(route)}\n                            className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                          >\n                            <FiPlay className=\"h-3 w-3 mr-1\" />\n                            Démarrer\n                          </button>\n\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n\n                          {route.isCustom && (\n                            <button\n                              onClick={() => deleteCustomRoute(route.id)}\n                              className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                              title=\"Supprimer\"\n                            >\n                              <FiTrash2 className=\"h-3 w-3\" />\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'plan' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Planificateur d'itinéraire\n                </h2>\n                \n                {!isPlanning ? (\n                  <div className=\"space-y-4\">\n                    <button\n                      onClick={startRoutePlanning}\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n                    >\n                      <FiPlus className=\"mr-2\" />\n                      Créer un nouveau parcours\n                    </button>\n                    \n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"mb-2\">Instructions :</p>\n                      <ul className=\"list-disc list-inside space-y-1\">\n                        <li>Cliquez sur \"Créer un nouveau parcours\"</li>\n                        <li>Cliquez sur la carte pour ajouter des points</li>\n                        <li>Minimum 2 points requis</li>\n                        <li>Cliquez sur \"Terminer\" pour sauvegarder</li>\n                      </ul>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">Mode planification actif</p>\n                      <p>Points ajoutés : {planningPoints.length}</p>\n                      {planningPoints.length >= 2 && (\n                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={finishPlanning}\n                        disabled={planningPoints.length < 2}\n                        className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Terminer\n                      </button>\n                      <button\n                        onClick={() => {\n                          setIsPlanning(false);\n                          setPlanningPoints([]);\n                        }}\n                        className=\"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\"\n                      >\n                        Annuler\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'pois' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Points d'intérêt\n                </h2>\n                \n                {selectedRoute && (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <p className=\"text-sm text-blue-700 mb-2\">\n                      Sélectionnez des POIs pour optimiser votre parcours\n                    </p>\n                    <button\n                      onClick={optimizeRoute}\n                      disabled={selectedPOIs.length === 0}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\"\n                    >\n                      <FiTarget className=\"inline mr-2\" />\n                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)\n                    </button>\n                  </div>\n                )}\n                \n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n                  {pois.map(poi => (\n                    <div\n                      key={poi.id}\n                      onClick={() => handlePOIToggle(poi)}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedPOIs.find(p => p.id === poi.id)\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{poi.icon}</span>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{poi.name}</h4>\n                          <p className=\"text-xs text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-xs text-gray-500\">{poi.rating.toFixed(1)}</span>\n                            {poi.verified && (\n                              <span className=\"ml-2 text-xs text-green-600\">✓ Vérifié</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n              <div className=\"h-96 lg:h-[600px]\">\n                <MapContainer\n                  center={[userPosition.lat, userPosition.lng]}\n                  zoom={13}\n                  style={{ height: '100%', width: '100%' }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  \n                  <MapClickHandler />\n                  \n                  {/* User position */}\n                  <Marker position={[userPosition.lat, userPosition.lng]}>\n                    <Popup>Votre position</Popup>\n                  </Marker>\n                  \n                  {/* Selected route */}\n                  {selectedRoute && (\n                    <Polyline\n                      positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n                      color=\"blue\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* Optimized route */}\n                  {optimizedRoute && (\n                    <Polyline\n                      positions={optimizedRoute.map(p => [p.lat, p.lng])}\n                      color=\"red\"\n                      weight={4}\n                      opacity={0.8}\n                    />\n                  )}\n                  \n                  {/* Planning points */}\n                  {planningPoints.map((point, index) => (\n                    <Marker key={index} position={[point.lat, point.lng]}>\n                      <Popup>Point {index + 1}</Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Planning route */}\n                  {planningPoints.length > 1 && (\n                    <Polyline\n                      positions={planningPoints.map(p => [p.lat, p.lng])}\n                      color=\"green\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* POIs */}\n                  {showPOIs && pois.map(poi => (\n                    <Marker\n                      key={poi.id}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                        className: 'custom-poi-marker',\n                        iconSize: [30, 30]\n                      })}\n                    >\n                      <Popup>\n                        <div>\n                          <h4 className=\"font-medium\">{poi.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-sm\">{poi.rating.toFixed(1)}</span>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Selected POIs highlight */}\n                  {selectedPOIs.map(poi => (\n                    <Marker\n                      key={`selected-${poi.id}`}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                        className: 'custom-selected-poi-marker',\n                        iconSize: [35, 35]\n                      })}\n                    />\n                  ))}\n\n                  {/* Segments */}\n                  {(showSegments || activeTab === 'segments') && segments.map(segment => (\n                    <Polyline\n                      key={segment.id}\n                      positions={segment.points.map(point => [point.lat, point.lng])}\n                      color={selectedSegment?.id === segment.id ? '#3B82F6' :\n                             segment.type === 'climb' ? '#EF4444' :\n                             segment.type === 'sprint' ? '#10B981' :\n                             segment.type === 'descent' ? '#F59E0B' :\n                             '#8B5CF6'}\n                      weight={selectedSegment?.id === segment.id ? 6 : 4}\n                      opacity={0.8}\n                      eventHandlers={{\n                        click: () => handleSegmentSelect(segment)\n                      }}\n                    >\n                      <Popup>\n                        <div className=\"p-2\">\n                          <h3 className=\"font-semibold text-gray-900 mb-1\">{segment.name}</h3>\n                          <p className=\"text-sm text-gray-600 mb-2\">{segment.description}</p>\n                          <div className=\"space-y-1 text-xs text-gray-500\">\n                            <div>Distance: {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}</div>\n                            <div>Dénivelé: {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}</div>\n                            <div>Record: {segment.recordTime}</div>\n                            <div>Tentatives: {segment.attempts}</div>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Polyline>\n                  ))}\n                </MapContainer>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Panneau de segment sélectionné */}\n        {selectedSegment && (\n          <div className=\"mt-8\">\n            <SegmentPanel\n              segments={segments}\n              selectedSegment={selectedSegment}\n              onSegmentSelect={handleSegmentSelect}\n              userPosition={userPosition}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAC9F,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,EACrBC,gBAAgB,QACX,mBAAmB;AAC1B,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,YAAY,MAAM,4BAA4B;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOxB,CAAC,CAACyB,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3C5B,CAAC,CAACyB,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC4B,gBAAgB,CAAC;EAClE,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC;IACrCgE,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuE,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuF,KAAK,EAAEC,QAAQ,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMwF,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACrE,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MACxE,MAAME,aAAa,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;MAC1EjB,cAAc,CAACa,KAAK,CAAC;MACrBX,eAAe,CAACgB,OAAO,CAAC;MACxBd,WAAW,CAACe,aAAa,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAENtF,SAAS,CAAC,MAAM;IACdqF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIS,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAMC,GAAG,GAAG;UACVC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG;QACvB,CAAC;QACD3D,eAAe,CAACsD,GAAG,CAAC;QACpBM,iBAAiB,CAACN,GAAG,CAAC;MACxB,CAAC,EACAd,KAAK,IAAK;QACTS,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAErB,KAAK,CAAC;QAC/CC,QAAQ,CAAC,8EAA8E,CAAC;QACxFmB,iBAAiB,CAAC/E,gBAAgB,CAAC;MACrC,CAAC,EACD;QACEiF,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,MAAM,CAAC;MACrB,CACF,CAAC;IACH,CAAC,MAAM;MACLvB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DmB,iBAAiB,CAAC/E,gBAAgB,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+E,iBAAiB,GAAIP,QAAQ,IAAK;IACtC,IAAI;MACF,MAAMY,aAAa,GAAGzF,qBAAqB,CAAC6E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC3E,MAAMQ,UAAU,GAAGzF,YAAY,CAAC4E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC/D,MAAMS,cAAc,GAAGzF,gBAAgB,CAAC2E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,CAAC,CAAC;;MAEtE;MACA,MAAMU,SAAS,GAAG,CAAC,GAAGxC,WAAW,EAAE,GAAGqC,aAAa,CAAC;MACpD/D,SAAS,CAACkE,SAAS,CAAC;MACpBhE,OAAO,CAAC8D,UAAU,CAAC;MACnBjC,WAAW,CAACkC,cAAc,CAAC;MAC3B5B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM8B,cAAc,GAAGpE,MAAM,CAACqE,MAAM,CAACC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGD,KAAK,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC5DH,KAAK,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,WAAW,GAAG9D,OAAO,CAACE,IAAI,KAAK,KAAK,IAAIsD,KAAK,CAACtD,IAAI,KAAKF,OAAO,CAACE,IAAI;IACzE,MAAM6D,iBAAiB,GAAG/D,OAAO,CAACG,UAAU,KAAK,KAAK,IAAIqD,KAAK,CAACrD,UAAU,KAAKH,OAAO,CAACG,UAAU;IACjG,MAAM6D,eAAe,GAAGhE,OAAO,CAACI,QAAQ,KAAK,KAAK,IAC1BJ,OAAO,CAACI,QAAQ,KAAK,OAAO,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,CAAE,IACpDJ,OAAO,CAACI,QAAQ,KAAK,QAAQ,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,CAAC,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,EAAG,IAC5EJ,OAAO,CAACI,QAAQ,KAAK,MAAM,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,EAAG;IAE3E,OAAOqD,aAAa,IAAIK,WAAW,IAAIC,iBAAiB,IAAIC,eAAe;EAC7E,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnCjE,gBAAgB,CAACiE,KAAK,CAAC;IACvB7D,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;IACnB2B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8C,mBAAmB,GAAIC,OAAO,IAAK;IACvC/C,kBAAkB,CAAC+C,OAAO,CAAC;IAC3B5E,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM2E,mBAAmB,GAAIC,UAAU,IAAK;IAC1CpE,UAAU,CAACoE,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,eAAe,GAAIC,GAAG,IAAK;IAC/B9E,eAAe,CAAC+E,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAClD,IAAIH,UAAU,EAAE;QACd,OAAOD,IAAI,CAACjB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGJ,IAAI,EAAED,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIvF,aAAa,IAAIE,YAAY,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMC,UAAU,GAAGzF,aAAa,CAAC0F,MAAM,CAAC,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG3F,aAAa,CAAC0F,MAAM,CAAC1F,aAAa,CAAC0F,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMI,SAAS,GAAGrH,qBAAqB,CAACkH,UAAU,EAAEE,QAAQ,EAAEzF,YAAY,CAAC;MAC3EG,iBAAiB,CAACuF,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtF,aAAa,CAAC,IAAI,CAAC;IACnBE,iBAAiB,CAAC,EAAE,CAAC;IACrBR,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyF,eAAe,GAAGA,CAAA,KAAM;IAAAvG,EAAA;IAC5BpC,YAAY,CAAC;MACX4I,KAAK,EAAGC,CAAC,IAAK;QACZ,IAAI1F,UAAU,EAAE;UACd,MAAM2F,QAAQ,GAAG;YACf/C,GAAG,EAAE8C,CAAC,CAACE,MAAM,CAAChD,GAAG;YACjBG,GAAG,EAAE2C,CAAC,CAACE,MAAM,CAAC7C,GAAG;YACjB8C,SAAS,EAAE,GAAG,CAAC;UACjB,CAAC;UACD1F,iBAAiB,CAACyE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,QAAQ,CAAC,CAAC;QAChD;MACF;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAAC1G,EAAA,CAdIuG,eAAe;IAAA,QACnB3I,YAAY;EAAA;EAed,MAAMiJ,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI5F,cAAc,CAACgF,MAAM,IAAI,CAAC,EAAE;MAC9B,MAAMa,QAAQ,GAAG;QACff,EAAE,EAAE,UAAUgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1BnC,IAAI,EAAE,2BAA2B;QACjCxD,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAE0F,sBAAsB,CAAChG,cAAc,CAAC;QAChDkF,MAAM,EAAElF,cAAc;QACtBK,UAAU,EAAE,QAAQ;QACpB4F,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAAlH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmH,SAAS,KAAI,KAAK;QACnCC,IAAI,EAAE,CAAC,QAAQ,CAAC;QAChBtC,WAAW,EAAE,kCAAkC;QAC/CuC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,kBAAkB,GAAG,CAACZ,QAAQ,EAAE,GAAG9E,WAAW,CAAC;MACrDC,cAAc,CAACyF,kBAAkB,CAAC;MAClCzE,YAAY,CAAC0E,OAAO,CAAC,aAAa,EAAE5E,IAAI,CAAC6E,SAAS,CAACF,kBAAkB,CAAC,CAAC;MAEvEpH,SAAS,CAACqF,IAAI,IAAI,CAACmB,QAAQ,EAAE,GAAGnB,IAAI,CAAC,CAAC;MACtCjF,gBAAgB,CAACoG,QAAQ,CAAC;MAC1B9F,aAAa,CAAC,KAAK,CAAC;MACpBE,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAM+F,sBAAsB,GAAId,MAAM,IAAK;IACzC,IAAI0B,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,MAAM,CAACF,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtCD,KAAK,IAAI9I,iBAAiB,CACxBoH,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAACnE,GAAG,EAAEwC,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAAChE,GAAG,EAChCqC,MAAM,CAAC2B,CAAC,CAAC,CAACnE,GAAG,EAAEwC,MAAM,CAAC2B,CAAC,CAAC,CAAChE,GAC3B,CAAC;IACH;IACA,OAAO+D,KAAK;EACd,CAAC;EAED,MAAME,kBAAkB,GAAIzG,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAM0G,WAAW,GAAI3G,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAM4G,SAAS,GAAItD,KAAK,IAAK;IAC3B,IAAI,CAAC3C,WAAW,CAAC6D,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,EAAE;MAC7C,MAAMoC,WAAW,GAAG;QAAE,GAAGxD,KAAK;QAAEyD,OAAO,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MAAE,CAAC;MACnE,MAAME,kBAAkB,GAAG,CAACS,WAAW,EAAE,GAAGnG,WAAW,CAAC;MACxDC,cAAc,CAACyF,kBAAkB,CAAC;MAClCzE,YAAY,CAAC0E,OAAO,CAAC,aAAa,EAAE5E,IAAI,CAAC6E,SAAS,CAACF,kBAAkB,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMW,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMZ,kBAAkB,GAAG1F,WAAW,CAAC0C,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpErG,cAAc,CAACyF,kBAAkB,CAAC;IAClCzE,YAAY,CAAC0E,OAAO,CAAC,aAAa,EAAE5E,IAAI,CAAC6E,SAAS,CAACF,kBAAkB,CAAC,CAAC;EACzE,CAAC;EAED,MAAMa,iBAAiB,GAAID,OAAO,IAAK;IACrC;IACA,MAAMZ,kBAAkB,GAAG1F,WAAW,CAAC0C,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpErG,cAAc,CAACyF,kBAAkB,CAAC;IAClCzE,YAAY,CAAC0E,OAAO,CAAC,aAAa,EAAE5E,IAAI,CAAC6E,SAAS,CAACF,kBAAkB,CAAC,CAAC;;IAEvE;IACApH,SAAS,CAACqF,IAAI,IAAIA,IAAI,CAACjB,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC,CAAC;;IAErD;IACA,IAAI,CAAA7H,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKuC,OAAO,EAAE;MACjC5H,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM8H,UAAU,GAAI7D,KAAK,IAAK;IAC5B,MAAM8D,YAAY,GAAG;MACnB1C,EAAE,EAAEgB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdrC,KAAK,EAAEA,KAAK;MACZ+D,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCmB,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,cAAc,GAAG,CAACH,YAAY,EAAE,GAAGvG,YAAY,CAAC;IACtDC,eAAe,CAACyG,cAAc,CAAC;IAC/B3F,YAAY,CAAC0E,OAAO,CAAC,cAAc,EAAE5E,IAAI,CAAC6E,SAAS,CAACgB,cAAc,CAAC,CAAC;EACtE,CAAC;EAED,oBACEvJ,OAAA;IAAKwJ,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCzJ,OAAA;MAAKwJ,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DzJ,OAAA;QAAKwJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzJ,OAAA;UAAIwJ,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7J,OAAA;UAAGwJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHtG,KAAK,iBACJvD,OAAA;UAAKwJ,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFzJ,OAAA;YAAKwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzJ,OAAA,CAACX,aAAa;cAACmK,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D7J,OAAA;cAAGwJ,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAElG;YAAK;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAxG,SAAS,iBACRrD,OAAA;UAAKwJ,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrFzJ,OAAA;YAAKwJ,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CzJ,OAAA;cAAKwJ,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzF7J,OAAA;cAAGwJ,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7J,OAAA;QAAKwJ,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DzJ,OAAA;UACE8J,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,UAAU,CAAE;UACxC8G,SAAS,EAAE,qEACT/G,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAgH,QAAA,gBAEHzJ,OAAA,CAACvB,KAAK;YAAC+K,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7J,OAAA;UACE8J,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,UAAU,CAAE;UACxC8G,SAAS,EAAE,qEACT/G,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAgH,QAAA,gBAEHzJ,OAAA,CAAChB,QAAQ;YAACwK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7J,OAAA;UACE8J,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,OAAO,CAAE;UACrC8G,SAAS,EAAE,qEACT/G,SAAS,KAAK,OAAO,GACjB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAgH,QAAA,gBAEHzJ,OAAA,CAACf,UAAU;YAACuK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACxB,EAAClH,WAAW,CAACiE,MAAM,EAAC,GACpC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7J,OAAA;UACE8J,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,MAAM,CAAE;UACpC8G,SAAS,EAAE,qEACT/G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAgH,QAAA,gBAEHzJ,OAAA,CAACtB,YAAY;YAAC8K,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7J,OAAA;UACE8J,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,MAAM,CAAE;UACpC8G,SAAS,EAAE,qEACT/G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAgH,QAAA,gBAEHzJ,OAAA,CAACrB,QAAQ;YAAC6K,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7J,OAAA;QAAKwJ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDzJ,OAAA;UAAKwJ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BhH,SAAS,KAAK,UAAU,iBACvBzC,OAAA;YAAKwJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzJ,OAAA;cAAKwJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDzJ,OAAA;gBAAIwJ,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7J,OAAA;gBACE8J,OAAO,EAAEA,CAAA,KAAMtH,WAAW,CAAC,CAACD,QAAQ,CAAE;gBACtCiH,SAAS,EAAE,gCACTjH,QAAQ,GAAG,2BAA2B,GAAG,2BAA2B,EACnE;gBAAAkH,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN7J,OAAA;cAAKwJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzJ,OAAA;gBAAKwJ,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzJ,OAAA,CAACpB,QAAQ;kBAAC4K,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpE7J,OAAA;kBACEgC,IAAI,EAAC,MAAM;kBACX+H,WAAW,EAAC,2BAA2B;kBACvCC,KAAK,EAAE3H,UAAW;kBAClB4H,QAAQ,EAAG7C,CAAC,IAAK9E,aAAa,CAAC8E,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAA8G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzJ,OAAA;kBACEgK,KAAK,EAAElI,OAAO,CAACE,IAAK;kBACpBiI,QAAQ,EAAG7C,CAAC,IAAKrF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtE,IAAI,EAAEoF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACzER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnHzJ,OAAA;oBAAQgK,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC7J,OAAA;oBAAQgK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC7J,OAAA;oBAAQgK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC7J,OAAA;oBAAQgK,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzC7J,OAAA;oBAAQgK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAET7J,OAAA;kBACEgK,KAAK,EAAElI,OAAO,CAACG,UAAW;kBAC1BgI,QAAQ,EAAG7C,CAAC,IAAKrF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErE,UAAU,EAAEmF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/ER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnHzJ,OAAA;oBAAQgK,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/C7J,OAAA;oBAAQgK,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC7J,OAAA;oBAAQgK,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC7J,OAAA;oBAAQgK,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7J,OAAA;cAAKwJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDrE,cAAc,CAAC+E,GAAG,CAAC7E,KAAK,iBACvBtF,OAAA;gBAEE8J,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAACT,KAAK,CAAE;gBACxCkE,SAAS,EAAE,0DACT,CAAApI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,gBAEHzJ,OAAA;kBAAKwJ,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDzJ,OAAA;oBAAKwJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CzJ,OAAA;sBAAMwJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACrD,KAAK,CAACtD,IAAI;oBAAC;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D7J,OAAA;sBAAIwJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnE,KAAK,CAACE;oBAAI;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN7J,OAAA;oBAAMwJ,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACpD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAwH,QAAA,EACnGnE,KAAK,CAACrD;kBAAU;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN7J,OAAA;kBAAKwJ,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxCnE,KAAK,CAACK;gBAAW;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEN7J,OAAA;kBAAKwJ,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3EzJ,OAAA;oBAAAyJ,QAAA,GAAOnE,KAAK,CAACpD,QAAQ,CAACkI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C7J,OAAA;oBAAKwJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CzJ,OAAA;sBAAKwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCzJ,OAAA,CAAClB,MAAM;wBAAC0K,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD7J,OAAA;wBAAAyJ,QAAA,EAAOnE,KAAK,CAACuC,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN7J,OAAA;sBAAKwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCzJ,OAAA,CAACjB,OAAO;wBAACyK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpC7J,OAAA;wBAAAyJ,QAAA,EAAOnE,KAAK,CAACwC;sBAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7J,OAAA;kBAAKwJ,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/EzJ,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC7D,KAAK,CAAE;oBACjCkE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1IzJ,OAAA,CAACb,MAAM;sBAACqK,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERlH,WAAW,CAAC6D,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,gBACvC1G,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC1D,KAAK,CAACoB,EAAE,CAAE;oBACrC8C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3BzJ,OAAA,CAACf,UAAU;sBAACuK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,gBAET7J,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMlB,SAAS,CAACtD,KAAK,CAAE;oBAChCkE,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,aAAa;oBAAAb,QAAA,eAEnBzJ,OAAA,CAACf,UAAU;sBAACuK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACT,EAEAvE,KAAK,CAAC8C,QAAQ,iBACbpI,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC5D,KAAK,CAACoB,EAAE,CAAE;oBAC3C8C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjBzJ,OAAA,CAACd,QAAQ;sBAACsK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzEDvE,KAAK,CAACoB,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0EV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEApH,SAAS,KAAK,UAAU,iBACvBzC,OAAA;YAAKwJ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBzJ,OAAA,CAACH,eAAe;cACdiC,OAAO,EAAEA,OAAQ;cACjByI,eAAe,EAAErE;YAAoB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAGF7J,OAAA;cAAKwJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDzJ,OAAA;gBAAKwJ,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDzJ,OAAA;kBAAIwJ,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAEpD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7J,OAAA;kBACE8J,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CqG,SAAS,EAAE,gCACTrG,YAAY,GAAG,2BAA2B,GAAG,2BAA2B,EACvE;kBAAAsG,QAAA,EACJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChD1G,QAAQ,CAACoH,GAAG,CAAClE,OAAO,iBACnBjG,OAAA;kBAEE8J,OAAO,EAAEA,CAAA,KAAM9D,mBAAmB,CAACC,OAAO,CAAE;kBAC5CuD,SAAS,EAAE,0DACT,CAAAvG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAC9B,4BAA4B,GAC5B,uCAAuC,EAC1C;kBAAA+C,QAAA,gBAEHzJ,OAAA;oBAAKwJ,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDzJ,OAAA;sBAAIwJ,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAExD,OAAO,CAACT;oBAAI;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/D7J,OAAA;sBAAMwJ,SAAS,EAAE,8CACfvD,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,6BAA6B,GAC/DgE,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,+BAA+B,GACjEgE,OAAO,CAAChE,UAAU,KAAK,WAAW,GAAG,yBAAyB,GAC9D,+BAA+B,EAC9B;sBAAAwH,QAAA,EACAxD,OAAO,CAAChE;oBAAU;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7J,OAAA;oBAAGwJ,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAExD,OAAO,CAACN;kBAAW;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE7J,OAAA;oBAAKwJ,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChEzJ,OAAA;sBAAMwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjCzJ,OAAA,CAACrB,QAAQ;wBAAC6K,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpC5D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,GAAG,GAAG+D,OAAO,CAAC/D,QAAQ,GAAG,GAAG,GAAG,CAAC+D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,EAAEkI,OAAO,CAAC,CAAC,CAAC,IAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACP7J,OAAA;sBAAMwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjCzJ,OAAA,CAACwK,YAAY;wBAAChB,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACxC5D,OAAO,CAACsB,SAAS,GAAG,CAAC,GAAG,IAAItB,OAAO,CAACsB,SAAS,GAAG,GAAG,GAAGtB,OAAO,CAACsB,SAAS,GAAG;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACP7J,OAAA;sBAAMwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjCzJ,OAAA,CAACjB,OAAO;wBAACyK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACnC5D,OAAO,CAACwE,QAAQ,EAAC,aACpB;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP7J,OAAA;sBAAMwJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjCzJ,OAAA,CAACZ,OAAO;wBAACoK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAC5B,EAAC5D,OAAO,CAACyE,UAAU;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArCD5D,OAAO,CAACS,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEApH,SAAS,KAAK,OAAO,iBACpBzC,OAAA;YAAKwJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzJ,OAAA;cAAKwJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDzJ,OAAA;gBAAIwJ,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7J,OAAA;gBAAMwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpC9G,WAAW,CAACiE,MAAM,EAAC,QAAM,EAACjE,WAAW,CAACiE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELlH,WAAW,CAACiE,MAAM,KAAK,CAAC,gBACvB5G,OAAA;cAAKwJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BzJ,OAAA,CAACf,UAAU;gBAACuK,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D7J,OAAA;gBAAGwJ,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9D7J,OAAA;gBAAGwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAEN7J,OAAA;cAAKwJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD9G,WAAW,CAACwH,GAAG,CAAC7E,KAAK,iBACpBtF,OAAA;gBAEE8J,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAACT,KAAK,CAAE;gBACxCkE,SAAS,EAAE,0DACT,CAAApI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,gBAEHzJ,OAAA;kBAAKwJ,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDzJ,OAAA;oBAAKwJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CzJ,OAAA;sBAAMwJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACrD,KAAK,CAACtD,IAAI;oBAAC;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D7J,OAAA;sBAAIwJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnE,KAAK,CAACE;oBAAI;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAClEvE,KAAK,CAAC8C,QAAQ,iBACbpI,OAAA;sBAAMwJ,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,EAAC;oBAE/E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN7J,OAAA;oBAAMwJ,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACpD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAwH,QAAA,EACnGnE,KAAK,CAACrD;kBAAU;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN7J,OAAA;kBAAKwJ,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxCnE,KAAK,CAACK;gBAAW;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEN7J,OAAA;kBAAKwJ,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3EzJ,OAAA;oBAAAyJ,QAAA,GAAOnE,KAAK,CAACpD,QAAQ,CAACkI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C7J,OAAA;oBAAKwJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EACzCnE,KAAK,CAACyD,OAAO,iBACZ/I,OAAA;sBAAAyJ,QAAA,GAAM,mBAAc,EAAC,IAAI/B,IAAI,CAACpC,KAAK,CAACyD,OAAO,CAAC,CAAC4B,kBAAkB,CAAC,CAAC;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBACzE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7J,OAAA;kBAAKwJ,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/EzJ,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC7D,KAAK,CAAE;oBACjCkE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1IzJ,OAAA,CAACb,MAAM;sBAACqK,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAET7J,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC1D,KAAK,CAACoB,EAAE,CAAE;oBACrC8C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3BzJ,OAAA,CAACf,UAAU;sBAACuK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,EAERvE,KAAK,CAAC8C,QAAQ,iBACbpI,OAAA;oBACE8J,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC5D,KAAK,CAACoB,EAAE,CAAE;oBAC3C8C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjBzJ,OAAA,CAACd,QAAQ;sBAACsK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/DDvE,KAAK,CAACoB,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEApH,SAAS,KAAK,MAAM,iBACnBzC,OAAA;YAAKwJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzJ,OAAA;cAAIwJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ,CAACnI,UAAU,gBACV1B,OAAA;cAAKwJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzJ,OAAA;gBACE8J,OAAO,EAAE7C,kBAAmB;gBAC5BuC,SAAS,EAAC,yHAAyH;gBAAAC,QAAA,gBAEnIzJ,OAAA,CAACnB,MAAM;kBAAC2K,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET7J,OAAA;gBAAKwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCzJ,OAAA;kBAAGwJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtC7J,OAAA;kBAAIwJ,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7CzJ,OAAA;oBAAAyJ,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChD7J,OAAA;oBAAAyJ,QAAA,EAAI;kBAA4C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrD7J,OAAA;oBAAAyJ,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChC7J,OAAA;oBAAAyJ,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN7J,OAAA;cAAKwJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzJ,OAAA;gBAAKwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCzJ,OAAA;kBAAGwJ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5D7J,OAAA;kBAAAyJ,QAAA,GAAG,sBAAiB,EAAC7H,cAAc,CAACgF,MAAM;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9CjI,cAAc,CAACgF,MAAM,IAAI,CAAC,iBACzB5G,OAAA;kBAAAyJ,QAAA,GAAG,aAAW,EAAC7B,sBAAsB,CAAChG,cAAc,CAAC,CAACwI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzJ,OAAA;kBACE8J,OAAO,EAAEtC,cAAe;kBACxBoD,QAAQ,EAAEhJ,cAAc,CAACgF,MAAM,GAAG,CAAE;kBACpC4C,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EACtJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7J,OAAA;kBACE8J,OAAO,EAAEA,CAAA,KAAM;oBACbnI,aAAa,CAAC,KAAK,CAAC;oBACpBE,iBAAiB,CAAC,EAAE,CAAC;kBACvB,CAAE;kBACF2H,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,EACnG;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEApH,SAAS,KAAK,MAAM,iBACnBzC,OAAA;YAAKwJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzJ,OAAA;cAAIwJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJzI,aAAa,iBACZpB,OAAA;cAAKwJ,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzJ,OAAA;gBAAGwJ,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ7J,OAAA;gBACE8J,OAAO,EAAEnD,aAAc;gBACvBiE,QAAQ,EAAEtJ,YAAY,CAACsF,MAAM,KAAK,CAAE;gBACpC4C,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,gBAE3JzJ,OAAA,CAAChB,QAAQ;kBAACwK,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BACZ,EAACvI,YAAY,CAACsF,MAAM,EAAC,QAC/C;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAED7J,OAAA;cAAKwJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDvI,IAAI,CAACiJ,GAAG,CAAC9D,GAAG,iBACXrG,OAAA;gBAEE8J,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACC,GAAG,CAAE;gBACpCmD,SAAS,EAAE,0DACTlI,YAAY,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,GACnC,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,eAEHzJ,OAAA;kBAAKwJ,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzJ,OAAA;oBAAMwJ,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEpD,GAAG,CAACwE;kBAAI;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C7J,OAAA;oBAAKwJ,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBzJ,OAAA;sBAAIwJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpD,GAAG,CAACb;oBAAI;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE7J,OAAA;sBAAGwJ,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEpD,GAAG,CAACV;oBAAW;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D7J,OAAA;sBAAKwJ,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCzJ,OAAA,CAAClB,MAAM;wBAAC0K,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD7J,OAAA;wBAAMwJ,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACrExD,GAAG,CAACyE,QAAQ,iBACX9K,OAAA;wBAAMwJ,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC9D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArBDxD,GAAG,CAACK,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7J,OAAA;UAAKwJ,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzJ,OAAA;YAAKwJ,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5DzJ,OAAA;cAAKwJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCzJ,OAAA,CAAC9B,YAAY;gBACX6M,MAAM,EAAE,CAACjK,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;gBAC7CuG,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAE;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBAEzCzJ,OAAA,CAAC7B,SAAS;kBACRiN,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,eAEF7J,OAAA,CAACkH,eAAe;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGnB7J,OAAA,CAAC5B,MAAM;kBAACgG,QAAQ,EAAE,CAACtD,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;kBAAAgF,QAAA,eACrDzJ,OAAA,CAAC3B,KAAK;oBAAAoL,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EAGRzI,aAAa,iBACZpB,OAAA,CAAC1B,QAAQ;kBACPgN,SAAS,EAAElK,aAAa,CAAC0F,MAAM,CAACqD,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACzD8G,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGArI,cAAc,iBACbxB,OAAA,CAAC1B,QAAQ;kBACPgN,SAAS,EAAE9J,cAAc,CAAC2I,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACnD8G,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAjI,cAAc,CAACuI,GAAG,CAAC,CAACuB,KAAK,EAAEC,KAAK,kBAC/B3L,OAAA,CAAC5B,MAAM;kBAAagG,QAAQ,EAAE,CAACsH,KAAK,CAACpH,GAAG,EAAEoH,KAAK,CAACjH,GAAG,CAAE;kBAAAgF,QAAA,eACnDzJ,OAAA,CAAC3B,KAAK;oBAAAoL,QAAA,GAAC,QAAM,EAACkC,KAAK,GAAG,CAAC;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC,GADrB8B,KAAK;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC,EAGDjI,cAAc,CAACgF,MAAM,GAAG,CAAC,iBACxB5G,OAAA,CAAC1B,QAAQ;kBACPgN,SAAS,EAAE1J,cAAc,CAACuI,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACnD8G,KAAK,EAAC,OAAO;kBACbC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAtH,QAAQ,IAAIrB,IAAI,CAACiJ,GAAG,CAAC9D,GAAG,iBACvBrG,OAAA,CAAC5B,MAAM;kBAELgG,QAAQ,EAAE,CAACiC,GAAG,CAAC/B,GAAG,EAAE+B,GAAG,CAAC5B,GAAG,CAAE;kBAC7BoG,IAAI,EAAErM,CAAC,CAACoN,OAAO,CAAC;oBACdC,IAAI,EAAE,2BAA2BxF,GAAG,CAACkF,KAAK,iJAAiJlF,GAAG,CAACwE,IAAI,QAAQ;oBAC3MrB,SAAS,EAAE,mBAAmB;oBAC9BsC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC,CAAE;kBAAArC,QAAA,eAEHzJ,OAAA,CAAC3B,KAAK;oBAAAoL,QAAA,eACJzJ,OAAA;sBAAAyJ,QAAA,gBACEzJ,OAAA;wBAAIwJ,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEpD,GAAG,CAACb;sBAAI;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3C7J,OAAA;wBAAGwJ,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACV;sBAAW;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1D7J,OAAA;wBAAKwJ,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCzJ,OAAA,CAAClB,MAAM;0BAAC0K,SAAS,EAAC;wBAA8B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACnD7J,OAAA;0BAAMwJ,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;wBAAC;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAjBHxD,GAAG,CAACK,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBL,CACT,CAAC,EAGDvI,YAAY,CAAC6I,GAAG,CAAC9D,GAAG,iBACnBrG,OAAA,CAAC5B,MAAM;kBAELgG,QAAQ,EAAE,CAACiC,GAAG,CAAC/B,GAAG,EAAE+B,GAAG,CAAC5B,GAAG,CAAE;kBAC7BoG,IAAI,EAAErM,CAAC,CAACoN,OAAO,CAAC;oBACdC,IAAI,EAAE,4OAA4OxF,GAAG,CAACwE,IAAI,QAAQ;oBAClQrB,SAAS,EAAE,4BAA4B;oBACvCsC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC;gBAAE,GANE,YAAYzF,GAAG,CAACK,EAAE,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAO1B,CACF,CAAC,EAGD,CAAC1G,YAAY,IAAIV,SAAS,KAAK,UAAU,KAAKM,QAAQ,CAACoH,GAAG,CAAClE,OAAO,iBACjEjG,OAAA,CAAC1B,QAAQ;kBAEPgN,SAAS,EAAErF,OAAO,CAACa,MAAM,CAACqD,GAAG,CAACuB,KAAK,IAAI,CAACA,KAAK,CAACpH,GAAG,EAAEoH,KAAK,CAACjH,GAAG,CAAC,CAAE;kBAC/D8G,KAAK,EAAE,CAAAtI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAAG,SAAS,GAC9CT,OAAO,CAACjE,IAAI,KAAK,OAAO,GAAG,SAAS,GACpCiE,OAAO,CAACjE,IAAI,KAAK,QAAQ,GAAG,SAAS,GACrCiE,OAAO,CAACjE,IAAI,KAAK,SAAS,GAAG,SAAS,GACtC,SAAU;kBACjBwJ,MAAM,EAAE,CAAAvI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAAG,CAAC,GAAG,CAAE;kBACnD+E,OAAO,EAAE,GAAI;kBACbM,aAAa,EAAE;oBACb5E,KAAK,EAAEA,CAAA,KAAMnB,mBAAmB,CAACC,OAAO;kBAC1C,CAAE;kBAAAwD,QAAA,eAEFzJ,OAAA,CAAC3B,KAAK;oBAAAoL,QAAA,eACJzJ,OAAA;sBAAKwJ,SAAS,EAAC,KAAK;sBAAAC,QAAA,gBAClBzJ,OAAA;wBAAIwJ,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAExD,OAAO,CAACT;sBAAI;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpE7J,OAAA;wBAAGwJ,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAExD,OAAO,CAACN;sBAAW;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnE7J,OAAA;wBAAKwJ,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,gBAC9CzJ,OAAA;0BAAAyJ,QAAA,GAAK,YAAU,EAACxD,OAAO,CAAC/D,QAAQ,GAAG,IAAI,GAAG,GAAG+D,OAAO,CAAC/D,QAAQ,GAAG,GAAG,GAAG,CAAC+D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,EAAEkI,OAAO,CAAC,CAAC,CAAC,IAAI;wBAAA;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACrH7J,OAAA;0BAAAyJ,QAAA,GAAK,kBAAU,EAACxD,OAAO,CAACsB,SAAS,GAAG,CAAC,GAAG,IAAItB,OAAO,CAACsB,SAAS,GAAG,GAAG,GAAGtB,OAAO,CAACsB,SAAS,GAAG;wBAAA;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACjG7J,OAAA;0BAAAyJ,QAAA,GAAK,UAAQ,EAACxD,OAAO,CAACyE,UAAU;wBAAA;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvC7J,OAAA;0BAAAyJ,QAAA,GAAK,cAAY,EAACxD,OAAO,CAACwE,QAAQ;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAxBH5D,OAAO,CAACS,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBP,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL5G,eAAe,iBACdjD,OAAA;QAAKwJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBzJ,OAAA,CAACF,YAAY;UACXiD,QAAQ,EAAEA,QAAS;UACnBE,eAAe,EAAEA,eAAgB;UACjC+I,eAAe,EAAEhG,mBAAoB;UACrClF,YAAY,EAAEA;QAAa;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnJ,GAAA,CAj6BID,MAAM;EAAA,QACOnB,OAAO;AAAA;AAAA2M,EAAA,GADpBxL,MAAM;AAm6BZ,eAAeA,MAAM;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}