{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Routes.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ModernMap from '../components/ModernMap';\nimport { FiMap, FiNavigation, FiMapPin, FiSearch, FiPlus, FiStar, FiUsers, FiTarget, FiBookmark, FiTrash2, FiPlay, FiClock, FiAlertCircle, FiTrendingUp, FiActivity } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generatePopularRoutes, generatePOIs, generateSegments, calculateDistance, optimizeRouteWithPOIs, DEFAULT_POSITION } from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\n\n// Configuration des icônes Leaflet\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\nconst Routes = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    user\n  } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const pos = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        };\n        setUserPosition(pos);\n        loadRoutesAndPOIs(pos);\n      }, error => {\n        console.warn('Géolocalisation échouée:', error);\n        setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n        loadRoutesAndPOIs(DEFAULT_POSITION);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      });\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n  const loadRoutesAndPOIs = position => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) || route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || filters.distance === 'short' && route.distance <= 5 || filters.distance === 'medium' && route.distance > 5 && route.distance <= 15 || filters.distance === 'long' && route.distance > 15;\n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n  const handleSegmentSelect = segment => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n  const handleFiltersChange = newFilters => {\n    setFilters(newFilters);\n  };\n  const handlePOIToggle = poi => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n  const MapClickHandler = () => {\n    _s();\n    useMapEvents({\n      click: e => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n  _s(MapClickHandler, \"Ld/tk8Iz8AdZhC1l7acENaOEoCo=\", true);\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n  const calculateTotalDistance = points => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n    }\n    return total;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const saveRoute = route => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = {\n        ...route,\n        savedAt: new Date().toISOString()\n      };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n  const unsaveRoute = routeId => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n  const deleteCustomRoute = routeId => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if ((selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n  const startRoute = route => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Cartes et Itin\\xE9raires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez de nouveaux parcours, planifiez vos itin\\xE9raires et explorez les points d'int\\xE9r\\xEAt autour de vous.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-yellow-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: \"Chargement des donn\\xE9es...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('discover'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'discover' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMap, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), \"D\\xE9couvrir\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('segments'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'segments' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), \"Segments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('saved'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'saved' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), \"Sauvegard\\xE9es (\", savedRoutes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('plan'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'plan' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiNavigation, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), \"Planifier\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('pois'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'pois' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), \"Points d'int\\xE9r\\xEAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [activeTab === 'discover' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes populaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPOIs(!showPOIs),\n                className: `px-3 py-1 rounded-md text-sm ${showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                children: \"POIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un parcours...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.type,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    type: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Tous types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"running\",\n                    children: \"Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"cycling\",\n                    children: \"V\\xE9lo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hiking\",\n                    children: \"Randonn\\xE9e\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"walking\",\n                    children: \"Marche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.difficulty,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    difficulty: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Toutes difficult\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 492,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 495,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.completions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 27\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this), savedRoutes.find(r => r.id === route.id) ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => saveRoute(route),\n                    className: \"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\",\n                    title: \"Sauvegarder\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), activeTab === 'segments' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(AdvancedFilters, {\n              filters: filters,\n              onFiltersChange: handleFiltersChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900\",\n                  children: \"Segments populaires\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowSegments(!showSegments),\n                  className: `px-3 py-1 rounded-md text-sm ${showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                  children: \"Afficher sur la carte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                children: segments.map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleSegmentSelect(segment),\n                  className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs font-medium ${segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' : segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' : segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' : 'bg-purple-100 text-purple-800'}`,\n                      children: segment.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 mb-3\",\n                    children: segment.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 29\n                      }, this), segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 29\n                      }, this), segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 29\n                      }, this), segment.attempts, \" tentatives\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 29\n                      }, this), \"Record: \", segment.recordTime]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this), activeTab === 'saved' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes sauvegard\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [savedRoutes.length, \" route\", savedRoutes.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), savedRoutes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mb-2\",\n                children: \"Aucune route sauvegard\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Sauvegardez vos routes pr\\xE9f\\xE9r\\xE9es pour les retrouver facilement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: savedRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 29\n                    }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\",\n                      children: \"Personnalis\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: route.savedAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Sauvegard\\xE9 le \", new Date(route.savedAt).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 29\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 25\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this), activeTab === 'plan' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Planificateur d'itin\\xE9raire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this), !isPlanning ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startRoutePlanning,\n                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this), \"Cr\\xE9er un nouveau parcours\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: \"Instructions :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Cr\\xE9er un nouveau parcours\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur la carte pour ajouter des points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Minimum 2 points requis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Terminer\\\" pour sauvegarder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium mb-2\",\n                  children: \"Mode planification actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Points ajout\\xE9s : \", planningPoints.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 23\n                }, this), planningPoints.length >= 2 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Distance : \", calculateTotalDistance(planningPoints).toFixed(1), \" km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: finishPlanning,\n                  disabled: planningPoints.length < 2,\n                  className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                  children: \"Terminer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setIsPlanning(false);\n                    setPlanningPoints([]);\n                  },\n                  className: \"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                  children: \"Annuler\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 15\n          }, this), activeTab === 'pois' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Points d'int\\xE9r\\xEAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this), selectedRoute && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700 mb-2\",\n                children: \"S\\xE9lectionnez des POIs pour optimiser votre parcours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: optimizeRoute,\n                disabled: selectedPOIs.length === 0,\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                  className: \"inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 23\n                }, this), \"Optimiser l'itin\\xE9raire (\", selectedPOIs.length, \" POIs)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 max-h-96 overflow-y-auto\",\n              children: pois.map(poi => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handlePOIToggle(poi),\n                className: `p-3 border rounded-lg cursor-pointer transition-colors ${selectedPOIs.find(p => p.id === poi.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: poi.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: poi.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: poi.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 807,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 809,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: poi.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 810,\n                        columnNumber: 29\n                      }, this), poi.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 text-xs text-green-600\",\n                        children: \"\\u2713 V\\xE9rifi\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 808,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 23\n                }, this)\n              }, poi.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-96 lg:h-[600px]\",\n              children: /*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [userPosition.lat, userPosition.lng],\n                zoom: 13,\n                style: {\n                  height: '100%',\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MapClickHandler, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [userPosition.lat, userPosition.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: \"Votre position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 19\n                }, this), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n                  color: \"blue\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 21\n                }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: optimizedRoute.map(p => [p.lat, p.lng]),\n                  color: \"red\",\n                  weight: 4,\n                  opacity: 0.8\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 21\n                }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [point.lat, point.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: [\"Point \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 21\n                }, this)), planningPoints.length > 1 && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: planningPoints.map(p => [p.lat, p.lng]),\n                  color: \"green\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                    className: 'custom-poi-marker',\n                    iconSize: [30, 30]\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: poi.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 895,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: poi.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 896,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                          className: \"h-3 w-3 text-yellow-400 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 898,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm\",\n                          children: poi.rating.toFixed(1)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 899,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 897,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 23\n                  }, this)\n                }, poi.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                    className: 'custom-selected-poi-marker',\n                    iconSize: [35, 35]\n                  })\n                }, `selected-${poi.id}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 21\n                }, this)), (showSegments || activeTab === 'segments') && segments.map(segment => /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: segment.points.map(point => [point.lat, point.lng]),\n                  color: (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? '#3B82F6' : segment.type === 'climb' ? '#EF4444' : segment.type === 'sprint' ? '#10B981' : segment.type === 'descent' ? '#F59E0B' : '#8B5CF6',\n                  weight: (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id ? 6 : 4,\n                  opacity: 0.8,\n                  eventHandlers: {\n                    click: () => handleSegmentSelect(segment)\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900 mb-1\",\n                        children: segment.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 mb-2\",\n                        children: segment.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 938,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-1 text-xs text-gray-500\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Distance: \", segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"D\\xE9nivel\\xE9: \", segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 941,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Record: \", segment.recordTime]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Tentatives: \", segment.attempts]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 23\n                  }, this)\n                }, segment.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), selectedSegment && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(SegmentPanel, {\n          segments: segments,\n          selectedSegment: selectedSegment,\n          onSegmentSelect: handleSegmentSelect,\n          userPosition: userPosition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 311,\n    columnNumber: 5\n  }, this);\n};\n_s2(Routes, \"/GAdmxt/KEQN28cZpn0Cj9L97BY=\", false, function () {\n  return [useAuth];\n});\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ModernMap", "FiMap", "FiNavigation", "FiMapPin", "FiSearch", "FiPlus", "FiStar", "FiUsers", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiTrash2", "FiPlay", "<PERSON><PERSON><PERSON>", "FiAlertCircle", "FiTrendingUp", "FiActivity", "useAuth", "generatePopularRoutes", "generatePOIs", "generateSegments", "calculateDistance", "optimizeRouteWithPOIs", "DEFAULT_POSITION", "AdvancedFilters", "SegmentPanel", "jsxDEV", "_jsxDEV", "L", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "Routes", "_s2", "_s", "$RefreshSig$", "user", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedPOIs", "setSelectedPOIs", "optimizedRoute", "setOptimizedRoute", "isPlanning", "setIsPlanning", "planningPoints", "setPlanningPoints", "filters", "setFilters", "type", "difficulty", "distance", "sport", "surface", "searchTerm", "setSearchTerm", "showPOIs", "setShowPOIs", "activeTab", "setActiveTab", "savedRoutes", "setSavedRoutes", "routeHistory", "setRouteHistory", "segments", "setSegments", "selectedSegment", "setSelectedSegment", "showSegments", "setShowSegments", "isLoading", "setIsLoading", "error", "setError", "saved", "JSON", "parse", "localStorage", "getItem", "history", "savedSegments", "console", "navigator", "geolocation", "getCurrentPosition", "position", "pos", "lat", "coords", "latitude", "lng", "longitude", "loadRoutesAndPOIs", "warn", "enableHighAccuracy", "timeout", "maximumAge", "popularRoutes", "nearbyPOIs", "nearbySegments", "allRoutes", "filteredRoutes", "filter", "route", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesType", "matchesDifficulty", "matchesDistance", "handleRouteSelect", "handleSegmentSelect", "segment", "handleFiltersChange", "newFilters", "handlePOIToggle", "poi", "prev", "isSelected", "find", "p", "id", "optimizeRoute", "length", "startPoint", "points", "endPoint", "optimized", "startRoutePlanning", "MapClickHandler", "useMapEvents", "click", "e", "newPoint", "latlng", "elevation", "finishPlanning", "newRoute", "Date", "now", "calculateTotalDistance", "rating", "completions", "created<PERSON>y", "firstName", "tags", "createdAt", "toISOString", "isCustom", "updatedSavedRoutes", "setItem", "stringify", "total", "i", "getDifficultyColor", "getTypeIcon", "saveRoute", "r", "routeToSave", "savedAt", "unsaveRoute", "routeId", "deleteCustomRoute", "startRoute", "historyEntry", "startedAt", "status", "updatedHistory", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "target", "map", "toFixed", "stopPropagation", "title", "onFiltersChange", "attempts", "recordTime", "toLocaleDateString", "disabled", "icon", "verified", "MapContainer", "center", "zoom", "style", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "attribution", "<PERSON><PERSON>", "Popup", "Polyline", "positions", "color", "weight", "opacity", "point", "index", "divIcon", "html", "iconSize", "eventHandlers", "onSegmentSelect", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Routes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ModernMap from '../components/ModernMap';\nimport {\n  FiMap,\n  FiNavigation,\n  FiMapPin,\n  FiSearch,\n  FiPlus,\n  FiStar,\n  FiUsers,\n  FiTarget,\n  FiBookmark,\n  FiTrash2,\n  FiPlay,\n  FiClock,\n  FiAlertCircle,\n  FiTrendingUp,\n  FiActivity\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generatePopularRoutes,\n  generatePOIs,\n  generateSegments,\n  calculateDistance,\n  optimizeRouteWithPOIs,\n  DEFAULT_POSITION\n} from '../utils/mapUtils';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport SegmentPanel from '../components/SegmentPanel';\n\n// Configuration des icônes Leaflet\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\nconst Routes = () => {\n  const { user } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const pos = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          };\n          setUserPosition(pos);\n          loadRoutesAndPOIs(pos);\n        },\n        (error) => {\n          console.warn('Géolocalisation échouée:', error);\n          setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n          loadRoutesAndPOIs(DEFAULT_POSITION);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 300000 // 5 minutes\n        }\n      );\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n\n  const loadRoutesAndPOIs = (position) => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n      const nearbySegments = generateSegments(position.lat, position.lng, 8);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setSegments(nearbySegments);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || \n                           (filters.distance === 'short' && route.distance <= 5) ||\n                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||\n                           (filters.distance === 'long' && route.distance > 15);\n    \n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n    setSelectedSegment(null);\n  };\n\n  const handleSegmentSelect = (segment) => {\n    setSelectedSegment(segment);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n\n  const handleFiltersChange = (newFilters) => {\n    setFilters(newFilters);\n  };\n\n  const handlePOIToggle = (poi) => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n\n  const MapClickHandler = () => {\n    useMapEvents({\n      click: (e) => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: user?.firstName || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n\n  const calculateTotalDistance = (points) => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(\n        points[i-1].lat, points[i-1].lng,\n        points[i].lat, points[i].lng\n      );\n    }\n    return total;\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const saveRoute = (route) => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = { ...route, savedAt: new Date().toISOString() };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n\n  const unsaveRoute = (routeId) => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n\n  const deleteCustomRoute = (routeId) => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if (selectedRoute?.id === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n\n  const startRoute = (route) => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Cartes et Itinéraires\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez\n            les points d'intérêt autour de vous.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                <p className=\"text-sm text-yellow-800\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Indicateur de chargement */}\n          {isLoading && (\n            <div className=\"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"></div>\n                <p className=\"text-sm text-blue-800\">Chargement des données...</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('discover')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'discover'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMap className=\"inline mr-2\" />\n            Découvrir\n          </button>\n          <button\n            onClick={() => setActiveTab('segments')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'segments'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Segments\n          </button>\n          <button\n            onClick={() => setActiveTab('saved')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'saved'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiBookmark className=\"inline mr-2\" />\n            Sauvegardées ({savedRoutes.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('plan')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'plan'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiNavigation className=\"inline mr-2\" />\n            Planifier\n          </button>\n          <button\n            onClick={() => setActiveTab('pois')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'pois'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMapPin className=\"inline mr-2\" />\n            Points d'intérêt\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {activeTab === 'discover' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes populaires\n                  </h2>\n                  <button\n                    onClick={() => setShowPOIs(!showPOIs)}\n                    className={`px-3 py-1 rounded-md text-sm ${\n                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    POIs\n                  </button>\n                </div>\n\n                {/* Search and Filters */}\n                <div className=\"space-y-4 mb-6\">\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un parcours...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <select\n                      value={filters.type}\n                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Tous types</option>\n                      <option value=\"running\">Course</option>\n                      <option value=\"cycling\">Vélo</option>\n                      <option value=\"hiking\">Randonnée</option>\n                      <option value=\"walking\">Marche</option>\n                    </select>\n\n                    <select\n                      value={filters.difficulty}\n                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Toutes difficultés</option>\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n\n                {/* Routes List */}\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {filteredRoutes.map(route => (\n                    <div\n                      key={route.id}\n                      onClick={() => handleRouteSelect(route)}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedRoute?.id === route.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                          <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                        </div>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                          {route.difficulty}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 mb-2\">\n                        {route.description}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                        <span>{route.distance.toFixed(1)} km</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span>{route.rating.toFixed(1)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <FiUsers className=\"h-3 w-3 mr-1\" />\n                            <span>{route.completions}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Boutons d'action */}\n                      <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                        <button\n                          onClick={() => startRoute(route)}\n                          className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                        >\n                          <FiPlay className=\"h-3 w-3 mr-1\" />\n                          Démarrer\n                        </button>\n\n                        {savedRoutes.find(r => r.id === route.id) ? (\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => saveRoute(route)}\n                            className=\"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\"\n                            title=\"Sauvegarder\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        )}\n\n                        {route.isCustom && (\n                          <button\n                            onClick={() => deleteCustomRoute(route.id)}\n                            className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                            title=\"Supprimer\"\n                          >\n                            <FiTrash2 className=\"h-3 w-3\" />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'segments' && (\n              <div className=\"space-y-6\">\n                {/* Filtres avancés */}\n                <AdvancedFilters\n                  filters={filters}\n                  onFiltersChange={handleFiltersChange}\n                />\n\n                {/* Liste des segments */}\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Segments populaires\n                    </h2>\n                    <button\n                      onClick={() => setShowSegments(!showSegments)}\n                      className={`px-3 py-1 rounded-md text-sm ${\n                        showSegments ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      Afficher sur la carte\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {segments.map(segment => (\n                      <div\n                        key={segment.id}\n                        onClick={() => handleSegmentSelect(segment)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedSegment?.id === segment.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            segment.difficulty === 'Facile' ? 'bg-green-100 text-green-800' :\n                            segment.difficulty === 'Modéré' ? 'bg-yellow-100 text-yellow-800' :\n                            segment.difficulty === 'Difficile' ? 'bg-red-100 text-red-800' :\n                            'bg-purple-100 text-purple-800'\n                          }`}>\n                            {segment.difficulty}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-600 mb-3\">{segment.description}</p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <FiMapPin className=\"h-4 w-4 mr-1\" />\n                            {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiTrendingUp className=\"h-4 w-4 mr-1\" />\n                            {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiUsers className=\"h-4 w-4 mr-1\" />\n                            {segment.attempts} tentatives\n                          </span>\n                          <span className=\"flex items-center\">\n                            <FiClock className=\"h-4 w-4 mr-1\" />\n                            Record: {segment.recordTime}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'saved' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes sauvegardées\n                  </h2>\n                  <span className=\"text-sm text-gray-500\">\n                    {savedRoutes.length} route{savedRoutes.length !== 1 ? 's' : ''}\n                  </span>\n                </div>\n\n                {savedRoutes.length === 0 ? (\n                  <div className=\"text-center py-8\">\n                    <FiBookmark className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                    <p className=\"text-gray-500 mb-2\">Aucune route sauvegardée</p>\n                    <p className=\"text-sm text-gray-400\">\n                      Sauvegardez vos routes préférées pour les retrouver facilement\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {savedRoutes.map(route => (\n                      <div\n                        key={route.id}\n                        onClick={() => handleRouteSelect(route)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedRoute?.id === route.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                            <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                            {route.isCustom && (\n                              <span className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\">\n                                Personnalisé\n                              </span>\n                            )}\n                          </div>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                            {route.difficulty}\n                          </span>\n                        </div>\n\n                        <div className=\"text-sm text-gray-600 mb-2\">\n                          {route.description}\n                        </div>\n\n                        <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                          <span>{route.distance.toFixed(1)} km</span>\n                          <div className=\"flex items-center space-x-2\">\n                            {route.savedAt && (\n                              <span>Sauvegardé le {new Date(route.savedAt).toLocaleDateString()}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Boutons d'action */}\n                        <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                          <button\n                            onClick={() => startRoute(route)}\n                            className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                          >\n                            <FiPlay className=\"h-3 w-3 mr-1\" />\n                            Démarrer\n                          </button>\n\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n\n                          {route.isCustom && (\n                            <button\n                              onClick={() => deleteCustomRoute(route.id)}\n                              className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                              title=\"Supprimer\"\n                            >\n                              <FiTrash2 className=\"h-3 w-3\" />\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'plan' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Planificateur d'itinéraire\n                </h2>\n                \n                {!isPlanning ? (\n                  <div className=\"space-y-4\">\n                    <button\n                      onClick={startRoutePlanning}\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n                    >\n                      <FiPlus className=\"mr-2\" />\n                      Créer un nouveau parcours\n                    </button>\n                    \n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"mb-2\">Instructions :</p>\n                      <ul className=\"list-disc list-inside space-y-1\">\n                        <li>Cliquez sur \"Créer un nouveau parcours\"</li>\n                        <li>Cliquez sur la carte pour ajouter des points</li>\n                        <li>Minimum 2 points requis</li>\n                        <li>Cliquez sur \"Terminer\" pour sauvegarder</li>\n                      </ul>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">Mode planification actif</p>\n                      <p>Points ajoutés : {planningPoints.length}</p>\n                      {planningPoints.length >= 2 && (\n                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={finishPlanning}\n                        disabled={planningPoints.length < 2}\n                        className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Terminer\n                      </button>\n                      <button\n                        onClick={() => {\n                          setIsPlanning(false);\n                          setPlanningPoints([]);\n                        }}\n                        className=\"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\"\n                      >\n                        Annuler\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'pois' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Points d'intérêt\n                </h2>\n                \n                {selectedRoute && (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <p className=\"text-sm text-blue-700 mb-2\">\n                      Sélectionnez des POIs pour optimiser votre parcours\n                    </p>\n                    <button\n                      onClick={optimizeRoute}\n                      disabled={selectedPOIs.length === 0}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\"\n                    >\n                      <FiTarget className=\"inline mr-2\" />\n                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)\n                    </button>\n                  </div>\n                )}\n                \n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n                  {pois.map(poi => (\n                    <div\n                      key={poi.id}\n                      onClick={() => handlePOIToggle(poi)}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedPOIs.find(p => p.id === poi.id)\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{poi.icon}</span>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{poi.name}</h4>\n                          <p className=\"text-xs text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-xs text-gray-500\">{poi.rating.toFixed(1)}</span>\n                            {poi.verified && (\n                              <span className=\"ml-2 text-xs text-green-600\">✓ Vérifié</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n              <div className=\"h-96 lg:h-[600px]\">\n                <MapContainer\n                  center={[userPosition.lat, userPosition.lng]}\n                  zoom={13}\n                  style={{ height: '100%', width: '100%' }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  \n                  <MapClickHandler />\n                  \n                  {/* User position */}\n                  <Marker position={[userPosition.lat, userPosition.lng]}>\n                    <Popup>Votre position</Popup>\n                  </Marker>\n                  \n                  {/* Selected route */}\n                  {selectedRoute && (\n                    <Polyline\n                      positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n                      color=\"blue\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* Optimized route */}\n                  {optimizedRoute && (\n                    <Polyline\n                      positions={optimizedRoute.map(p => [p.lat, p.lng])}\n                      color=\"red\"\n                      weight={4}\n                      opacity={0.8}\n                    />\n                  )}\n                  \n                  {/* Planning points */}\n                  {planningPoints.map((point, index) => (\n                    <Marker key={index} position={[point.lat, point.lng]}>\n                      <Popup>Point {index + 1}</Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Planning route */}\n                  {planningPoints.length > 1 && (\n                    <Polyline\n                      positions={planningPoints.map(p => [p.lat, p.lng])}\n                      color=\"green\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* POIs */}\n                  {showPOIs && pois.map(poi => (\n                    <Marker\n                      key={poi.id}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                        className: 'custom-poi-marker',\n                        iconSize: [30, 30]\n                      })}\n                    >\n                      <Popup>\n                        <div>\n                          <h4 className=\"font-medium\">{poi.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-sm\">{poi.rating.toFixed(1)}</span>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Selected POIs highlight */}\n                  {selectedPOIs.map(poi => (\n                    <Marker\n                      key={`selected-${poi.id}`}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                        className: 'custom-selected-poi-marker',\n                        iconSize: [35, 35]\n                      })}\n                    />\n                  ))}\n\n                  {/* Segments */}\n                  {(showSegments || activeTab === 'segments') && segments.map(segment => (\n                    <Polyline\n                      key={segment.id}\n                      positions={segment.points.map(point => [point.lat, point.lng])}\n                      color={selectedSegment?.id === segment.id ? '#3B82F6' :\n                             segment.type === 'climb' ? '#EF4444' :\n                             segment.type === 'sprint' ? '#10B981' :\n                             segment.type === 'descent' ? '#F59E0B' :\n                             '#8B5CF6'}\n                      weight={selectedSegment?.id === segment.id ? 6 : 4}\n                      opacity={0.8}\n                      eventHandlers={{\n                        click: () => handleSegmentSelect(segment)\n                      }}\n                    >\n                      <Popup>\n                        <div className=\"p-2\">\n                          <h3 className=\"font-semibold text-gray-900 mb-1\">{segment.name}</h3>\n                          <p className=\"text-sm text-gray-600 mb-2\">{segment.description}</p>\n                          <div className=\"space-y-1 text-xs text-gray-500\">\n                            <div>Distance: {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}</div>\n                            <div>Dénivelé: {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}</div>\n                            <div>Record: {segment.recordTime}</div>\n                            <div>Tentatives: {segment.attempts}</div>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Polyline>\n                  ))}\n                </MapContainer>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Panneau de segment sélectionné */}\n        {selectedSegment && (\n          <div className=\"mt-8\">\n            <SegmentPanel\n              segments={segments}\n              selectedSegment={selectedSegment}\n              onSegmentSelect={handleSegmentSelect}\n              userPosition={userPosition}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SACEC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,EACrBC,gBAAgB,QACX,mBAAmB;AAC1B,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,YAAY,MAAM,4BAA4B;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOC,CAAC,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CJ,CAAC,CAACC,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAACwB,gBAAgB,CAAC;EAClE,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC;IACrC6D,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoF,KAAK,EAAEC,QAAQ,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMqF,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACrE,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MACxE,MAAME,aAAa,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;MAC1EjB,cAAc,CAACa,KAAK,CAAC;MACrBX,eAAe,CAACgB,OAAO,CAAC;MACxBd,WAAW,CAACe,aAAa,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAENnF,SAAS,CAAC,MAAM;IACdkF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIS,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAMC,GAAG,GAAG;UACVC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG;QACvB,CAAC;QACD3D,eAAe,CAACsD,GAAG,CAAC;QACpBM,iBAAiB,CAACN,GAAG,CAAC;MACxB,CAAC,EACAd,KAAK,IAAK;QACTS,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAErB,KAAK,CAAC;QAC/CC,QAAQ,CAAC,8EAA8E,CAAC;QACxFmB,iBAAiB,CAAChF,gBAAgB,CAAC;MACrC,CAAC,EACD;QACEkF,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,MAAM,CAAC;MACrB,CACF,CAAC;IACH,CAAC,MAAM;MACLvB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DmB,iBAAiB,CAAChF,gBAAgB,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgF,iBAAiB,GAAIP,QAAQ,IAAK;IACtC,IAAI;MACF,MAAMY,aAAa,GAAG1F,qBAAqB,CAAC8E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC3E,MAAMQ,UAAU,GAAG1F,YAAY,CAAC6E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC/D,MAAMS,cAAc,GAAG1F,gBAAgB,CAAC4E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,CAAC,CAAC;;MAEtE;MACA,MAAMU,SAAS,GAAG,CAAC,GAAGxC,WAAW,EAAE,GAAGqC,aAAa,CAAC;MACpD/D,SAAS,CAACkE,SAAS,CAAC;MACpBhE,OAAO,CAAC8D,UAAU,CAAC;MACnBjC,WAAW,CAACkC,cAAc,CAAC;MAC3B5B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM8B,cAAc,GAAGpE,MAAM,CAACqE,MAAM,CAACC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGD,KAAK,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC5DH,KAAK,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,WAAW,GAAG9D,OAAO,CAACE,IAAI,KAAK,KAAK,IAAIsD,KAAK,CAACtD,IAAI,KAAKF,OAAO,CAACE,IAAI;IACzE,MAAM6D,iBAAiB,GAAG/D,OAAO,CAACG,UAAU,KAAK,KAAK,IAAIqD,KAAK,CAACrD,UAAU,KAAKH,OAAO,CAACG,UAAU;IACjG,MAAM6D,eAAe,GAAGhE,OAAO,CAACI,QAAQ,KAAK,KAAK,IAC1BJ,OAAO,CAACI,QAAQ,KAAK,OAAO,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,CAAE,IACpDJ,OAAO,CAACI,QAAQ,KAAK,QAAQ,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,CAAC,IAAIoD,KAAK,CAACpD,QAAQ,IAAI,EAAG,IAC5EJ,OAAO,CAACI,QAAQ,KAAK,MAAM,IAAIoD,KAAK,CAACpD,QAAQ,GAAG,EAAG;IAE3E,OAAOqD,aAAa,IAAIK,WAAW,IAAIC,iBAAiB,IAAIC,eAAe;EAC7E,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnCjE,gBAAgB,CAACiE,KAAK,CAAC;IACvB7D,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;IACnB2B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8C,mBAAmB,GAAIC,OAAO,IAAK;IACvC/C,kBAAkB,CAAC+C,OAAO,CAAC;IAC3B5E,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM2E,mBAAmB,GAAIC,UAAU,IAAK;IAC1CpE,UAAU,CAACoE,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,eAAe,GAAIC,GAAG,IAAK;IAC/B9E,eAAe,CAAC+E,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAClD,IAAIH,UAAU,EAAE;QACd,OAAOD,IAAI,CAACjB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGJ,IAAI,EAAED,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIvF,aAAa,IAAIE,YAAY,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMC,UAAU,GAAGzF,aAAa,CAAC0F,MAAM,CAAC,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG3F,aAAa,CAAC0F,MAAM,CAAC1F,aAAa,CAAC0F,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMI,SAAS,GAAGtH,qBAAqB,CAACmH,UAAU,EAAEE,QAAQ,EAAEzF,YAAY,CAAC;MAC3EG,iBAAiB,CAACuF,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtF,aAAa,CAAC,IAAI,CAAC;IACnBE,iBAAiB,CAAC,EAAE,CAAC;IACrBR,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyF,eAAe,GAAGA,CAAA,KAAM;IAAAvG,EAAA;IAC5BwG,YAAY,CAAC;MACXC,KAAK,EAAGC,CAAC,IAAK;QACZ,IAAI3F,UAAU,EAAE;UACd,MAAM4F,QAAQ,GAAG;YACfhD,GAAG,EAAE+C,CAAC,CAACE,MAAM,CAACjD,GAAG;YACjBG,GAAG,EAAE4C,CAAC,CAACE,MAAM,CAAC9C,GAAG;YACjB+C,SAAS,EAAE,GAAG,CAAC;UACjB,CAAC;UACD3F,iBAAiB,CAACyE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,QAAQ,CAAC,CAAC;QAChD;MACF;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAAC3G,EAAA,CAdIuG,eAAe;EAgBrB,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI7F,cAAc,CAACgF,MAAM,IAAI,CAAC,EAAE;MAC9B,MAAMc,QAAQ,GAAG;QACfhB,EAAE,EAAE,UAAUiB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1BpC,IAAI,EAAE,2BAA2B;QACjCxD,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAE2F,sBAAsB,CAACjG,cAAc,CAAC;QAChDkF,MAAM,EAAElF,cAAc;QACtBK,UAAU,EAAE,QAAQ;QACpB6F,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAAnH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoH,SAAS,KAAI,KAAK;QACnCC,IAAI,EAAE,CAAC,QAAQ,CAAC;QAChBvC,WAAW,EAAE,kCAAkC;QAC/CwC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,kBAAkB,GAAG,CAACZ,QAAQ,EAAE,GAAG/E,WAAW,CAAC;MACrDC,cAAc,CAAC0F,kBAAkB,CAAC;MAClC1E,YAAY,CAAC2E,OAAO,CAAC,aAAa,EAAE7E,IAAI,CAAC8E,SAAS,CAACF,kBAAkB,CAAC,CAAC;MAEvErH,SAAS,CAACqF,IAAI,IAAI,CAACoB,QAAQ,EAAE,GAAGpB,IAAI,CAAC,CAAC;MACtCjF,gBAAgB,CAACqG,QAAQ,CAAC;MAC1B/F,aAAa,CAAC,KAAK,CAAC;MACpBE,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAMgG,sBAAsB,GAAIf,MAAM,IAAK;IACzC,IAAI2B,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,MAAM,CAACF,MAAM,EAAE8B,CAAC,EAAE,EAAE;MACtCD,KAAK,IAAIhJ,iBAAiB,CACxBqH,MAAM,CAAC4B,CAAC,GAAC,CAAC,CAAC,CAACpE,GAAG,EAAEwC,MAAM,CAAC4B,CAAC,GAAC,CAAC,CAAC,CAACjE,GAAG,EAChCqC,MAAM,CAAC4B,CAAC,CAAC,CAACpE,GAAG,EAAEwC,MAAM,CAAC4B,CAAC,CAAC,CAACjE,GAC3B,CAAC;IACH;IACA,OAAOgE,KAAK;EACd,CAAC;EAED,MAAME,kBAAkB,GAAI1G,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAM2G,WAAW,GAAI5G,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAM6G,SAAS,GAAIvD,KAAK,IAAK;IAC3B,IAAI,CAAC3C,WAAW,CAAC6D,IAAI,CAACsC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,EAAE;MAC7C,MAAMqC,WAAW,GAAG;QAAE,GAAGzD,KAAK;QAAE0D,OAAO,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MAAE,CAAC;MACnE,MAAME,kBAAkB,GAAG,CAACS,WAAW,EAAE,GAAGpG,WAAW,CAAC;MACxDC,cAAc,CAAC0F,kBAAkB,CAAC;MAClC1E,YAAY,CAAC2E,OAAO,CAAC,aAAa,EAAE7E,IAAI,CAAC8E,SAAS,CAACF,kBAAkB,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMW,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMZ,kBAAkB,GAAG3F,WAAW,CAAC0C,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKwC,OAAO,CAAC;IACpEtG,cAAc,CAAC0F,kBAAkB,CAAC;IAClC1E,YAAY,CAAC2E,OAAO,CAAC,aAAa,EAAE7E,IAAI,CAAC8E,SAAS,CAACF,kBAAkB,CAAC,CAAC;EACzE,CAAC;EAED,MAAMa,iBAAiB,GAAID,OAAO,IAAK;IACrC;IACA,MAAMZ,kBAAkB,GAAG3F,WAAW,CAAC0C,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKwC,OAAO,CAAC;IACpEtG,cAAc,CAAC0F,kBAAkB,CAAC;IAClC1E,YAAY,CAAC2E,OAAO,CAAC,aAAa,EAAE7E,IAAI,CAAC8E,SAAS,CAACF,kBAAkB,CAAC,CAAC;;IAEvE;IACArH,SAAS,CAACqF,IAAI,IAAIA,IAAI,CAACjB,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKwC,OAAO,CAAC,CAAC;;IAErD;IACA,IAAI,CAAA9H,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKwC,OAAO,EAAE;MACjC7H,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM+H,UAAU,GAAI9D,KAAK,IAAK;IAC5B,MAAM+D,YAAY,GAAG;MACnB3C,EAAE,EAAEiB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdtC,KAAK,EAAEA,KAAK;MACZgE,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCmB,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,cAAc,GAAG,CAACH,YAAY,EAAE,GAAGxG,YAAY,CAAC;IACtDC,eAAe,CAAC0G,cAAc,CAAC;IAC/B5F,YAAY,CAAC2E,OAAO,CAAC,cAAc,EAAE7E,IAAI,CAAC8E,SAAS,CAACgB,cAAc,CAAC,CAAC;EACtE,CAAC;EAED,oBACEzJ,OAAA;IAAK0J,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC3J,OAAA;MAAK0J,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D3J,OAAA;QAAK0J,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B3J,OAAA;UAAI0J,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/J,OAAA;UAAG0J,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHvG,KAAK,iBACJxD,OAAA;UAAK0J,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzF3J,OAAA;YAAK0J,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3J,OAAA,CAACb,aAAa;cAACuK,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D/J,OAAA;cAAG0J,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEnG;YAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAzG,SAAS,iBACRtD,OAAA;UAAK0J,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrF3J,OAAA;YAAK0J,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C3J,OAAA;cAAK0J,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzF/J,OAAA;cAAG0J,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/J,OAAA;QAAK0J,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D3J,OAAA;UACEgK,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,UAAU,CAAE;UACxC+G,SAAS,EAAE,qEACThH,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAiH,QAAA,gBAEH3J,OAAA,CAACzB,KAAK;YAACmL,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/J,OAAA;UACEgK,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,UAAU,CAAE;UACxC+G,SAAS,EAAE,qEACThH,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAiH,QAAA,gBAEH3J,OAAA,CAAClB,QAAQ;YAAC4K,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/J,OAAA;UACEgK,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,OAAO,CAAE;UACrC+G,SAAS,EAAE,qEACThH,SAAS,KAAK,OAAO,GACjB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAiH,QAAA,gBAEH3J,OAAA,CAACjB,UAAU;YAAC2K,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACxB,EAACnH,WAAW,CAACiE,MAAM,EAAC,GACpC;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/J,OAAA;UACEgK,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,MAAM,CAAE;UACpC+G,SAAS,EAAE,qEACThH,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAiH,QAAA,gBAEH3J,OAAA,CAACxB,YAAY;YAACkL,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/J,OAAA;UACEgK,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,MAAM,CAAE;UACpC+G,SAAS,EAAE,qEACThH,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAiH,QAAA,gBAEH3J,OAAA,CAACvB,QAAQ;YAACiL,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/J,OAAA;QAAK0J,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3J,OAAA;UAAK0J,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BjH,SAAS,KAAK,UAAU,iBACvB1C,OAAA;YAAK0J,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3J,OAAA;cAAK0J,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD3J,OAAA;gBAAI0J,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/J,OAAA;gBACEgK,OAAO,EAAEA,CAAA,KAAMvH,WAAW,CAAC,CAACD,QAAQ,CAAE;gBACtCkH,SAAS,EAAE,gCACTlH,QAAQ,GAAG,2BAA2B,GAAG,2BAA2B,EACnE;gBAAAmH,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN/J,OAAA;cAAK0J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3J,OAAA;gBAAK0J,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB3J,OAAA,CAACtB,QAAQ;kBAACgL,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpE/J,OAAA;kBACEiC,IAAI,EAAC,MAAM;kBACXgI,WAAW,EAAC,2BAA2B;kBACvCC,KAAK,EAAE5H,UAAW;kBAClB6H,QAAQ,EAAG7C,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAA8G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC3J,OAAA;kBACEkK,KAAK,EAAEnI,OAAO,CAACE,IAAK;kBACpBkI,QAAQ,EAAG7C,CAAC,IAAKtF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtE,IAAI,EAAEqF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACzER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH3J,OAAA;oBAAQkK,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC/J,OAAA;oBAAQkK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC/J,OAAA;oBAAQkK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC/J,OAAA;oBAAQkK,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzC/J,OAAA;oBAAQkK,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAET/J,OAAA;kBACEkK,KAAK,EAAEnI,OAAO,CAACG,UAAW;kBAC1BiI,QAAQ,EAAG7C,CAAC,IAAKtF,UAAU,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErE,UAAU,EAAEoF,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/ER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH3J,OAAA;oBAAQkK,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/C/J,OAAA;oBAAQkK,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC/J,OAAA;oBAAQkK,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC/J,OAAA;oBAAQkK,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/J,OAAA;cAAK0J,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDtE,cAAc,CAACgF,GAAG,CAAC9E,KAAK,iBACvBvF,OAAA;gBAEEgK,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAACT,KAAK,CAAE;gBACxCmE,SAAS,EAAE,0DACT,CAAArI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAAgD,QAAA,gBAEH3J,OAAA;kBAAK0J,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD3J,OAAA;oBAAK0J,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3J,OAAA;sBAAM0J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACtD,KAAK,CAACtD,IAAI;oBAAC;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D/J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpE,KAAK,CAACE;oBAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN/J,OAAA;oBAAM0J,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACrD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAyH,QAAA,EACnGpE,KAAK,CAACrD;kBAAU;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN/J,OAAA;kBAAK0J,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxCpE,KAAK,CAACK;gBAAW;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEN/J,OAAA;kBAAK0J,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3E3J,OAAA;oBAAA2J,QAAA,GAAOpE,KAAK,CAACpD,QAAQ,CAACmI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C/J,OAAA;oBAAK0J,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3J,OAAA;sBAAK0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC3J,OAAA,CAACpB,MAAM;wBAAC8K,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD/J,OAAA;wBAAA2J,QAAA,EAAOpE,KAAK,CAACwC,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN/J,OAAA;sBAAK0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC3J,OAAA,CAACnB,OAAO;wBAAC6K,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpC/J,OAAA;wBAAA2J,QAAA,EAAOpE,KAAK,CAACyC;sBAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/J,OAAA;kBAAK0J,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/E3J,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC9D,KAAK,CAAE;oBACjCmE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I3J,OAAA,CAACf,MAAM;sBAACyK,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERnH,WAAW,CAAC6D,IAAI,CAACsC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKpB,KAAK,CAACoB,EAAE,CAAC,gBACvC3G,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC3D,KAAK,CAACoB,EAAE,CAAE;oBACrC+C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3B3J,OAAA,CAACjB,UAAU;sBAAC2K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,gBAET/J,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMlB,SAAS,CAACvD,KAAK,CAAE;oBAChCmE,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,aAAa;oBAAAb,QAAA,eAEnB3J,OAAA,CAACjB,UAAU;sBAAC2K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACT,EAEAxE,KAAK,CAAC+C,QAAQ,iBACbtI,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC7D,KAAK,CAACoB,EAAE,CAAE;oBAC3C+C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjB3J,OAAA,CAAChB,QAAQ;sBAAC0K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzEDxE,KAAK,CAACoB,EAAE;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0EV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEArH,SAAS,KAAK,UAAU,iBACvB1C,OAAA;YAAK0J,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB3J,OAAA,CAACH,eAAe;cACdkC,OAAO,EAAEA,OAAQ;cACjB0I,eAAe,EAAEtE;YAAoB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAGF/J,OAAA;cAAK0J,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3J,OAAA;gBAAK0J,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD3J,OAAA;kBAAI0J,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAEpD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/J,OAAA;kBACEgK,OAAO,EAAEA,CAAA,KAAM3G,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CsG,SAAS,EAAE,gCACTtG,YAAY,GAAG,2BAA2B,GAAG,2BAA2B,EACvE;kBAAAuG,QAAA,EACJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChD3G,QAAQ,CAACqH,GAAG,CAACnE,OAAO,iBACnBlG,OAAA;kBAEEgK,OAAO,EAAEA,CAAA,KAAM/D,mBAAmB,CAACC,OAAO,CAAE;kBAC5CwD,SAAS,EAAE,0DACT,CAAAxG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAC9B,4BAA4B,GAC5B,uCAAuC,EAC1C;kBAAAgD,QAAA,gBAEH3J,OAAA;oBAAK0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD3J,OAAA;sBAAI0J,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAEzD,OAAO,CAACT;oBAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/D/J,OAAA;sBAAM0J,SAAS,EAAE,8CACfxD,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,6BAA6B,GAC/DgE,OAAO,CAAChE,UAAU,KAAK,QAAQ,GAAG,+BAA+B,GACjEgE,OAAO,CAAChE,UAAU,KAAK,WAAW,GAAG,yBAAyB,GAC9D,+BAA+B,EAC9B;sBAAAyH,QAAA,EACAzD,OAAO,CAAChE;oBAAU;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/J,OAAA;oBAAG0J,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEzD,OAAO,CAACN;kBAAW;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE/J,OAAA;oBAAK0J,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChE3J,OAAA;sBAAM0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC3J,OAAA,CAACvB,QAAQ;wBAACiL,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpC7D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,GAAG,GAAG+D,OAAO,CAAC/D,QAAQ,GAAG,GAAG,GAAG,CAAC+D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,EAAEmI,OAAO,CAAC,CAAC,CAAC,IAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACP/J,OAAA;sBAAM0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC3J,OAAA,CAACZ,YAAY;wBAACsK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACxC7D,OAAO,CAACuB,SAAS,GAAG,CAAC,GAAG,IAAIvB,OAAO,CAACuB,SAAS,GAAG,GAAG,GAAGvB,OAAO,CAACuB,SAAS,GAAG;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACP/J,OAAA;sBAAM0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC3J,OAAA,CAACnB,OAAO;wBAAC6K,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACnC7D,OAAO,CAACwE,QAAQ,EAAC,aACpB;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP/J,OAAA;sBAAM0J,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBACjC3J,OAAA,CAACd,OAAO;wBAACwK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAC5B,EAAC7D,OAAO,CAACyE,UAAU;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArCD7D,OAAO,CAACS,EAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEArH,SAAS,KAAK,OAAO,iBACpB1C,OAAA;YAAK0J,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3J,OAAA;cAAK0J,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD3J,OAAA;gBAAI0J,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/J,OAAA;gBAAM0J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpC/G,WAAW,CAACiE,MAAM,EAAC,QAAM,EAACjE,WAAW,CAACiE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELnH,WAAW,CAACiE,MAAM,KAAK,CAAC,gBACvB7G,OAAA;cAAK0J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3J,OAAA,CAACjB,UAAU;gBAAC2K,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D/J,OAAA;gBAAG0J,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9D/J,OAAA;gBAAG0J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAEN/J,OAAA;cAAK0J,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD/G,WAAW,CAACyH,GAAG,CAAC9E,KAAK,iBACpBvF,OAAA;gBAEEgK,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAACT,KAAK,CAAE;gBACxCmE,SAAS,EAAE,0DACT,CAAArI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,EAAE,MAAKpB,KAAK,CAACoB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAAgD,QAAA,gBAEH3J,OAAA;kBAAK0J,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD3J,OAAA;oBAAK0J,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3J,OAAA;sBAAM0J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACtD,KAAK,CAACtD,IAAI;oBAAC;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D/J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpE,KAAK,CAACE;oBAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAClExE,KAAK,CAAC+C,QAAQ,iBACbtI,OAAA;sBAAM0J,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,EAAC;oBAE/E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN/J,OAAA;oBAAM0J,SAAS,EAAE,8CAA8Cd,kBAAkB,CAACrD,KAAK,CAACrD,UAAU,CAAC,EAAG;oBAAAyH,QAAA,EACnGpE,KAAK,CAACrD;kBAAU;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN/J,OAAA;kBAAK0J,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxCpE,KAAK,CAACK;gBAAW;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEN/J,OAAA;kBAAK0J,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3E3J,OAAA;oBAAA2J,QAAA,GAAOpE,KAAK,CAACpD,QAAQ,CAACmI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C/J,OAAA;oBAAK0J,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EACzCpE,KAAK,CAAC0D,OAAO,iBACZjJ,OAAA;sBAAA2J,QAAA,GAAM,mBAAc,EAAC,IAAI/B,IAAI,CAACrC,KAAK,CAAC0D,OAAO,CAAC,CAAC2B,kBAAkB,CAAC,CAAC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBACzE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/J,OAAA;kBAAK0J,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/E3J,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAAC9D,KAAK,CAAE;oBACjCmE,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I3J,OAAA,CAACf,MAAM;sBAACyK,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAET/J,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC3D,KAAK,CAACoB,EAAE,CAAE;oBACrC+C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3B3J,OAAA,CAACjB,UAAU;sBAAC2K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,EAERxE,KAAK,CAAC+C,QAAQ,iBACbtI,OAAA;oBACEgK,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC7D,KAAK,CAACoB,EAAE,CAAE;oBAC3C+C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjB3J,OAAA,CAAChB,QAAQ;sBAAC0K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/DDxE,KAAK,CAACoB,EAAE;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEArH,SAAS,KAAK,MAAM,iBACnB1C,OAAA;YAAK0J,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3J,OAAA;cAAI0J,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ,CAACpI,UAAU,gBACV3B,OAAA;cAAK0J,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3J,OAAA;gBACEgK,OAAO,EAAE9C,kBAAmB;gBAC5BwC,SAAS,EAAC,yHAAyH;gBAAAC,QAAA,gBAEnI3J,OAAA,CAACrB,MAAM;kBAAC+K,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET/J,OAAA;gBAAK0J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC3J,OAAA;kBAAG0J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtC/J,OAAA;kBAAI0J,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7C3J,OAAA;oBAAA2J,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChD/J,OAAA;oBAAA2J,QAAA,EAAI;kBAA4C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrD/J,OAAA;oBAAA2J,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChC/J,OAAA;oBAAA2J,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/J,OAAA;cAAK0J,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3J,OAAA;gBAAK0J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC3J,OAAA;kBAAG0J,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5D/J,OAAA;kBAAA2J,QAAA,GAAG,sBAAiB,EAAC9H,cAAc,CAACgF,MAAM;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9ClI,cAAc,CAACgF,MAAM,IAAI,CAAC,iBACzB7G,OAAA;kBAAA2J,QAAA,GAAG,aAAW,EAAC7B,sBAAsB,CAACjG,cAAc,CAAC,CAACyI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B3J,OAAA;kBACEgK,OAAO,EAAEtC,cAAe;kBACxBmD,QAAQ,EAAEhJ,cAAc,CAACgF,MAAM,GAAG,CAAE;kBACpC6C,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EACtJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/J,OAAA;kBACEgK,OAAO,EAAEA,CAAA,KAAM;oBACbpI,aAAa,CAAC,KAAK,CAAC;oBACpBE,iBAAiB,CAAC,EAAE,CAAC;kBACvB,CAAE;kBACF4H,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,EACnG;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEArH,SAAS,KAAK,MAAM,iBACnB1C,OAAA;YAAK0J,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3J,OAAA;cAAI0J,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ1I,aAAa,iBACZrB,OAAA;cAAK0J,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C3J,OAAA;gBAAG0J,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/J,OAAA;gBACEgK,OAAO,EAAEpD,aAAc;gBACvBiE,QAAQ,EAAEtJ,YAAY,CAACsF,MAAM,KAAK,CAAE;gBACpC6C,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,gBAE3J3J,OAAA,CAAClB,QAAQ;kBAAC4K,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BACZ,EAACxI,YAAY,CAACsF,MAAM,EAAC,QAC/C;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAED/J,OAAA;cAAK0J,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDxI,IAAI,CAACkJ,GAAG,CAAC/D,GAAG,iBACXtG,OAAA;gBAEEgK,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACC,GAAG,CAAE;gBACpCoD,SAAS,EAAE,0DACTnI,YAAY,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,GACnC,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAAgD,QAAA,eAEH3J,OAAA;kBAAK0J,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3J,OAAA;oBAAM0J,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAErD,GAAG,CAACwE;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C/J,OAAA;oBAAK0J,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAErD,GAAG,CAACb;oBAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE/J,OAAA;sBAAG0J,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAErD,GAAG,CAACV;oBAAW;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D/J,OAAA;sBAAK0J,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrC3J,OAAA,CAACpB,MAAM;wBAAC8K,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD/J,OAAA;wBAAM0J,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAErD,GAAG,CAACyB,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACrEzD,GAAG,CAACyE,QAAQ,iBACX/K,OAAA;wBAAM0J,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC9D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArBDzD,GAAG,CAACK,EAAE;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/J,OAAA;UAAK0J,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B3J,OAAA;YAAK0J,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D3J,OAAA;cAAK0J,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC3J,OAAA,CAACgL,YAAY;gBACXC,MAAM,EAAE,CAAClK,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;gBAC7CwG,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAE;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBAEzC3J,OAAA,CAACsL,SAAS;kBACRC,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,eAEF/J,OAAA,CAACmH,eAAe;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGnB/J,OAAA,CAACyL,MAAM;kBAACpH,QAAQ,EAAE,CAACtD,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;kBAAAiF,QAAA,eACrD3J,OAAA,CAAC0L,KAAK;oBAAA/B,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EAGR1I,aAAa,iBACZrB,OAAA,CAAC2L,QAAQ;kBACPC,SAAS,EAAEvK,aAAa,CAAC0F,MAAM,CAACsD,GAAG,CAAC3D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACzDmH,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAtI,cAAc,iBACbzB,OAAA,CAAC2L,QAAQ;kBACPC,SAAS,EAAEnK,cAAc,CAAC4I,GAAG,CAAC3D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACnDmH,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAlI,cAAc,CAACwI,GAAG,CAAC,CAAC2B,KAAK,EAAEC,KAAK,kBAC/BjM,OAAA,CAACyL,MAAM;kBAAapH,QAAQ,EAAE,CAAC2H,KAAK,CAACzH,GAAG,EAAEyH,KAAK,CAACtH,GAAG,CAAE;kBAAAiF,QAAA,eACnD3J,OAAA,CAAC0L,KAAK;oBAAA/B,QAAA,GAAC,QAAM,EAACsC,KAAK,GAAG,CAAC;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC,GADrBkC,KAAK;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC,EAGDlI,cAAc,CAACgF,MAAM,GAAG,CAAC,iBACxB7G,OAAA,CAAC2L,QAAQ;kBACPC,SAAS,EAAE/J,cAAc,CAACwI,GAAG,CAAC3D,CAAC,IAAI,CAACA,CAAC,CAACnC,GAAG,EAAEmC,CAAC,CAAChC,GAAG,CAAC,CAAE;kBACnDmH,KAAK,EAAC,OAAO;kBACbC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAvH,QAAQ,IAAIrB,IAAI,CAACkJ,GAAG,CAAC/D,GAAG,iBACvBtG,OAAA,CAACyL,MAAM;kBAELpH,QAAQ,EAAE,CAACiC,GAAG,CAAC/B,GAAG,EAAE+B,GAAG,CAAC5B,GAAG,CAAE;kBAC7BoG,IAAI,EAAE7K,CAAC,CAACiM,OAAO,CAAC;oBACdC,IAAI,EAAE,2BAA2B7F,GAAG,CAACuF,KAAK,iJAAiJvF,GAAG,CAACwE,IAAI,QAAQ;oBAC3MpB,SAAS,EAAE,mBAAmB;oBAC9B0C,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC,CAAE;kBAAAzC,QAAA,eAEH3J,OAAA,CAAC0L,KAAK;oBAAA/B,QAAA,eACJ3J,OAAA;sBAAA2J,QAAA,gBACE3J,OAAA;wBAAI0J,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAErD,GAAG,CAACb;sBAAI;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3C/J,OAAA;wBAAG0J,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAErD,GAAG,CAACV;sBAAW;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1D/J,OAAA;wBAAK0J,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrC3J,OAAA,CAACpB,MAAM;0BAAC8K,SAAS,EAAC;wBAA8B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACnD/J,OAAA;0BAAM0J,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAErD,GAAG,CAACyB,MAAM,CAACuC,OAAO,CAAC,CAAC;wBAAC;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAjBHzD,GAAG,CAACK,EAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBL,CACT,CAAC,EAGDxI,YAAY,CAAC8I,GAAG,CAAC/D,GAAG,iBACnBtG,OAAA,CAACyL,MAAM;kBAELpH,QAAQ,EAAE,CAACiC,GAAG,CAAC/B,GAAG,EAAE+B,GAAG,CAAC5B,GAAG,CAAE;kBAC7BoG,IAAI,EAAE7K,CAAC,CAACiM,OAAO,CAAC;oBACdC,IAAI,EAAE,4OAA4O7F,GAAG,CAACwE,IAAI,QAAQ;oBAClQpB,SAAS,EAAE,4BAA4B;oBACvC0C,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC;gBAAE,GANE,YAAY9F,GAAG,CAACK,EAAE,EAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAO1B,CACF,CAAC,EAGD,CAAC3G,YAAY,IAAIV,SAAS,KAAK,UAAU,KAAKM,QAAQ,CAACqH,GAAG,CAACnE,OAAO,iBACjElG,OAAA,CAAC2L,QAAQ;kBAEPC,SAAS,EAAE1F,OAAO,CAACa,MAAM,CAACsD,GAAG,CAAC2B,KAAK,IAAI,CAACA,KAAK,CAACzH,GAAG,EAAEyH,KAAK,CAACtH,GAAG,CAAC,CAAE;kBAC/DmH,KAAK,EAAE,CAAA3I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAAG,SAAS,GAC9CT,OAAO,CAACjE,IAAI,KAAK,OAAO,GAAG,SAAS,GACpCiE,OAAO,CAACjE,IAAI,KAAK,QAAQ,GAAG,SAAS,GACrCiE,OAAO,CAACjE,IAAI,KAAK,SAAS,GAAG,SAAS,GACtC,SAAU;kBACjB6J,MAAM,EAAE,CAAA5I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyD,EAAE,MAAKT,OAAO,CAACS,EAAE,GAAG,CAAC,GAAG,CAAE;kBACnDoF,OAAO,EAAE,GAAI;kBACbM,aAAa,EAAE;oBACbhF,KAAK,EAAEA,CAAA,KAAMpB,mBAAmB,CAACC,OAAO;kBAC1C,CAAE;kBAAAyD,QAAA,eAEF3J,OAAA,CAAC0L,KAAK;oBAAA/B,QAAA,eACJ3J,OAAA;sBAAK0J,SAAS,EAAC,KAAK;sBAAAC,QAAA,gBAClB3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAEzD,OAAO,CAACT;sBAAI;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpE/J,OAAA;wBAAG0J,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEzD,OAAO,CAACN;sBAAW;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnE/J,OAAA;wBAAK0J,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,gBAC9C3J,OAAA;0BAAA2J,QAAA,GAAK,YAAU,EAACzD,OAAO,CAAC/D,QAAQ,GAAG,IAAI,GAAG,GAAG+D,OAAO,CAAC/D,QAAQ,GAAG,GAAG,GAAG,CAAC+D,OAAO,CAAC/D,QAAQ,GAAG,IAAI,EAAEmI,OAAO,CAAC,CAAC,CAAC,IAAI;wBAAA;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACrH/J,OAAA;0BAAA2J,QAAA,GAAK,kBAAU,EAACzD,OAAO,CAACuB,SAAS,GAAG,CAAC,GAAG,IAAIvB,OAAO,CAACuB,SAAS,GAAG,GAAG,GAAGvB,OAAO,CAACuB,SAAS,GAAG;wBAAA;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACjG/J,OAAA;0BAAA2J,QAAA,GAAK,UAAQ,EAACzD,OAAO,CAACyE,UAAU;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvC/J,OAAA;0BAAA2J,QAAA,GAAK,cAAY,EAACzD,OAAO,CAACwE,QAAQ;wBAAA;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAxBH7D,OAAO,CAACS,EAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBP,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL7G,eAAe,iBACdlD,OAAA;QAAK0J,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3J,OAAA,CAACF,YAAY;UACXkD,QAAQ,EAAEA,QAAS;UACnBE,eAAe,EAAEA,eAAgB;UACjCoJ,eAAe,EAAErG,mBAAoB;UACrClF,YAAY,EAAEA;QAAa;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpJ,GAAA,CAj6BID,MAAM;EAAA,QACOpB,OAAO;AAAA;AAAAiN,EAAA,GADpB7L,MAAM;AAm6BZ,eAAeA,MAAM;AAAC,IAAA6L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}