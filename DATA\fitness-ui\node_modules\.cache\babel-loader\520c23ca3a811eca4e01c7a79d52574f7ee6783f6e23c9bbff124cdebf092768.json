{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\SegmentPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiTarget, FiTrendingUp, FiClock, FiActivity, FiAward, FiUsers, FiMapPin, FiChevronRight, FiStar, FiFlag } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SegmentPanel = ({\n  segments,\n  selectedSegment,\n  onSegmentSelect,\n  userPosition\n}) => {\n  _s();\n  const [leaderboard, setLeaderboard] = useState([]);\n  const [userStats, setUserStats] = useState(null);\n\n  // Générer un classement fictif pour le segment sélectionné\n  useEffect(() => {\n    if (selectedSegment) {\n      generateLeaderboard(selectedSegment);\n      generateUserStats(selectedSegment);\n    }\n  }, [selectedSegment]);\n  const generateLeaderboard = segment => {\n    const mockLeaderboard = [{\n      rank: 1,\n      name: '<PERSON>',\n      time: '12:34',\n      speed: '18.5 km/h',\n      date: '2024-01-15',\n      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n      isPR: true\n    }, {\n      rank: 2,\n      name: 'Sophie Dubois',\n      time: '12:45',\n      speed: '18.2 km/h',\n      date: '2024-01-12',\n      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',\n      isPR: false\n    }, {\n      rank: 3,\n      name: 'Thomas Leroy',\n      time: '12:58',\n      speed: '17.8 km/h',\n      date: '2024-01-10',\n      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',\n      isPR: false\n    }, {\n      rank: 4,\n      name: 'Marie Dubois',\n      time: '13:15',\n      speed: '17.2 km/h',\n      date: '2024-01-08',\n      avatar: 'https://randomuser.me/api/portraits/women/1.jpg',\n      isPR: true,\n      isCurrentUser: true\n    }, {\n      rank: 5,\n      name: 'Pierre Moreau',\n      time: '13:22',\n      speed: '17.0 km/h',\n      date: '2024-01-05',\n      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',\n      isPR: false\n    }];\n    setLeaderboard(mockLeaderboard);\n  };\n  const generateUserStats = segment => {\n    setUserStats({\n      personalRecord: '13:15',\n      attempts: 8,\n      lastAttempt: '2024-01-08',\n      averageTime: '13:45',\n      improvement: '+12s'\n    });\n  };\n  const getRankIcon = rank => {\n    switch (rank) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-yellow-500\",\n          children: \"\\uD83E\\uDD47\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 16\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: \"\\uD83E\\uDD48\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 16\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-orange-600\",\n          children: \"\\uD83E\\uDD49\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-500 font-bold\",\n          children: [\"#\", rank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const formatDistance = distance => {\n    return distance < 1000 ? `${distance}m` : `${(distance / 1000).toFixed(1)}km`;\n  };\n  if (!selectedSegment) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n          className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-2\",\n          children: \"S\\xE9lectionnez un segment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: \"Cliquez sur un segment sur la carte pour voir les classements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: selectedSegment.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 text-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), formatDistance(selectedSegment.distance)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), selectedSegment.elevation, \"m D+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), selectedSegment.attempts || 156, \" tentatives\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold\",\n            children: selectedSegment.difficulty\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-blue-100\",\n            children: \"Difficult\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), userStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold mb-3 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FiStar, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), \"Vos statistiques\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-100\",\n              children: \"Record personnel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold text-lg\",\n              children: userStats.personalRecord\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-100\",\n              children: \"Tentatives\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold text-lg\",\n              children: userStats.attempts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-100\",\n              children: \"Derni\\xE8re fois\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium\",\n              children: userStats.lastAttempt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-100\",\n              children: \"Progression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-green-300\",\n              children: userStats.improvement\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FiAward, {\n            className: \"h-5 w-5 mr-2 text-yellow-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), \"Classement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-blue-600 hover:text-blue-700 text-sm font-medium\",\n          children: \"Voir tout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: leaderboard.map(entry => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center space-x-3 p-3 rounded-lg transition-colors ${entry.isCurrentUser ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 w-8 text-center\",\n            children: getRankIcon(entry.rank)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: entry.avatar,\n            alt: entry.name,\n            className: \"w-8 h-8 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium truncate ${entry.isCurrentUser ? 'text-blue-700' : 'text-gray-900'}`,\n                children: entry.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), entry.isPR && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium\",\n                children: \"PR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: entry.date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold text-gray-900\",\n              children: entry.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: entry.speed\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FiChevronRight, {\n            className: \"h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, entry.rank, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(FiFlag, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), \"Tenter ce segment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n          children: \"Ajouter aux favoris\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(SegmentPanel, \"qivZwz4z9nK5tZqdenPi+JY/06I=\");\n_c = SegmentPanel;\nexport default SegmentPanel;\nvar _c;\n$RefreshReg$(_c, \"SegmentPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "FiTrendingUp", "<PERSON><PERSON><PERSON>", "FiActivity", "FiAward", "FiUsers", "FiMapPin", "FiChevronRight", "FiStar", "FiFlag", "jsxDEV", "_jsxDEV", "SegmentPanel", "segments", "selectedSegment", "onSegmentSelect", "userPosition", "_s", "leaderboard", "setLeaderboard", "userStats", "setUserStats", "generateLeaderboard", "generateUserStats", "segment", "mockLeaderboard", "rank", "name", "time", "speed", "date", "avatar", "isPR", "isCurrentUser", "<PERSON><PERSON><PERSON><PERSON>", "attempts", "lastAttempt", "averageTime", "improvement", "getRankIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDistance", "distance", "toFixed", "elevation", "difficulty", "map", "entry", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/SegmentPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiTarget,\n  FiTrendingUp,\n  FiClock,\n  FiActivity,\n  FiAward,\n  FiUsers,\n  FiMapPin,\n  FiChevronRight,\n  FiStar,\n  FiFlag\n} from 'react-icons/fi';\n\nconst SegmentPanel = ({ segments, selectedSegment, onSegmentSelect, userPosition }) => {\n  const [leaderboard, setLeaderboard] = useState([]);\n  const [userStats, setUserStats] = useState(null);\n\n  // Générer un classement fictif pour le segment sélectionné\n  useEffect(() => {\n    if (selectedSegment) {\n      generateLeaderboard(selectedSegment);\n      generateUserStats(selectedSegment);\n    }\n  }, [selectedSegment]);\n\n  const generateLeaderboard = (segment) => {\n    const mockLeaderboard = [\n      {\n        rank: 1,\n        name: '<PERSON>',\n        time: '12:34',\n        speed: '18.5 km/h',\n        date: '2024-01-15',\n        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n        isPR: true\n      },\n      {\n        rank: 2,\n        name: '<PERSON>',\n        time: '12:45',\n        speed: '18.2 km/h',\n        date: '2024-01-12',\n        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',\n        isPR: false\n      },\n      {\n        rank: 3,\n        name: 'Thomas Leroy',\n        time: '12:58',\n        speed: '17.8 km/h',\n        date: '2024-01-10',\n        avatar: 'https://randomuser.me/api/portraits/men/3.jpg',\n        isPR: false\n      },\n      {\n        rank: 4,\n        name: 'Marie Dubois',\n        time: '13:15',\n        speed: '17.2 km/h',\n        date: '2024-01-08',\n        avatar: 'https://randomuser.me/api/portraits/women/1.jpg',\n        isPR: true,\n        isCurrentUser: true\n      },\n      {\n        rank: 5,\n        name: 'Pierre Moreau',\n        time: '13:22',\n        speed: '17.0 km/h',\n        date: '2024-01-05',\n        avatar: 'https://randomuser.me/api/portraits/men/4.jpg',\n        isPR: false\n      }\n    ];\n    setLeaderboard(mockLeaderboard);\n  };\n\n  const generateUserStats = (segment) => {\n    setUserStats({\n      personalRecord: '13:15',\n      attempts: 8,\n      lastAttempt: '2024-01-08',\n      averageTime: '13:45',\n      improvement: '+12s'\n    });\n  };\n\n  const getRankIcon = (rank) => {\n    switch (rank) {\n      case 1:\n        return <span className=\"text-yellow-500\">🥇</span>;\n      case 2:\n        return <span className=\"text-gray-400\">🥈</span>;\n      case 3:\n        return <span className=\"text-orange-600\">🥉</span>;\n      default:\n        return <span className=\"text-gray-500 font-bold\">#{rank}</span>;\n    }\n  };\n\n  const formatDistance = (distance) => {\n    return distance < 1000 ? `${distance}m` : `${(distance / 1000).toFixed(1)}km`;\n  };\n\n  if (!selectedSegment) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"text-center text-gray-500\">\n          <FiTarget className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n          <h3 className=\"text-lg font-medium mb-2\">Sélectionnez un segment</h3>\n          <p className=\"text-sm\">Cliquez sur un segment sur la carte pour voir les classements</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n      {/* Segment Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\">\n        <div className=\"flex items-start justify-between mb-4\">\n          <div>\n            <h2 className=\"text-xl font-bold mb-2\">{selectedSegment.name}</h2>\n            <div className=\"flex items-center space-x-4 text-blue-100\">\n              <span className=\"flex items-center\">\n                <FiMapPin className=\"h-4 w-4 mr-1\" />\n                {formatDistance(selectedSegment.distance)}\n              </span>\n              <span className=\"flex items-center\">\n                <FiTrendingUp className=\"h-4 w-4 mr-1\" />\n                {selectedSegment.elevation}m D+\n              </span>\n              <span className=\"flex items-center\">\n                <FiUsers className=\"h-4 w-4 mr-1\" />\n                {selectedSegment.attempts || 156} tentatives\n              </span>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-2xl font-bold\">{selectedSegment.difficulty}</div>\n            <div className=\"text-sm text-blue-100\">Difficulté</div>\n          </div>\n        </div>\n\n        {/* User Stats */}\n        {userStats && (\n          <div className=\"bg-white/10 rounded-lg p-4\">\n            <h3 className=\"font-semibold mb-3 flex items-center\">\n              <FiStar className=\"h-4 w-4 mr-2\" />\n              Vos statistiques\n            </h3>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <div className=\"text-blue-100\">Record personnel</div>\n                <div className=\"font-bold text-lg\">{userStats.personalRecord}</div>\n              </div>\n              <div>\n                <div className=\"text-blue-100\">Tentatives</div>\n                <div className=\"font-bold text-lg\">{userStats.attempts}</div>\n              </div>\n              <div>\n                <div className=\"text-blue-100\">Dernière fois</div>\n                <div className=\"font-medium\">{userStats.lastAttempt}</div>\n              </div>\n              <div>\n                <div className=\"text-blue-100\">Progression</div>\n                <div className=\"font-medium text-green-300\">{userStats.improvement}</div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Leaderboard */}\n      <div className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <FiAward className=\"h-5 w-5 mr-2 text-yellow-500\" />\n            Classement\n          </h3>\n          <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n            Voir tout\n          </button>\n        </div>\n\n        <div className=\"space-y-3\">\n          {leaderboard.map((entry) => (\n            <div\n              key={entry.rank}\n              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${\n                entry.isCurrentUser\n                  ? 'bg-blue-50 border border-blue-200'\n                  : 'hover:bg-gray-50'\n              }`}\n            >\n              {/* Rank */}\n              <div className=\"flex-shrink-0 w-8 text-center\">\n                {getRankIcon(entry.rank)}\n              </div>\n\n              {/* Avatar */}\n              <img\n                src={entry.avatar}\n                alt={entry.name}\n                className=\"w-8 h-8 rounded-full\"\n              />\n\n              {/* User Info */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className={`font-medium truncate ${\n                    entry.isCurrentUser ? 'text-blue-700' : 'text-gray-900'\n                  }`}>\n                    {entry.name}\n                  </span>\n                  {entry.isPR && (\n                    <span className=\"bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium\">\n                      PR\n                    </span>\n                  )}\n                </div>\n                <div className=\"text-sm text-gray-500\">{entry.date}</div>\n              </div>\n\n              {/* Stats */}\n              <div className=\"text-right\">\n                <div className=\"font-bold text-gray-900\">{entry.time}</div>\n                <div className=\"text-sm text-gray-500\">{entry.speed}</div>\n              </div>\n\n              {/* Arrow */}\n              <FiChevronRight className=\"h-4 w-4 text-gray-400\" />\n            </div>\n          ))}\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"mt-6 space-y-3\">\n          <button className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center\">\n            <FiFlag className=\"h-4 w-4 mr-2\" />\n            Tenter ce segment\n          </button>\n          <button className=\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\">\n            Ajouter aux favoris\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SegmentPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,MAAM,EACNC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,eAAe;EAAEC,eAAe;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIe,eAAe,EAAE;MACnBQ,mBAAmB,CAACR,eAAe,CAAC;MACpCS,iBAAiB,CAACT,eAAe,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMQ,mBAAmB,GAAIE,OAAO,IAAK;IACvC,MAAMC,eAAe,GAAG,CACtB;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,+CAA+C;MACvDC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,iDAAiD;MACzDC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,+CAA+C;MACvDC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,iDAAiD;MACzDC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE;IACjB,CAAC,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,+CAA+C;MACvDC,IAAI,EAAE;IACR,CAAC,CACF;IACDb,cAAc,CAACM,eAAe,CAAC;EACjC,CAAC;EAED,MAAMF,iBAAiB,GAAIC,OAAO,IAAK;IACrCH,YAAY,CAAC;MACXa,cAAc,EAAE,OAAO;MACvBC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,YAAY;MACzBC,WAAW,EAAE,OAAO;MACpBC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAIb,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,oBAAOf,OAAA;UAAM6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MACpD,KAAK,CAAC;QACJ,oBAAOlC,OAAA;UAAM6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAClD,KAAK,CAAC;QACJ,oBAAOlC,OAAA;UAAM6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MACpD;QACE,oBAAOlC,OAAA;UAAM6B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GAAC,GAAC,EAACf,IAAI;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,QAAQ,IAAK;IACnC,OAAOA,QAAQ,GAAG,IAAI,GAAG,GAAGA,QAAQ,GAAG,GAAG,GAAG,CAACA,QAAQ,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;EAC/E,CAAC;EAED,IAAI,CAAClC,eAAe,EAAE;IACpB,oBACEH,OAAA;MAAK6B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChD9B,OAAA;QAAK6B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC9B,OAAA,CAACX,QAAQ;UAACwC,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DlC,OAAA;UAAI6B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrElC,OAAA;UAAG6B,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA6D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5D9B,OAAA;MAAK6B,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAC1E9B,OAAA;QAAK6B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAI6B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAE3B,eAAe,CAACa;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClElC,OAAA;YAAK6B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxD9B,OAAA;cAAM6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9B,OAAA,CAACL,QAAQ;gBAACkC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACpCC,cAAc,CAAChC,eAAe,CAACiC,QAAQ,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACPlC,OAAA;cAAM6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9B,OAAA,CAACV,YAAY;gBAACuC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACxC/B,eAAe,CAACmC,SAAS,EAAC,MAC7B;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlC,OAAA;cAAM6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9B,OAAA,CAACN,OAAO;gBAACmC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnC/B,eAAe,CAACqB,QAAQ,IAAI,GAAG,EAAC,aACnC;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9B,OAAA;YAAK6B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAE3B,eAAe,CAACoC;UAAU;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtElC,OAAA;YAAK6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzB,SAAS,iBACRT,OAAA;QAAK6B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9B,OAAA;UAAI6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBAClD9B,OAAA,CAACH,MAAM;YAACgC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAK6B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDlC,OAAA;cAAK6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAErB,SAAS,CAACc;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/ClC,OAAA;cAAK6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAErB,SAAS,CAACe;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDlC,OAAA;cAAK6B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAErB,SAAS,CAACgB;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDlC,OAAA;cAAK6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErB,SAAS,CAACkB;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClB9B,OAAA;QAAK6B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9B,OAAA;UAAI6B,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACnE9B,OAAA,CAACP,OAAO;YAACoC,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAQ6B,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBvB,WAAW,CAACiC,GAAG,CAAEC,KAAK,iBACrBzC,OAAA;UAEE6B,SAAS,EAAE,gEACTY,KAAK,CAACnB,aAAa,GACf,mCAAmC,GACnC,kBAAkB,EACrB;UAAAQ,QAAA,gBAGH9B,OAAA;YAAK6B,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAC3CF,WAAW,CAACa,KAAK,CAAC1B,IAAI;UAAC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNlC,OAAA;YACE0C,GAAG,EAAED,KAAK,CAACrB,MAAO;YAClBuB,GAAG,EAAEF,KAAK,CAACzB,IAAK;YAChBa,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGFlC,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9B,OAAA;cAAK6B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9B,OAAA;gBAAM6B,SAAS,EAAE,wBACfY,KAAK,CAACnB,aAAa,GAAG,eAAe,GAAG,eAAe,EACtD;gBAAAQ,QAAA,EACAW,KAAK,CAACzB;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EACNO,KAAK,CAACpB,IAAI,iBACTrB,OAAA;gBAAM6B,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EAAC;cAE3F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEW,KAAK,CAACtB;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEW,KAAK,CAACxB;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DlC,OAAA;cAAK6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEW,KAAK,CAACvB;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAGNlC,OAAA,CAACJ,cAAc;YAACiC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GA3C/CO,KAAK,CAAC1B,IAAI;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4CZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9B,OAAA;UAAQ6B,SAAS,EAAC,qIAAqI;UAAAC,QAAA,gBACrJ9B,OAAA,CAACF,MAAM;YAAC+B,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlC,OAAA;UAAQ6B,SAAS,EAAC,iHAAiH;UAAAC,QAAA,EAAC;QAEpI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA5OIL,YAAY;AAAA2C,EAAA,GAAZ3C,YAAY;AA8OlB,eAAeA,YAAY;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}