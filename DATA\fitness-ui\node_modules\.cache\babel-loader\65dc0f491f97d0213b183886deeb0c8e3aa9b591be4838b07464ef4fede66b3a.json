{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Routes.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiMap, FiNavigation, FiMapPin, FiSearch, FiPlus, FiStar, FiUsers, FiTarget, FiBookmark, FiTrash2, FiPlay, FiClock, FiAlertCircle } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generatePopularRoutes, generatePOIs, calculateDistance, optimizeRouteWithPOIs, DEFAULT_POSITION } from '../utils/mapUtils';\n\n// Configuration des icônes Leaflet\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\nconst Routes = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    user\n  } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const pos = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        };\n        setUserPosition(pos);\n        loadRoutesAndPOIs(pos);\n      }, error => {\n        console.warn('Géolocalisation échouée:', error);\n        setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n        loadRoutesAndPOIs(DEFAULT_POSITION);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      });\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n  const loadRoutesAndPOIs = position => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) || route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || filters.distance === 'short' && route.distance <= 5 || filters.distance === 'medium' && route.distance > 5 && route.distance <= 15 || filters.distance === 'long' && route.distance > 15;\n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n  const handlePOIToggle = poi => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n  const MapClickHandler = () => {\n    _s();\n    useMapEvents({\n      click: e => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n  _s(MapClickHandler, \"Ld/tk8Iz8AdZhC1l7acENaOEoCo=\", false, function () {\n    return [useMapEvents];\n  });\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n  const calculateTotalDistance = points => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n    }\n    return total;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const saveRoute = route => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = {\n        ...route,\n        savedAt: new Date().toISOString()\n      };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n  const unsaveRoute = routeId => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n  const deleteCustomRoute = routeId => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if ((selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n  const startRoute = route => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Cartes et Itin\\xE9raires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez de nouveaux parcours, planifiez vos itin\\xE9raires et explorez les points d'int\\xE9r\\xEAt autour de vous.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-yellow-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: \"Chargement des donn\\xE9es...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('discover'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'discover' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMap, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), \"D\\xE9couvrir\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('saved'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'saved' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), \"Sauvegard\\xE9es (\", savedRoutes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('plan'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'plan' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiNavigation, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), \"Planifier\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('pois'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'pois' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), \"Points d'int\\xE9r\\xEAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [activeTab === 'discover' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes populaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPOIs(!showPOIs),\n                className: `px-3 py-1 rounded-md text-sm ${showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                children: \"POIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un parcours...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.type,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    type: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Tous types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"running\",\n                    children: \"Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"cycling\",\n                    children: \"V\\xE9lo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hiking\",\n                    children: \"Randonn\\xE9e\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"walking\",\n                    children: \"Marche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.difficulty,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    difficulty: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Toutes difficult\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 462,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 463,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 466,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.completions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 467,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 27\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 25\n                  }, this), savedRoutes.find(r => r.id === route.id) ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => saveRoute(route),\n                    className: \"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\",\n                    title: \"Sauvegarder\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 23\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), activeTab === 'saved' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes sauvegard\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [savedRoutes.length, \" route\", savedRoutes.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), savedRoutes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mb-2\",\n                children: \"Aucune route sauvegard\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Sauvegardez vos routes pr\\xE9f\\xE9r\\xE9es pour les retrouver facilement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: savedRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 29\n                    }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\",\n                      children: \"Personnalis\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: route.savedAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Sauvegard\\xE9 le \", new Date(route.savedAt).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 29\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 25\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), activeTab === 'plan' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Planificateur d'itin\\xE9raire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), !isPlanning ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startRoutePlanning,\n                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this), \"Cr\\xE9er un nouveau parcours\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: \"Instructions :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Cr\\xE9er un nouveau parcours\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur la carte pour ajouter des points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Minimum 2 points requis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Terminer\\\" pour sauvegarder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium mb-2\",\n                  children: \"Mode planification actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Points ajout\\xE9s : \", planningPoints.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 23\n                }, this), planningPoints.length >= 2 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Distance : \", calculateTotalDistance(planningPoints).toFixed(1), \" km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: finishPlanning,\n                  disabled: planningPoints.length < 2,\n                  className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                  children: \"Terminer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setIsPlanning(false);\n                    setPlanningPoints([]);\n                  },\n                  className: \"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                  children: \"Annuler\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), activeTab === 'pois' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Points d'int\\xE9r\\xEAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), selectedRoute && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700 mb-2\",\n                children: \"S\\xE9lectionnez des POIs pour optimiser votre parcours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: optimizeRoute,\n                disabled: selectedPOIs.length === 0,\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                  className: \"inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 23\n                }, this), \"Optimiser l'itin\\xE9raire (\", selectedPOIs.length, \" POIs)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 max-h-96 overflow-y-auto\",\n              children: pois.map(poi => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handlePOIToggle(poi),\n                className: `p-3 border rounded-lg cursor-pointer transition-colors ${selectedPOIs.find(p => p.id === poi.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: poi.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: poi.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: poi.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: poi.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 29\n                      }, this), poi.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 text-xs text-green-600\",\n                        children: \"\\u2713 V\\xE9rifi\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 23\n                }, this)\n              }, poi.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-96 lg:h-[600px]\",\n              children: /*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [userPosition.lat, userPosition.lng],\n                zoom: 13,\n                style: {\n                  height: '100%',\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MapClickHandler, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [userPosition.lat, userPosition.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: \"Votre position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n                  color: \"blue\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 21\n                }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: optimizedRoute.map(p => [p.lat, p.lng]),\n                  color: \"red\",\n                  weight: 4,\n                  opacity: 0.8\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 21\n                }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [point.lat, point.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: [\"Point \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this)), planningPoints.length > 1 && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: planningPoints.map(p => [p.lat, p.lng]),\n                  color: \"green\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                    className: 'custom-poi-marker',\n                    iconSize: [30, 30]\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: poi.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 794,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: poi.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                          className: \"h-3 w-3 text-yellow-400 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm\",\n                          children: poi.rating.toFixed(1)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 23\n                  }, this)\n                }, poi.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 21\n                }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                    className: 'custom-selected-poi-marker',\n                    iconSize: [35, 35]\n                  })\n                }, `selected-${poi.id}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s2(Routes, \"/GAdmxt/KEQN28cZpn0Cj9L97BY=\", false, function () {\n  return [useAuth];\n});\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "L", "FiMap", "FiNavigation", "FiMapPin", "FiSearch", "FiPlus", "FiStar", "FiUsers", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiTrash2", "FiPlay", "<PERSON><PERSON><PERSON>", "FiAlertCircle", "useAuth", "generatePopularRoutes", "generatePOIs", "calculateDistance", "optimizeRouteWithPOIs", "DEFAULT_POSITION", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "Routes", "_s2", "_s", "$RefreshSig$", "user", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedPOIs", "setSelectedPOIs", "optimizedRoute", "setOptimizedRoute", "isPlanning", "setIsPlanning", "planningPoints", "setPlanningPoints", "filters", "setFilters", "type", "difficulty", "distance", "sport", "surface", "searchTerm", "setSearchTerm", "showPOIs", "setShowPOIs", "activeTab", "setActiveTab", "savedRoutes", "setSavedRoutes", "routeHistory", "setRouteHistory", "segments", "setSegments", "selectedSegment", "setSelectedSegment", "showSegments", "setShowSegments", "isLoading", "setIsLoading", "error", "setError", "saved", "JSON", "parse", "localStorage", "getItem", "history", "savedSegments", "console", "navigator", "geolocation", "getCurrentPosition", "position", "pos", "lat", "coords", "latitude", "lng", "longitude", "loadRoutesAndPOIs", "warn", "enableHighAccuracy", "timeout", "maximumAge", "popularRoutes", "nearbyPOIs", "allRoutes", "filteredRoutes", "filter", "route", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesType", "matchesDifficulty", "matchesDistance", "handleRouteSelect", "handlePOIToggle", "poi", "prev", "isSelected", "find", "p", "id", "optimizeRoute", "length", "startPoint", "points", "endPoint", "optimized", "startRoutePlanning", "MapClickHandler", "click", "e", "newPoint", "latlng", "elevation", "finishPlanning", "newRoute", "Date", "now", "calculateTotalDistance", "rating", "completions", "created<PERSON>y", "firstName", "tags", "createdAt", "toISOString", "isCustom", "updatedSavedRoutes", "setItem", "stringify", "total", "i", "getDifficultyColor", "getTypeIcon", "saveRoute", "r", "routeToSave", "savedAt", "unsaveRoute", "routeId", "deleteCustomRoute", "startRoute", "historyEntry", "startedAt", "status", "updatedHistory", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "target", "map", "toFixed", "stopPropagation", "title", "toLocaleDateString", "disabled", "icon", "verified", "center", "zoom", "style", "height", "width", "url", "attribution", "positions", "color", "weight", "opacity", "point", "index", "divIcon", "html", "iconSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Routes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiMap,\n  FiNavigation,\n  FiMapPin,\n  FiSearch,\n  FiPlus,\n  FiStar,\n  FiUsers,\n  FiTarget,\n  FiBookmark,\n  FiTrash2,\n  FiPlay,\n  FiClock,\n  FiAlertCircle\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generatePopularRoutes,\n  generatePOIs,\n  calculateDistance,\n  optimizeRouteWithPOIs,\n  DEFAULT_POSITION\n} from '../utils/mapUtils';\n\n// Configuration des icônes Leaflet\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\nconst Routes = () => {\n  const { user } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all',\n    sport: 'all',\n    surface: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [selectedSegment, setSelectedSegment] = useState(null);\n  const [showSegments, setShowSegments] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      const savedSegments = JSON.parse(localStorage.getItem('segments') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n      setSegments(savedSegments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const pos = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          };\n          setUserPosition(pos);\n          loadRoutesAndPOIs(pos);\n        },\n        (error) => {\n          console.warn('Géolocalisation échouée:', error);\n          setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n          loadRoutesAndPOIs(DEFAULT_POSITION);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 300000 // 5 minutes\n        }\n      );\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n\n  const loadRoutesAndPOIs = (position) => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || \n                           (filters.distance === 'short' && route.distance <= 5) ||\n                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||\n                           (filters.distance === 'long' && route.distance > 15);\n    \n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n\n  const handlePOIToggle = (poi) => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n\n  const MapClickHandler = () => {\n    useMapEvents({\n      click: (e) => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: user?.firstName || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n\n  const calculateTotalDistance = (points) => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(\n        points[i-1].lat, points[i-1].lng,\n        points[i].lat, points[i].lng\n      );\n    }\n    return total;\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const saveRoute = (route) => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = { ...route, savedAt: new Date().toISOString() };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n\n  const unsaveRoute = (routeId) => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n\n  const deleteCustomRoute = (routeId) => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if (selectedRoute?.id === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n\n  const startRoute = (route) => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Cartes et Itinéraires\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez\n            les points d'intérêt autour de vous.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                <p className=\"text-sm text-yellow-800\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Indicateur de chargement */}\n          {isLoading && (\n            <div className=\"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"></div>\n                <p className=\"text-sm text-blue-800\">Chargement des données...</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('discover')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'discover'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMap className=\"inline mr-2\" />\n            Découvrir\n          </button>\n          <button\n            onClick={() => setActiveTab('saved')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'saved'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiBookmark className=\"inline mr-2\" />\n            Sauvegardées ({savedRoutes.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('plan')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'plan'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiNavigation className=\"inline mr-2\" />\n            Planifier\n          </button>\n          <button\n            onClick={() => setActiveTab('pois')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'pois'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMapPin className=\"inline mr-2\" />\n            Points d'intérêt\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {activeTab === 'discover' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes populaires\n                  </h2>\n                  <button\n                    onClick={() => setShowPOIs(!showPOIs)}\n                    className={`px-3 py-1 rounded-md text-sm ${\n                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    POIs\n                  </button>\n                </div>\n\n                {/* Search and Filters */}\n                <div className=\"space-y-4 mb-6\">\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un parcours...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <select\n                      value={filters.type}\n                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Tous types</option>\n                      <option value=\"running\">Course</option>\n                      <option value=\"cycling\">Vélo</option>\n                      <option value=\"hiking\">Randonnée</option>\n                      <option value=\"walking\">Marche</option>\n                    </select>\n\n                    <select\n                      value={filters.difficulty}\n                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Toutes difficultés</option>\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n\n                {/* Routes List */}\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {filteredRoutes.map(route => (\n                    <div\n                      key={route.id}\n                      onClick={() => handleRouteSelect(route)}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedRoute?.id === route.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                          <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                        </div>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                          {route.difficulty}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 mb-2\">\n                        {route.description}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                        <span>{route.distance.toFixed(1)} km</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span>{route.rating.toFixed(1)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <FiUsers className=\"h-3 w-3 mr-1\" />\n                            <span>{route.completions}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Boutons d'action */}\n                      <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                        <button\n                          onClick={() => startRoute(route)}\n                          className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                        >\n                          <FiPlay className=\"h-3 w-3 mr-1\" />\n                          Démarrer\n                        </button>\n\n                        {savedRoutes.find(r => r.id === route.id) ? (\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => saveRoute(route)}\n                            className=\"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\"\n                            title=\"Sauvegarder\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        )}\n\n                        {route.isCustom && (\n                          <button\n                            onClick={() => deleteCustomRoute(route.id)}\n                            className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                            title=\"Supprimer\"\n                          >\n                            <FiTrash2 className=\"h-3 w-3\" />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'saved' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes sauvegardées\n                  </h2>\n                  <span className=\"text-sm text-gray-500\">\n                    {savedRoutes.length} route{savedRoutes.length !== 1 ? 's' : ''}\n                  </span>\n                </div>\n\n                {savedRoutes.length === 0 ? (\n                  <div className=\"text-center py-8\">\n                    <FiBookmark className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                    <p className=\"text-gray-500 mb-2\">Aucune route sauvegardée</p>\n                    <p className=\"text-sm text-gray-400\">\n                      Sauvegardez vos routes préférées pour les retrouver facilement\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {savedRoutes.map(route => (\n                      <div\n                        key={route.id}\n                        onClick={() => handleRouteSelect(route)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          selectedRoute?.id === route.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                            <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                            {route.isCustom && (\n                              <span className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\">\n                                Personnalisé\n                              </span>\n                            )}\n                          </div>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                            {route.difficulty}\n                          </span>\n                        </div>\n\n                        <div className=\"text-sm text-gray-600 mb-2\">\n                          {route.description}\n                        </div>\n\n                        <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                          <span>{route.distance.toFixed(1)} km</span>\n                          <div className=\"flex items-center space-x-2\">\n                            {route.savedAt && (\n                              <span>Sauvegardé le {new Date(route.savedAt).toLocaleDateString()}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Boutons d'action */}\n                        <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                          <button\n                            onClick={() => startRoute(route)}\n                            className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                          >\n                            <FiPlay className=\"h-3 w-3 mr-1\" />\n                            Démarrer\n                          </button>\n\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n\n                          {route.isCustom && (\n                            <button\n                              onClick={() => deleteCustomRoute(route.id)}\n                              className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                              title=\"Supprimer\"\n                            >\n                              <FiTrash2 className=\"h-3 w-3\" />\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'plan' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Planificateur d'itinéraire\n                </h2>\n                \n                {!isPlanning ? (\n                  <div className=\"space-y-4\">\n                    <button\n                      onClick={startRoutePlanning}\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n                    >\n                      <FiPlus className=\"mr-2\" />\n                      Créer un nouveau parcours\n                    </button>\n                    \n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"mb-2\">Instructions :</p>\n                      <ul className=\"list-disc list-inside space-y-1\">\n                        <li>Cliquez sur \"Créer un nouveau parcours\"</li>\n                        <li>Cliquez sur la carte pour ajouter des points</li>\n                        <li>Minimum 2 points requis</li>\n                        <li>Cliquez sur \"Terminer\" pour sauvegarder</li>\n                      </ul>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">Mode planification actif</p>\n                      <p>Points ajoutés : {planningPoints.length}</p>\n                      {planningPoints.length >= 2 && (\n                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={finishPlanning}\n                        disabled={planningPoints.length < 2}\n                        className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Terminer\n                      </button>\n                      <button\n                        onClick={() => {\n                          setIsPlanning(false);\n                          setPlanningPoints([]);\n                        }}\n                        className=\"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\"\n                      >\n                        Annuler\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'pois' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Points d'intérêt\n                </h2>\n                \n                {selectedRoute && (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <p className=\"text-sm text-blue-700 mb-2\">\n                      Sélectionnez des POIs pour optimiser votre parcours\n                    </p>\n                    <button\n                      onClick={optimizeRoute}\n                      disabled={selectedPOIs.length === 0}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\"\n                    >\n                      <FiTarget className=\"inline mr-2\" />\n                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)\n                    </button>\n                  </div>\n                )}\n                \n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n                  {pois.map(poi => (\n                    <div\n                      key={poi.id}\n                      onClick={() => handlePOIToggle(poi)}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedPOIs.find(p => p.id === poi.id)\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{poi.icon}</span>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{poi.name}</h4>\n                          <p className=\"text-xs text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-xs text-gray-500\">{poi.rating.toFixed(1)}</span>\n                            {poi.verified && (\n                              <span className=\"ml-2 text-xs text-green-600\">✓ Vérifié</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n              <div className=\"h-96 lg:h-[600px]\">\n                <MapContainer\n                  center={[userPosition.lat, userPosition.lng]}\n                  zoom={13}\n                  style={{ height: '100%', width: '100%' }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  \n                  <MapClickHandler />\n                  \n                  {/* User position */}\n                  <Marker position={[userPosition.lat, userPosition.lng]}>\n                    <Popup>Votre position</Popup>\n                  </Marker>\n                  \n                  {/* Selected route */}\n                  {selectedRoute && (\n                    <Polyline\n                      positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n                      color=\"blue\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* Optimized route */}\n                  {optimizedRoute && (\n                    <Polyline\n                      positions={optimizedRoute.map(p => [p.lat, p.lng])}\n                      color=\"red\"\n                      weight={4}\n                      opacity={0.8}\n                    />\n                  )}\n                  \n                  {/* Planning points */}\n                  {planningPoints.map((point, index) => (\n                    <Marker key={index} position={[point.lat, point.lng]}>\n                      <Popup>Point {index + 1}</Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Planning route */}\n                  {planningPoints.length > 1 && (\n                    <Polyline\n                      positions={planningPoints.map(p => [p.lat, p.lng])}\n                      color=\"green\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* POIs */}\n                  {showPOIs && pois.map(poi => (\n                    <Marker\n                      key={poi.id}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                        className: 'custom-poi-marker',\n                        iconSize: [30, 30]\n                      })}\n                    >\n                      <Popup>\n                        <div>\n                          <h4 className=\"font-medium\">{poi.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-sm\">{poi.rating.toFixed(1)}</span>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Selected POIs highlight */}\n                  {selectedPOIs.map(poi => (\n                    <Marker\n                      key={`selected-${poi.id}`}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                        className: 'custom-selected-poi-marker',\n                        iconSize: [35, 35]\n                      })}\n                    />\n                  ))}\n                </MapContainer>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAC9F,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,iBAAiB,EACjBC,qBAAqB,EACrBC,gBAAgB,QACX,mBAAmB;;AAE1B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOrB,CAAC,CAACsB,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CzB,CAAC,CAACsB,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC2B,gBAAgB,CAAC;EAClE,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC;IACrC6D,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoF,KAAK,EAAEC,QAAQ,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMqF,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACrE,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MACxE,MAAME,aAAa,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;MAC1EjB,cAAc,CAACa,KAAK,CAAC;MACrBX,eAAe,CAACgB,OAAO,CAAC;MACxBd,WAAW,CAACe,aAAa,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAENnF,SAAS,CAAC,MAAM;IACdkF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIS,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAMC,GAAG,GAAG;UACVC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG;QACvB,CAAC;QACD3D,eAAe,CAACsD,GAAG,CAAC;QACpBM,iBAAiB,CAACN,GAAG,CAAC;MACxB,CAAC,EACAd,KAAK,IAAK;QACTS,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAErB,KAAK,CAAC;QAC/CC,QAAQ,CAAC,8EAA8E,CAAC;QACxFmB,iBAAiB,CAAC7E,gBAAgB,CAAC;MACrC,CAAC,EACD;QACE+E,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,MAAM,CAAC;MACrB,CACF,CAAC;IACH,CAAC,MAAM;MACLvB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DmB,iBAAiB,CAAC7E,gBAAgB,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6E,iBAAiB,GAAIP,QAAQ,IAAK;IACtC,IAAI;MACF,MAAMY,aAAa,GAAGtF,qBAAqB,CAAC0E,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC3E,MAAMQ,UAAU,GAAGtF,YAAY,CAACyE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;;MAE/D;MACA,MAAMS,SAAS,GAAG,CAAC,GAAGvC,WAAW,EAAE,GAAGqC,aAAa,CAAC;MACpD/D,SAAS,CAACiE,SAAS,CAAC;MACpB/D,OAAO,CAAC8D,UAAU,CAAC;MACnB3B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6B,cAAc,GAAGnE,MAAM,CAACoE,MAAM,CAACC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGD,KAAK,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpD,UAAU,CAACmD,WAAW,CAAC,CAAC,CAAC,IAC5DH,KAAK,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpD,UAAU,CAACmD,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,WAAW,GAAG7D,OAAO,CAACE,IAAI,KAAK,KAAK,IAAIqD,KAAK,CAACrD,IAAI,KAAKF,OAAO,CAACE,IAAI;IACzE,MAAM4D,iBAAiB,GAAG9D,OAAO,CAACG,UAAU,KAAK,KAAK,IAAIoD,KAAK,CAACpD,UAAU,KAAKH,OAAO,CAACG,UAAU;IACjG,MAAM4D,eAAe,GAAG/D,OAAO,CAACI,QAAQ,KAAK,KAAK,IAC1BJ,OAAO,CAACI,QAAQ,KAAK,OAAO,IAAImD,KAAK,CAACnD,QAAQ,IAAI,CAAE,IACpDJ,OAAO,CAACI,QAAQ,KAAK,QAAQ,IAAImD,KAAK,CAACnD,QAAQ,GAAG,CAAC,IAAImD,KAAK,CAACnD,QAAQ,IAAI,EAAG,IAC5EJ,OAAO,CAACI,QAAQ,KAAK,MAAM,IAAImD,KAAK,CAACnD,QAAQ,GAAG,EAAG;IAE3E,OAAOoD,aAAa,IAAIK,WAAW,IAAIC,iBAAiB,IAAIC,eAAe;EAC7E,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnChE,gBAAgB,CAACgE,KAAK,CAAC;IACvB5D,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMwE,eAAe,GAAIC,GAAG,IAAK;IAC/BzE,eAAe,CAAC0E,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAClD,IAAIH,UAAU,EAAE;QACd,OAAOD,IAAI,CAACb,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGJ,IAAI,EAAED,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIlF,aAAa,IAAIE,YAAY,CAACiF,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMC,UAAU,GAAGpF,aAAa,CAACqF,MAAM,CAAC,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAGtF,aAAa,CAACqF,MAAM,CAACrF,aAAa,CAACqF,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMI,SAAS,GAAG9G,qBAAqB,CAAC2G,UAAU,EAAEE,QAAQ,EAAEpF,YAAY,CAAC;MAC3EG,iBAAiB,CAACkF,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjF,aAAa,CAAC,IAAI,CAAC;IACnBE,iBAAiB,CAAC,EAAE,CAAC;IACrBR,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoF,eAAe,GAAGA,CAAA,KAAM;IAAAlG,EAAA;IAC5BjC,YAAY,CAAC;MACXoI,KAAK,EAAGC,CAAC,IAAK;QACZ,IAAIrF,UAAU,EAAE;UACd,MAAMsF,QAAQ,GAAG;YACf1C,GAAG,EAAEyC,CAAC,CAACE,MAAM,CAAC3C,GAAG;YACjBG,GAAG,EAAEsC,CAAC,CAACE,MAAM,CAACxC,GAAG;YACjByC,SAAS,EAAE,GAAG,CAAC;UACjB,CAAC;UACDrF,iBAAiB,CAACoE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,QAAQ,CAAC,CAAC;QAChD;MACF;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAACrG,EAAA,CAdIkG,eAAe;IAAA,QACnBnI,YAAY;EAAA;EAed,MAAMyI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvF,cAAc,CAAC2E,MAAM,IAAI,CAAC,EAAE;MAC9B,MAAMa,QAAQ,GAAG;QACff,EAAE,EAAE,UAAUgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1B/B,IAAI,EAAE,2BAA2B;QACjCvD,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAEqF,sBAAsB,CAAC3F,cAAc,CAAC;QAChD6E,MAAM,EAAE7E,cAAc;QACtBK,UAAU,EAAE,QAAQ;QACpBuF,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAA7G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8G,SAAS,KAAI,KAAK;QACnCC,IAAI,EAAE,CAAC,QAAQ,CAAC;QAChBlC,WAAW,EAAE,kCAAkC;QAC/CmC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,kBAAkB,GAAG,CAACZ,QAAQ,EAAE,GAAGzE,WAAW,CAAC;MACrDC,cAAc,CAACoF,kBAAkB,CAAC;MAClCpE,YAAY,CAACqE,OAAO,CAAC,aAAa,EAAEvE,IAAI,CAACwE,SAAS,CAACF,kBAAkB,CAAC,CAAC;MAEvE/G,SAAS,CAACgF,IAAI,IAAI,CAACmB,QAAQ,EAAE,GAAGnB,IAAI,CAAC,CAAC;MACtC5E,gBAAgB,CAAC+F,QAAQ,CAAC;MAC1BzF,aAAa,CAAC,KAAK,CAAC;MACpBE,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAM0F,sBAAsB,GAAId,MAAM,IAAK;IACzC,IAAI0B,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,MAAM,CAACF,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtCD,KAAK,IAAIvI,iBAAiB,CACxB6G,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAAC9D,GAAG,EAAEmC,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAAC3D,GAAG,EAChCgC,MAAM,CAAC2B,CAAC,CAAC,CAAC9D,GAAG,EAAEmC,MAAM,CAAC2B,CAAC,CAAC,CAAC3D,GAC3B,CAAC;IACH;IACA,OAAO0D,KAAK;EACd,CAAC;EAED,MAAME,kBAAkB,GAAIpG,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMqG,WAAW,GAAItG,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMuG,SAAS,GAAIlD,KAAK,IAAK;IAC3B,IAAI,CAAC1C,WAAW,CAACwD,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKhB,KAAK,CAACgB,EAAE,CAAC,EAAE;MAC7C,MAAMoC,WAAW,GAAG;QAAE,GAAGpD,KAAK;QAAEqD,OAAO,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MAAE,CAAC;MACnE,MAAME,kBAAkB,GAAG,CAACS,WAAW,EAAE,GAAG9F,WAAW,CAAC;MACxDC,cAAc,CAACoF,kBAAkB,CAAC;MAClCpE,YAAY,CAACqE,OAAO,CAAC,aAAa,EAAEvE,IAAI,CAACwE,SAAS,CAACF,kBAAkB,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMW,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMZ,kBAAkB,GAAGrF,WAAW,CAACyC,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpEhG,cAAc,CAACoF,kBAAkB,CAAC;IAClCpE,YAAY,CAACqE,OAAO,CAAC,aAAa,EAAEvE,IAAI,CAACwE,SAAS,CAACF,kBAAkB,CAAC,CAAC;EACzE,CAAC;EAED,MAAMa,iBAAiB,GAAID,OAAO,IAAK;IACrC;IACA,MAAMZ,kBAAkB,GAAGrF,WAAW,CAACyC,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpEhG,cAAc,CAACoF,kBAAkB,CAAC;IAClCpE,YAAY,CAACqE,OAAO,CAAC,aAAa,EAAEvE,IAAI,CAACwE,SAAS,CAACF,kBAAkB,CAAC,CAAC;;IAEvE;IACA/G,SAAS,CAACgF,IAAI,IAAIA,IAAI,CAACb,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC,CAAC;;IAErD;IACA,IAAI,CAAAxH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiF,EAAE,MAAKuC,OAAO,EAAE;MACjCvH,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMyH,UAAU,GAAIzD,KAAK,IAAK;IAC5B,MAAM0D,YAAY,GAAG;MACnB1C,EAAE,EAAEgB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdjC,KAAK,EAAEA,KAAK;MACZ2D,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCmB,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,cAAc,GAAG,CAACH,YAAY,EAAE,GAAGlG,YAAY,CAAC;IACtDC,eAAe,CAACoG,cAAc,CAAC;IAC/BtF,YAAY,CAACqE,OAAO,CAAC,cAAc,EAAEvE,IAAI,CAACwE,SAAS,CAACgB,cAAc,CAAC,CAAC;EACtE,CAAC;EAED,oBACElJ,OAAA;IAAKmJ,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCpJ,OAAA;MAAKmJ,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DpJ,OAAA;QAAKmJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpJ,OAAA;UAAImJ,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxJ,OAAA;UAAGmJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHjG,KAAK,iBACJvD,OAAA;UAAKmJ,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFpJ,OAAA;YAAKmJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpJ,OAAA,CAACR,aAAa;cAAC2J,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxJ,OAAA;cAAGmJ,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE7F;YAAK;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAnG,SAAS,iBACRrD,OAAA;UAAKmJ,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrFpJ,OAAA;YAAKmJ,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CpJ,OAAA;cAAKmJ,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzFxJ,OAAA;cAAGmJ,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxJ,OAAA;QAAKmJ,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DpJ,OAAA;UACEyJ,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,UAAU,CAAE;UACxCyG,SAAS,EAAE,qEACT1G,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2G,QAAA,gBAEHpJ,OAAA,CAACpB,KAAK;YAACuK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxJ,OAAA;UACEyJ,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,OAAO,CAAE;UACrCyG,SAAS,EAAE,qEACT1G,SAAS,KAAK,OAAO,GACjB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2G,QAAA,gBAEHpJ,OAAA,CAACZ,UAAU;YAAC+J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACxB,EAAC7G,WAAW,CAAC4D,MAAM,EAAC,GACpC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxJ,OAAA;UACEyJ,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,MAAM,CAAE;UACpCyG,SAAS,EAAE,qEACT1G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2G,QAAA,gBAEHpJ,OAAA,CAACnB,YAAY;YAACsK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxJ,OAAA;UACEyJ,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,MAAM,CAAE;UACpCyG,SAAS,EAAE,qEACT1G,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2G,QAAA,gBAEHpJ,OAAA,CAAClB,QAAQ;YAACqK,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxJ,OAAA;QAAKmJ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDpJ,OAAA;UAAKmJ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3B3G,SAAS,KAAK,UAAU,iBACvBzC,OAAA;YAAKmJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpJ,OAAA;cAAKmJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpJ,OAAA;gBAAImJ,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxJ,OAAA;gBACEyJ,OAAO,EAAEA,CAAA,KAAMjH,WAAW,CAAC,CAACD,QAAQ,CAAE;gBACtC4G,SAAS,EAAE,gCACT5G,QAAQ,GAAG,2BAA2B,GAAG,2BAA2B,EACnE;gBAAA6G,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNxJ,OAAA;cAAKmJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BpJ,OAAA;gBAAKmJ,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpJ,OAAA,CAACjB,QAAQ;kBAACoK,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpExJ,OAAA;kBACEgC,IAAI,EAAC,MAAM;kBACX0H,WAAW,EAAC,2BAA2B;kBACvCC,KAAK,EAAEtH,UAAW;kBAClBuH,QAAQ,EAAG7C,CAAC,IAAKzE,aAAa,CAACyE,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAA8G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxJ,OAAA;gBAAKmJ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpJ,OAAA;kBACE2J,KAAK,EAAE7H,OAAO,CAACE,IAAK;kBACpB4H,QAAQ,EAAG7C,CAAC,IAAKhF,UAAU,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjE,IAAI,EAAE+E,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACzER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnHpJ,OAAA;oBAAQ2J,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCxJ,OAAA;oBAAQ2J,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCxJ,OAAA;oBAAQ2J,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCxJ,OAAA;oBAAQ2J,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCxJ,OAAA;oBAAQ2J,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAETxJ,OAAA;kBACE2J,KAAK,EAAE7H,OAAO,CAACG,UAAW;kBAC1B2H,QAAQ,EAAG7C,CAAC,IAAKhF,UAAU,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhE,UAAU,EAAE8E,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/ER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnHpJ,OAAA;oBAAQ2J,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/CxJ,OAAA;oBAAQ2J,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxJ,OAAA;oBAAQ2J,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxJ,OAAA;oBAAQ2J,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxJ,OAAA;cAAKmJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDjE,cAAc,CAAC2E,GAAG,CAACzE,KAAK,iBACvBrF,OAAA;gBAEEyJ,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACT,KAAK,CAAE;gBACxC8D,SAAS,EAAE,0DACT,CAAA/H,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiF,EAAE,MAAKhB,KAAK,CAACgB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,gBAEHpJ,OAAA;kBAAKmJ,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDpJ,OAAA;oBAAKmJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CpJ,OAAA;sBAAMmJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACjD,KAAK,CAACrD,IAAI;oBAAC;sBAAAqH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DxJ,OAAA;sBAAImJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE/D,KAAK,CAACE;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACNxJ,OAAA;oBAAMmJ,SAAS,EAAE,8CAA8Cd,kBAAkB,CAAChD,KAAK,CAACpD,UAAU,CAAC,EAAG;oBAAAmH,QAAA,EACnG/D,KAAK,CAACpD;kBAAU;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENxJ,OAAA;kBAAKmJ,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxC/D,KAAK,CAACK;gBAAW;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENxJ,OAAA;kBAAKmJ,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3EpJ,OAAA;oBAAAoJ,QAAA,GAAO/D,KAAK,CAACnD,QAAQ,CAAC6H,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CxJ,OAAA;oBAAKmJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CpJ,OAAA;sBAAKmJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCpJ,OAAA,CAACf,MAAM;wBAACkK,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDxJ,OAAA;wBAAAoJ,QAAA,EAAO/D,KAAK,CAACmC,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNxJ,OAAA;sBAAKmJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCpJ,OAAA,CAACd,OAAO;wBAACiK,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpCxJ,OAAA;wBAAAoJ,QAAA,EAAO/D,KAAK,CAACoC;sBAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxJ,OAAA;kBAAKmJ,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/EpJ,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAACzD,KAAK,CAAE;oBACjC8D,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1IpJ,OAAA,CAACV,MAAM;sBAAC6J,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAER7G,WAAW,CAACwD,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKhB,KAAK,CAACgB,EAAE,CAAC,gBACvCrG,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACtD,KAAK,CAACgB,EAAE,CAAE;oBACrC8C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3BpJ,OAAA,CAACZ,UAAU;sBAAC+J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,gBAETxJ,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMlB,SAAS,CAAClD,KAAK,CAAE;oBAChC8D,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,aAAa;oBAAAb,QAAA,eAEnBpJ,OAAA,CAACZ,UAAU;sBAAC+J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACT,EAEAnE,KAAK,CAAC0C,QAAQ,iBACb/H,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACxD,KAAK,CAACgB,EAAE,CAAE;oBAC3C8C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjBpJ,OAAA,CAACX,QAAQ;sBAAC8J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzEDnE,KAAK,CAACgB,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0EV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA/G,SAAS,KAAK,OAAO,iBACpBzC,OAAA;YAAKmJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpJ,OAAA;cAAKmJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpJ,OAAA;gBAAImJ,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxJ,OAAA;gBAAMmJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpCzG,WAAW,CAAC4D,MAAM,EAAC,QAAM,EAAC5D,WAAW,CAAC4D,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL7G,WAAW,CAAC4D,MAAM,KAAK,CAAC,gBACvBvG,OAAA;cAAKmJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BpJ,OAAA,CAACZ,UAAU;gBAAC+J,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DxJ,OAAA;gBAAGmJ,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9DxJ,OAAA;gBAAGmJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAENxJ,OAAA;cAAKmJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDzG,WAAW,CAACmH,GAAG,CAACzE,KAAK,iBACpBrF,OAAA;gBAEEyJ,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACT,KAAK,CAAE;gBACxC8D,SAAS,EAAE,0DACT,CAAA/H,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiF,EAAE,MAAKhB,KAAK,CAACgB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,gBAEHpJ,OAAA;kBAAKmJ,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDpJ,OAAA;oBAAKmJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CpJ,OAAA;sBAAMmJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACjD,KAAK,CAACrD,IAAI;oBAAC;sBAAAqH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DxJ,OAAA;sBAAImJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE/D,KAAK,CAACE;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAClEnE,KAAK,CAAC0C,QAAQ,iBACb/H,OAAA;sBAAMmJ,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,EAAC;oBAE/E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNxJ,OAAA;oBAAMmJ,SAAS,EAAE,8CAA8Cd,kBAAkB,CAAChD,KAAK,CAACpD,UAAU,CAAC,EAAG;oBAAAmH,QAAA,EACnG/D,KAAK,CAACpD;kBAAU;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENxJ,OAAA;kBAAKmJ,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxC/D,KAAK,CAACK;gBAAW;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENxJ,OAAA;kBAAKmJ,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3EpJ,OAAA;oBAAAoJ,QAAA,GAAO/D,KAAK,CAACnD,QAAQ,CAAC6H,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CxJ,OAAA;oBAAKmJ,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EACzC/D,KAAK,CAACqD,OAAO,iBACZ1I,OAAA;sBAAAoJ,QAAA,GAAM,mBAAc,EAAC,IAAI/B,IAAI,CAAChC,KAAK,CAACqD,OAAO,CAAC,CAACwB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBACzE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxJ,OAAA;kBAAKmJ,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/EpJ,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAACzD,KAAK,CAAE;oBACjC8D,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1IpJ,OAAA,CAACV,MAAM;sBAAC6J,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETxJ,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACtD,KAAK,CAACgB,EAAE,CAAE;oBACrC8C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3BpJ,OAAA,CAACZ,UAAU;sBAAC+J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,EAERnE,KAAK,CAAC0C,QAAQ,iBACb/H,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACxD,KAAK,CAACgB,EAAE,CAAE;oBAC3C8C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjBpJ,OAAA,CAACX,QAAQ;sBAAC8J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/DDnE,KAAK,CAACgB,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEA/G,SAAS,KAAK,MAAM,iBACnBzC,OAAA;YAAKmJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpJ,OAAA;cAAImJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ,CAAC9H,UAAU,gBACV1B,OAAA;cAAKmJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpJ,OAAA;gBACEyJ,OAAO,EAAE7C,kBAAmB;gBAC5BuC,SAAS,EAAC,yHAAyH;gBAAAC,QAAA,gBAEnIpJ,OAAA,CAAChB,MAAM;kBAACmK,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETxJ,OAAA;gBAAKmJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpJ,OAAA;kBAAGmJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtCxJ,OAAA;kBAAImJ,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7CpJ,OAAA;oBAAAoJ,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChDxJ,OAAA;oBAAAoJ,QAAA,EAAI;kBAA4C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrDxJ,OAAA;oBAAAoJ,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChCxJ,OAAA;oBAAAoJ,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENxJ,OAAA;cAAKmJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpJ,OAAA;gBAAKmJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpJ,OAAA;kBAAGmJ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DxJ,OAAA;kBAAAoJ,QAAA,GAAG,sBAAiB,EAACxH,cAAc,CAAC2E,MAAM;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9C5H,cAAc,CAAC2E,MAAM,IAAI,CAAC,iBACzBvG,OAAA;kBAAAoJ,QAAA,GAAG,aAAW,EAAC7B,sBAAsB,CAAC3F,cAAc,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxJ,OAAA;gBAAKmJ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpJ,OAAA;kBACEyJ,OAAO,EAAEtC,cAAe;kBACxBgD,QAAQ,EAAEvI,cAAc,CAAC2E,MAAM,GAAG,CAAE;kBACpC4C,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EACtJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxJ,OAAA;kBACEyJ,OAAO,EAAEA,CAAA,KAAM;oBACb9H,aAAa,CAAC,KAAK,CAAC;oBACpBE,iBAAiB,CAAC,EAAE,CAAC;kBACvB,CAAE;kBACFsH,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,EACnG;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEA/G,SAAS,KAAK,MAAM,iBACnBzC,OAAA;YAAKmJ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpJ,OAAA;cAAImJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJpI,aAAa,iBACZpB,OAAA;cAAKmJ,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CpJ,OAAA;gBAAGmJ,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxJ,OAAA;gBACEyJ,OAAO,EAAEnD,aAAc;gBACvB6D,QAAQ,EAAE7I,YAAY,CAACiF,MAAM,KAAK,CAAE;gBACpC4C,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,gBAE3JpJ,OAAA,CAACb,QAAQ;kBAACgK,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BACZ,EAAClI,YAAY,CAACiF,MAAM,EAAC,QAC/C;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAEDxJ,OAAA;cAAKmJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDlI,IAAI,CAAC4I,GAAG,CAAC9D,GAAG,iBACXhG,OAAA;gBAEEyJ,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACC,GAAG,CAAE;gBACpCmD,SAAS,EAAE,0DACT7H,YAAY,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,GACnC,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,eAEHpJ,OAAA;kBAAKmJ,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CpJ,OAAA;oBAAMmJ,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEpD,GAAG,CAACoE;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CxJ,OAAA;oBAAKmJ,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBpJ,OAAA;sBAAImJ,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpD,GAAG,CAACT;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjExJ,OAAA;sBAAGmJ,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEpD,GAAG,CAACN;oBAAW;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1DxJ,OAAA;sBAAKmJ,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCpJ,OAAA,CAACf,MAAM;wBAACkK,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDxJ,OAAA;wBAAMmJ,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACrExD,GAAG,CAACqE,QAAQ,iBACXrK,OAAA;wBAAMmJ,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC9D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArBDxD,GAAG,CAACK,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNxJ,OAAA;UAAKmJ,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BpJ,OAAA;YAAKmJ,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5DpJ,OAAA;cAAKmJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCpJ,OAAA,CAAC3B,YAAY;gBACXiM,MAAM,EAAE,CAACxJ,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;gBAC7C8F,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAE;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,gBAEzCpJ,OAAA,CAAC1B,SAAS;kBACRqM,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,eAEFxJ,OAAA,CAAC6G,eAAe;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGnBxJ,OAAA,CAACzB,MAAM;kBAAC6F,QAAQ,EAAE,CAACtD,YAAY,CAACwD,GAAG,EAAExD,YAAY,CAAC2D,GAAG,CAAE;kBAAA2E,QAAA,eACrDpJ,OAAA,CAACxB,KAAK;oBAAA4K,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EAGRpI,aAAa,iBACZpB,OAAA,CAACvB,QAAQ;kBACPoM,SAAS,EAAEzJ,aAAa,CAACqF,MAAM,CAACqD,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACzDqG,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAhI,cAAc,iBACbxB,OAAA,CAACvB,QAAQ;kBACPoM,SAAS,EAAErJ,cAAc,CAACsI,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACnDqG,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGA5H,cAAc,CAACkI,GAAG,CAAC,CAACmB,KAAK,EAAEC,KAAK,kBAC/BlL,OAAA,CAACzB,MAAM;kBAAa6F,QAAQ,EAAE,CAAC6G,KAAK,CAAC3G,GAAG,EAAE2G,KAAK,CAACxG,GAAG,CAAE;kBAAA2E,QAAA,eACnDpJ,OAAA,CAACxB,KAAK;oBAAA4K,QAAA,GAAC,QAAM,EAAC8B,KAAK,GAAG,CAAC;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC,GADrB0B,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC,EAGD5H,cAAc,CAAC2E,MAAM,GAAG,CAAC,iBACxBvG,OAAA,CAACvB,QAAQ;kBACPoM,SAAS,EAAEjJ,cAAc,CAACkI,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACnDqG,KAAK,EAAC,OAAO;kBACbC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAjH,QAAQ,IAAIrB,IAAI,CAAC4I,GAAG,CAAC9D,GAAG,iBACvBhG,OAAA,CAACzB,MAAM;kBAEL6F,QAAQ,EAAE,CAAC4B,GAAG,CAAC1B,GAAG,EAAE0B,GAAG,CAACvB,GAAG,CAAE;kBAC7B2F,IAAI,EAAEzL,CAAC,CAACwM,OAAO,CAAC;oBACdC,IAAI,EAAE,2BAA2BpF,GAAG,CAAC8E,KAAK,iJAAiJ9E,GAAG,CAACoE,IAAI,QAAQ;oBAC3MjB,SAAS,EAAE,mBAAmB;oBAC9BkC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC,CAAE;kBAAAjC,QAAA,eAEHpJ,OAAA,CAACxB,KAAK;oBAAA4K,QAAA,eACJpJ,OAAA;sBAAAoJ,QAAA,gBACEpJ,OAAA;wBAAImJ,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEpD,GAAG,CAACT;sBAAI;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3CxJ,OAAA;wBAAGmJ,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACN;sBAAW;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1DxJ,OAAA;wBAAKmJ,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCpJ,OAAA,CAACf,MAAM;0BAACkK,SAAS,EAAC;wBAA8B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACnDxJ,OAAA;0BAAMmJ,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;wBAAC;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAjBHxD,GAAG,CAACK,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBL,CACT,CAAC,EAGDlI,YAAY,CAACwI,GAAG,CAAC9D,GAAG,iBACnBhG,OAAA,CAACzB,MAAM;kBAEL6F,QAAQ,EAAE,CAAC4B,GAAG,CAAC1B,GAAG,EAAE0B,GAAG,CAACvB,GAAG,CAAE;kBAC7B2F,IAAI,EAAEzL,CAAC,CAACwM,OAAO,CAAC;oBACdC,IAAI,EAAE,4OAA4OpF,GAAG,CAACoE,IAAI,QAAQ;oBAClQjB,SAAS,EAAE,4BAA4B;oBACvCkC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC;gBAAE,GANE,YAAYrF,GAAG,CAACK,EAAE,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAO1B,CACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9I,GAAA,CArxBID,MAAM;EAAA,QACOhB,OAAO;AAAA;AAAA6L,EAAA,GADpB7K,MAAM;AAuxBZ,eAAeA,MAAM;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}