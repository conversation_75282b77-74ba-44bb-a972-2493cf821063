{"ast": null, "code": "// Utilitaires pour la gestion des cartes et des routes\n\n/**\n * Calcule la distance entre deux points géographiques (formule de Haversine)\n */\nexport const calculateDistance = (lat1, lon1, lat2, lon2) => {\n  const R = 6371; // Rayon de la Terre en km\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLon = (lon2 - lon1) * Math.PI / 180;\n  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c;\n};\n\n/**\n * Calcule l'élévation simulée basée sur la position géographique\n */\nexport const calculateElevation = (lat, lon) => {\n  // Simulation d'élévation basée sur des patterns géographiques\n  const baseElevation = Math.sin(lat * 0.1) * Math.cos(lon * 0.1) * 500;\n  const noise = (Math.sin(lat * 10) + Math.cos(lon * 10)) * 50;\n  return Math.max(0, baseElevation + noise + 100);\n};\n\n/**\n * Calcule la pente entre deux points\n */\nexport const calculateSlope = (point1, point2) => {\n  const distance = calculateDistance(point1.lat, point1.lng, point2.lat, point2.lng) * 1000; // en mètres\n  const elevationDiff = point2.elevation - point1.elevation;\n  return distance > 0 ? elevationDiff / distance * 100 : 0; // en pourcentage\n};\n\n/**\n * Détermine le niveau de difficulté basé sur la pente et la distance\n */\nexport const calculateDifficulty = route => {\n  if (!route.points || route.points.length < 2) return 'facile';\n  let totalSlope = 0;\n  let maxSlope = 0;\n  for (let i = 1; i < route.points.length; i++) {\n    const slope = Math.abs(calculateSlope(route.points[i - 1], route.points[i]));\n    totalSlope += slope;\n    maxSlope = Math.max(maxSlope, slope);\n  }\n  const avgSlope = totalSlope / (route.points.length - 1);\n  const distance = route.distance || 0;\n\n  // Calcul de difficulté basé sur pente moyenne, pente max et distance\n  if (maxSlope > 15 || avgSlope > 8 || distance > 20) return 'difficile';\n  if (maxSlope > 8 || avgSlope > 4 || distance > 10) return 'modéré';\n  return 'facile';\n};\n\n/**\n * Génère des routes populaires simulées autour d'une position\n */\nexport const generatePopularRoutes = (centerLat, centerLng, count = 10) => {\n  const routes = [];\n  for (let i = 0; i < count; i++) {\n    const angle = i / count * 2 * Math.PI;\n    const distance = 2 + Math.random() * 15; // 2-17 km\n    const points = generateRoutePoints(centerLat, centerLng, angle, distance);\n    const route = {\n      id: `route_${i + 1}`,\n      name: `Parcours ${getRouteTypeName()} ${i + 1}`,\n      type: getRandomRouteType(),\n      distance: distance,\n      points: points,\n      elevation: {\n        gain: calculateElevationGain(points),\n        loss: calculateElevationLoss(points),\n        max: Math.max(...points.map(p => p.elevation)),\n        min: Math.min(...points.map(p => p.elevation))\n      },\n      difficulty: '',\n      rating: 3 + Math.random() * 2,\n      // 3-5 étoiles\n      completions: Math.floor(Math.random() * 500) + 50,\n      createdBy: getRandomCreator(),\n      tags: getRandomTags(),\n      description: generateRouteDescription()\n    };\n    route.difficulty = calculateDifficulty(route);\n    routes.push(route);\n  }\n  return routes.sort((a, b) => b.completions - a.completions);\n};\n\n/**\n * Génère des points pour une route\n */\nconst generateRoutePoints = (startLat, startLng, angle, distance) => {\n  const points = [];\n  const numPoints = Math.floor(distance * 2) + 5; // Plus de points pour les routes longues\n\n  for (let i = 0; i < numPoints; i++) {\n    const progress = i / (numPoints - 1);\n    const currentDistance = distance * progress;\n\n    // Ajouter de la variation à l'angle pour créer des courbes\n    const variation = Math.sin(progress * Math.PI * 4) * 0.3;\n    const currentAngle = angle + variation;\n    const lat = startLat + currentDistance / 111 * Math.cos(currentAngle);\n    const lng = startLng + currentDistance / (111 * Math.cos(startLat * Math.PI / 180)) * Math.sin(currentAngle);\n    points.push({\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng)\n    });\n  }\n  return points;\n};\n\n/**\n * Calcule le dénivelé positif\n */\nconst calculateElevationGain = points => {\n  let gain = 0;\n  for (let i = 1; i < points.length; i++) {\n    const diff = points[i].elevation - points[i - 1].elevation;\n    if (diff > 0) gain += diff;\n  }\n  return Math.round(gain);\n};\n\n/**\n * Calcule le dénivelé négatif\n */\nconst calculateElevationLoss = points => {\n  let loss = 0;\n  for (let i = 1; i < points.length; i++) {\n    const diff = points[i - 1].elevation - points[i].elevation;\n    if (diff > 0) loss += diff;\n  }\n  return Math.round(loss);\n};\n\n/**\n * Types de routes aléatoires\n */\nconst getRandomRouteType = () => {\n  const types = ['running', 'cycling', 'hiking', 'walking'];\n  return types[Math.floor(Math.random() * types.length)];\n};\nconst getRouteTypeName = () => {\n  const names = ['du Parc', 'de la Forêt', 'du Lac', 'de la Colline', 'Urbain', 'Panoramique', 'Historique'];\n  return names[Math.floor(Math.random() * names.length)];\n};\n\n/**\n * Créateurs aléatoires\n */\nconst getRandomCreator = () => {\n  const creators = ['Marie L.', 'Thomas B.', 'Sophie M.', 'Pierre D.', 'Julie R.', 'Antoine C.', 'Camille P.', 'Nicolas F.', 'Emma T.', 'Lucas G.'];\n  return creators[Math.floor(Math.random() * creators.length)];\n};\n\n/**\n * Tags aléatoires\n */\nconst getRandomTags = () => {\n  const allTags = ['scenic', 'challenging', 'beginner-friendly', 'family', 'nature', 'urban', 'historic', 'waterfront', 'mountain', 'forest'];\n  const numTags = 2 + Math.floor(Math.random() * 3);\n  const shuffled = allTags.sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, numTags);\n};\n\n/**\n * Descriptions aléatoires\n */\nconst generateRouteDescription = () => {\n  const descriptions = ['Un magnifique parcours offrant des vues panoramiques sur la région.', 'Idéal pour les débutants, ce tracé combine nature et facilité d\\'accès.', 'Parcours technique avec quelques montées, parfait pour se challenger.', 'Route familiale sécurisée avec de nombreux points d\\'intérêt.', 'Tracé urbain dynamique traversant les quartiers historiques.', 'Parcours nature au cœur de la forêt, très rafraîchissant.', 'Route côtière avec vue sur l\\'eau, particulièrement belle au coucher du soleil.'];\n  return descriptions[Math.floor(Math.random() * descriptions.length)];\n};\n\n/**\n * Génère des points d'intérêt (POI) autour d'une position\n */\nexport const generatePOIs = (centerLat, centerLng, radius = 5) => {\n  const poiTypes = [{\n    type: 'fountain',\n    name: 'Fontaine',\n    icon: '⛲',\n    color: 'blue'\n  }, {\n    type: 'viewpoint',\n    name: 'Point de vue',\n    icon: '🏔️',\n    color: 'green'\n  }, {\n    type: 'restroom',\n    name: 'Toilettes',\n    icon: '🚻',\n    color: 'purple'\n  }, {\n    type: 'parking',\n    name: 'Parking',\n    icon: '🅿️',\n    color: 'gray'\n  }, {\n    type: 'cafe',\n    name: 'Café',\n    icon: '☕',\n    color: 'brown'\n  }, {\n    type: 'bench',\n    name: 'Banc',\n    icon: '🪑',\n    color: 'orange'\n  }, {\n    type: 'shelter',\n    name: 'Abri',\n    icon: '🏠',\n    color: 'red'\n  }];\n  const pois = [];\n  const numPOIs = 15 + Math.floor(Math.random() * 10);\n  for (let i = 0; i < numPOIs; i++) {\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = Math.random() * radius;\n    const poiType = poiTypes[Math.floor(Math.random() * poiTypes.length)];\n    const lat = centerLat + distance / 111 * Math.cos(angle);\n    const lng = centerLng + distance / (111 * Math.cos(centerLat * Math.PI / 180)) * Math.sin(angle);\n    pois.push({\n      id: `poi_${i + 1}`,\n      type: poiType.type,\n      name: `${poiType.name} ${i + 1}`,\n      icon: poiType.icon,\n      color: poiType.color,\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng),\n      description: `${poiType.name} situé dans un endroit pratique`,\n      addedBy: getRandomCreator(),\n      rating: 3 + Math.random() * 2,\n      verified: Math.random() > 0.3\n    });\n  }\n  return pois;\n};\n\n/**\n * Optimise une route pour passer par des POIs sélectionnés\n */\nexport const optimizeRouteWithPOIs = (startPoint, endPoint, selectedPOIs) => {\n  if (!selectedPOIs || selectedPOIs.length === 0) {\n    return generateDirectRoute(startPoint, endPoint);\n  }\n\n  // Algorithme simple de plus proche voisin pour optimiser l'ordre des POIs\n  const optimizedPoints = [startPoint];\n  const remaining = [...selectedPOIs];\n  let current = startPoint;\n  while (remaining.length > 0) {\n    let nearest = remaining[0];\n    let nearestIndex = 0;\n    let minDistance = calculateDistance(current.lat, current.lng, nearest.lat, nearest.lng);\n    for (let i = 1; i < remaining.length; i++) {\n      const distance = calculateDistance(current.lat, current.lng, remaining[i].lat, remaining[i].lng);\n      if (distance < minDistance) {\n        minDistance = distance;\n        nearest = remaining[i];\n        nearestIndex = i;\n      }\n    }\n    optimizedPoints.push(nearest);\n    remaining.splice(nearestIndex, 1);\n    current = nearest;\n  }\n  optimizedPoints.push(endPoint);\n\n  // Générer les points de route entre chaque waypoint\n  const routePoints = [];\n  for (let i = 0; i < optimizedPoints.length - 1; i++) {\n    const segmentPoints = generateDirectRoute(optimizedPoints[i], optimizedPoints[i + 1]);\n    routePoints.push(...segmentPoints.slice(0, -1)); // Éviter les doublons\n  }\n  routePoints.push(optimizedPoints[optimizedPoints.length - 1]);\n  return routePoints;\n};\n\n/**\n * Génère une route directe entre deux points\n */\nconst generateDirectRoute = (start, end) => {\n  const points = [];\n  const numPoints = 10;\n  for (let i = 0; i <= numPoints; i++) {\n    const progress = i / numPoints;\n    const lat = start.lat + (end.lat - start.lat) * progress;\n    const lng = start.lng + (end.lng - start.lng) * progress;\n    points.push({\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng)\n    });\n  }\n  return points;\n};\n\n/**\n * Position par défaut (Paris)\n */\nexport const DEFAULT_POSITION = {\n  lat: 48.8566,\n  lng: 2.3522\n};\n\n/**\n * Génère des segments pour les classements\n */\nexport const generateSegments = (centerLat, centerLng, count = 10) => {\n  const segments = [{\n    id: 'seg_1',\n    name: 'Montée du Sacré-Cœur',\n    distance: 850,\n    elevation: 65,\n    difficulty: 'Difficile',\n    type: 'climb',\n    sport: 'running',\n    surface: 'road',\n    attempts: 234,\n    averageTime: '4:12',\n    recordTime: '3:45',\n    points: generateRoutePoints(centerLat + 0.01, centerLng + 0.005, 0.85, 15),\n    description: 'Segment emblématique avec une belle montée vers Montmartre'\n  }, {\n    id: 'seg_2',\n    name: 'Sprint des Champs',\n    distance: 400,\n    elevation: 5,\n    difficulty: 'Modéré',\n    type: 'sprint',\n    sport: 'running',\n    surface: 'road',\n    attempts: 456,\n    averageTime: '1:28',\n    recordTime: '1:15',\n    points: generateRoutePoints(centerLat - 0.008, centerLng + 0.01, 0.4, 8),\n    description: 'Sprint plat idéal pour tester sa vitesse de pointe'\n  }, {\n    id: 'seg_3',\n    name: 'Boucle du Bois',\n    distance: 2100,\n    elevation: 25,\n    difficulty: 'Facile',\n    type: 'loop',\n    sport: 'cycling',\n    surface: 'mixed',\n    attempts: 189,\n    averageTime: '6:45',\n    recordTime: '5:58',\n    points: generateRoutePoints(centerLat + 0.015, centerLng - 0.01, 2.1, 25),\n    description: 'Belle boucle dans un cadre verdoyant, parfaite pour le vélo'\n  }, {\n    id: 'seg_4',\n    name: 'Côte de Belleville',\n    distance: 650,\n    elevation: 45,\n    difficulty: 'Difficile',\n    type: 'climb',\n    sport: 'running',\n    surface: 'road',\n    attempts: 167,\n    averageTime: '3:22',\n    recordTime: '2:58',\n    points: generateRoutePoints(centerLat - 0.005, centerLng - 0.008, 0.65, 12),\n    description: 'Montée technique dans le quartier de Belleville'\n  }, {\n    id: 'seg_5',\n    name: 'Descente des Martyrs',\n    distance: 750,\n    elevation: -35,\n    difficulty: 'Modéré',\n    type: 'descent',\n    sport: 'running',\n    surface: 'road',\n    attempts: 298,\n    averageTime: '2:45',\n    recordTime: '2:18',\n    points: generateRoutePoints(centerLat + 0.008, centerLng + 0.012, 0.75, 14),\n    description: 'Descente rapide avec quelques virages techniques'\n  }, {\n    id: 'seg_6',\n    name: 'Trail de la Butte',\n    distance: 1200,\n    elevation: 80,\n    difficulty: 'Expert',\n    type: 'trail',\n    sport: 'trail',\n    surface: 'trail',\n    attempts: 89,\n    averageTime: '8:15',\n    recordTime: '6:42',\n    points: generateRoutePoints(centerLat + 0.02, centerLng + 0.008, 1.2, 20),\n    description: 'Segment trail technique avec dénivelé important'\n  }, {\n    id: 'seg_7',\n    name: 'Ligne droite République',\n    distance: 600,\n    elevation: 8,\n    difficulty: 'Facile',\n    type: 'flat',\n    sport: 'cycling',\n    surface: 'road',\n    attempts: 512,\n    averageTime: '1:45',\n    recordTime: '1:28',\n    points: generateRoutePoints(centerLat - 0.01, centerLng + 0.015, 0.6, 10),\n    description: 'Segment plat et rapide, idéal pour les cyclistes'\n  }, {\n    id: 'seg_8',\n    name: 'Escaliers de Montmartre',\n    distance: 320,\n    elevation: 55,\n    difficulty: 'Expert',\n    type: 'stairs',\n    sport: 'running',\n    surface: 'stairs',\n    attempts: 145,\n    averageTime: '2:58',\n    recordTime: '2:12',\n    points: generateRoutePoints(centerLat + 0.012, centerLng + 0.003, 0.32, 8),\n    description: 'Défi ultime : montée des escaliers de Montmartre'\n  }];\n  return segments;\n  routes.forEach((route, routeIndex) => {\n    if (route.points && route.points.length > 4) {\n      // Créer 2-3 segments par route\n      const numSegments = Math.min(3, Math.floor(route.points.length / 3));\n      for (let i = 0; i < numSegments; i++) {\n        const startIndex = Math.floor(route.points.length / numSegments * i);\n        const endIndex = Math.floor(route.points.length / numSegments * (i + 1));\n        const segmentPoints = route.points.slice(startIndex, endIndex);\n        if (segmentPoints.length >= 2) {\n          const distance = calculateTotalSegmentDistance(segmentPoints);\n          segments.push({\n            id: `segment_${routeIndex}_${i}`,\n            name: `Segment ${i + 1} - ${route.name}`,\n            description: `Portion de ${distance.toFixed(1)} km du parcours ${route.name}`,\n            points: segmentPoints,\n            distance: distance,\n            type: route.type,\n            difficulty: calculateDifficulty({\n              points: segmentPoints,\n              distance\n            }),\n            leaderboard: generateSegmentLeaderboard(),\n            totalAttempts: Math.floor(Math.random() * 1000) + 100,\n            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)\n          });\n        }\n      }\n    }\n  });\n  return segments;\n};\nconst calculateTotalSegmentDistance = points => {\n  let total = 0;\n  for (let i = 1; i < points.length; i++) {\n    total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n  }\n  return total;\n};\nconst generateSegmentLeaderboard = () => {\n  const athletes = ['Marie Dubois', 'Thomas Martin', 'Sophie Bernard', 'Pierre Durand', 'Julie Moreau', 'Antoine Petit', 'Camille Roux', 'Nicolas Leroy', 'Emma Fournier', 'Lucas Girard', 'Léa Bonnet', 'Hugo Dupont'];\n  const leaderboard = [];\n  const numEntries = 5 + Math.floor(Math.random() * 10);\n  for (let i = 0; i < numEntries; i++) {\n    const baseTime = 300 + Math.random() * 1800; // 5-35 minutes\n    const time = baseTime + i * 10 + Math.random() * 30;\n    leaderboard.push({\n      rank: i + 1,\n      athlete: athletes[Math.floor(Math.random() * athletes.length)],\n      time: Math.round(time),\n      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),\n      pace: time / 1000,\n      // pace par km en secondes\n      isLocalLegend: i === 0 && Math.random() > 0.7\n    });\n  }\n  return leaderboard.sort((a, b) => a.time - b.time);\n};", "map": {"version": 3, "names": ["calculateDistance", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "Math", "PI", "dLon", "a", "sin", "cos", "c", "atan2", "sqrt", "calculateElevation", "lat", "lon", "baseElevation", "noise", "max", "calculateSlope", "point1", "point2", "distance", "lng", "elevationDiff", "elevation", "calculateDifficulty", "route", "points", "length", "totalSlope", "maxSlope", "i", "slope", "abs", "avgSlope", "generatePopularRoutes", "centerLat", "centerLng", "count", "routes", "angle", "random", "generateRoutePoints", "id", "name", "getRouteTypeName", "type", "getRandomRouteType", "gain", "calculateElevationGain", "loss", "calculateElevationLoss", "map", "p", "min", "difficulty", "rating", "completions", "floor", "created<PERSON>y", "getRandomCreator", "tags", "getRandomTags", "description", "generateRouteDescription", "push", "sort", "b", "startLat", "startLng", "numPoints", "progress", "currentDistance", "variation", "currentAngle", "diff", "round", "types", "names", "creators", "allTags", "numTags", "shuffled", "slice", "descriptions", "generatePOIs", "radius", "poiTypes", "icon", "color", "pois", "numPOIs", "poiType", "added<PERSON>y", "verified", "optimizeRouteWithPOIs", "startPoint", "endPoint", "selectedPOIs", "generateDirectRoute", "optimizedPoints", "remaining", "current", "nearest", "nearestIndex", "minDistance", "splice", "routePoints", "segmentPoints", "start", "end", "DEFAULT_POSITION", "generateSegments", "segments", "sport", "surface", "attempts", "averageTime", "recordTime", "for<PERSON>ach", "routeIndex", "numSegments", "startIndex", "endIndex", "calculateTotalSegmentDistance", "toFixed", "leaderboard", "generateSegmentLeaderboard", "totalAttempts", "createdAt", "Date", "now", "total", "athletes", "numEntries", "baseTime", "time", "rank", "athlete", "date", "pace", "isLocalLegend"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/utils/mapUtils.js"], "sourcesContent": ["// Utilitaires pour la gestion des cartes et des routes\n\n/**\n * Calcule la distance entre deux points géographiques (formule de Haversine)\n */\nexport const calculateDistance = (lat1, lon1, lat2, lon2) => {\n  const R = 6371; // Rayon de la Terre en km\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLon = (lon2 - lon1) * Math.PI / 180;\n  const a = \n    Math.sin(dLat/2) * Math.sin(dLat/2) +\n    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n    Math.sin(dLon/2) * Math.sin(dLon/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n  return R * c;\n};\n\n/**\n * Calcule l'élévation simulée basée sur la position géographique\n */\nexport const calculateElevation = (lat, lon) => {\n  // Simulation d'élévation basée sur des patterns géographiques\n  const baseElevation = Math.sin(lat * 0.1) * Math.cos(lon * 0.1) * 500;\n  const noise = (Math.sin(lat * 10) + Math.cos(lon * 10)) * 50;\n  return Math.max(0, baseElevation + noise + 100);\n};\n\n/**\n * Calcule la pente entre deux points\n */\nexport const calculateSlope = (point1, point2) => {\n  const distance = calculateDistance(point1.lat, point1.lng, point2.lat, point2.lng) * 1000; // en mètres\n  const elevationDiff = point2.elevation - point1.elevation;\n  return distance > 0 ? (elevationDiff / distance) * 100 : 0; // en pourcentage\n};\n\n/**\n * Détermine le niveau de difficulté basé sur la pente et la distance\n */\nexport const calculateDifficulty = (route) => {\n  if (!route.points || route.points.length < 2) return 'facile';\n  \n  let totalSlope = 0;\n  let maxSlope = 0;\n  \n  for (let i = 1; i < route.points.length; i++) {\n    const slope = Math.abs(calculateSlope(route.points[i-1], route.points[i]));\n    totalSlope += slope;\n    maxSlope = Math.max(maxSlope, slope);\n  }\n  \n  const avgSlope = totalSlope / (route.points.length - 1);\n  const distance = route.distance || 0;\n  \n  // Calcul de difficulté basé sur pente moyenne, pente max et distance\n  if (maxSlope > 15 || avgSlope > 8 || distance > 20) return 'difficile';\n  if (maxSlope > 8 || avgSlope > 4 || distance > 10) return 'modéré';\n  return 'facile';\n};\n\n/**\n * Génère des routes populaires simulées autour d'une position\n */\nexport const generatePopularRoutes = (centerLat, centerLng, count = 10) => {\n  const routes = [];\n  \n  for (let i = 0; i < count; i++) {\n    const angle = (i / count) * 2 * Math.PI;\n    const distance = 2 + Math.random() * 15; // 2-17 km\n    const points = generateRoutePoints(centerLat, centerLng, angle, distance);\n    \n    const route = {\n      id: `route_${i + 1}`,\n      name: `Parcours ${getRouteTypeName()} ${i + 1}`,\n      type: getRandomRouteType(),\n      distance: distance,\n      points: points,\n      elevation: {\n        gain: calculateElevationGain(points),\n        loss: calculateElevationLoss(points),\n        max: Math.max(...points.map(p => p.elevation)),\n        min: Math.min(...points.map(p => p.elevation))\n      },\n      difficulty: '',\n      rating: 3 + Math.random() * 2, // 3-5 étoiles\n      completions: Math.floor(Math.random() * 500) + 50,\n      createdBy: getRandomCreator(),\n      tags: getRandomTags(),\n      description: generateRouteDescription()\n    };\n    \n    route.difficulty = calculateDifficulty(route);\n    routes.push(route);\n  }\n  \n  return routes.sort((a, b) => b.completions - a.completions);\n};\n\n/**\n * Génère des points pour une route\n */\nconst generateRoutePoints = (startLat, startLng, angle, distance) => {\n  const points = [];\n  const numPoints = Math.floor(distance * 2) + 5; // Plus de points pour les routes longues\n  \n  for (let i = 0; i < numPoints; i++) {\n    const progress = i / (numPoints - 1);\n    const currentDistance = distance * progress;\n    \n    // Ajouter de la variation à l'angle pour créer des courbes\n    const variation = Math.sin(progress * Math.PI * 4) * 0.3;\n    const currentAngle = angle + variation;\n    \n    const lat = startLat + (currentDistance / 111) * Math.cos(currentAngle);\n    const lng = startLng + (currentDistance / (111 * Math.cos(startLat * Math.PI / 180))) * Math.sin(currentAngle);\n    \n    points.push({\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng)\n    });\n  }\n  \n  return points;\n};\n\n/**\n * Calcule le dénivelé positif\n */\nconst calculateElevationGain = (points) => {\n  let gain = 0;\n  for (let i = 1; i < points.length; i++) {\n    const diff = points[i].elevation - points[i-1].elevation;\n    if (diff > 0) gain += diff;\n  }\n  return Math.round(gain);\n};\n\n/**\n * Calcule le dénivelé négatif\n */\nconst calculateElevationLoss = (points) => {\n  let loss = 0;\n  for (let i = 1; i < points.length; i++) {\n    const diff = points[i-1].elevation - points[i].elevation;\n    if (diff > 0) loss += diff;\n  }\n  return Math.round(loss);\n};\n\n/**\n * Types de routes aléatoires\n */\nconst getRandomRouteType = () => {\n  const types = ['running', 'cycling', 'hiking', 'walking'];\n  return types[Math.floor(Math.random() * types.length)];\n};\n\nconst getRouteTypeName = () => {\n  const names = ['du Parc', 'de la Forêt', 'du Lac', 'de la Colline', 'Urbain', 'Panoramique', 'Historique'];\n  return names[Math.floor(Math.random() * names.length)];\n};\n\n/**\n * Créateurs aléatoires\n */\nconst getRandomCreator = () => {\n  const creators = [\n    'Marie L.', 'Thomas B.', 'Sophie M.', 'Pierre D.', 'Julie R.',\n    'Antoine C.', 'Camille P.', 'Nicolas F.', 'Emma T.', 'Lucas G.'\n  ];\n  return creators[Math.floor(Math.random() * creators.length)];\n};\n\n/**\n * Tags aléatoires\n */\nconst getRandomTags = () => {\n  const allTags = [\n    'scenic', 'challenging', 'beginner-friendly', 'family', 'nature',\n    'urban', 'historic', 'waterfront', 'mountain', 'forest'\n  ];\n  const numTags = 2 + Math.floor(Math.random() * 3);\n  const shuffled = allTags.sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, numTags);\n};\n\n/**\n * Descriptions aléatoires\n */\nconst generateRouteDescription = () => {\n  const descriptions = [\n    'Un magnifique parcours offrant des vues panoramiques sur la région.',\n    'Idéal pour les débutants, ce tracé combine nature et facilité d\\'accès.',\n    'Parcours technique avec quelques montées, parfait pour se challenger.',\n    'Route familiale sécurisée avec de nombreux points d\\'intérêt.',\n    'Tracé urbain dynamique traversant les quartiers historiques.',\n    'Parcours nature au cœur de la forêt, très rafraîchissant.',\n    'Route côtière avec vue sur l\\'eau, particulièrement belle au coucher du soleil.'\n  ];\n  return descriptions[Math.floor(Math.random() * descriptions.length)];\n};\n\n/**\n * Génère des points d'intérêt (POI) autour d'une position\n */\nexport const generatePOIs = (centerLat, centerLng, radius = 5) => {\n  const poiTypes = [\n    { type: 'fountain', name: 'Fontaine', icon: '⛲', color: 'blue' },\n    { type: 'viewpoint', name: 'Point de vue', icon: '🏔️', color: 'green' },\n    { type: 'restroom', name: 'Toilettes', icon: '🚻', color: 'purple' },\n    { type: 'parking', name: 'Parking', icon: '🅿️', color: 'gray' },\n    { type: 'cafe', name: 'Café', icon: '☕', color: 'brown' },\n    { type: 'bench', name: 'Banc', icon: '🪑', color: 'orange' },\n    { type: 'shelter', name: 'Abri', icon: '🏠', color: 'red' }\n  ];\n  \n  const pois = [];\n  const numPOIs = 15 + Math.floor(Math.random() * 10);\n  \n  for (let i = 0; i < numPOIs; i++) {\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = Math.random() * radius;\n    const poiType = poiTypes[Math.floor(Math.random() * poiTypes.length)];\n    \n    const lat = centerLat + (distance / 111) * Math.cos(angle);\n    const lng = centerLng + (distance / (111 * Math.cos(centerLat * Math.PI / 180))) * Math.sin(angle);\n    \n    pois.push({\n      id: `poi_${i + 1}`,\n      type: poiType.type,\n      name: `${poiType.name} ${i + 1}`,\n      icon: poiType.icon,\n      color: poiType.color,\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng),\n      description: `${poiType.name} situé dans un endroit pratique`,\n      addedBy: getRandomCreator(),\n      rating: 3 + Math.random() * 2,\n      verified: Math.random() > 0.3\n    });\n  }\n  \n  return pois;\n};\n\n/**\n * Optimise une route pour passer par des POIs sélectionnés\n */\nexport const optimizeRouteWithPOIs = (startPoint, endPoint, selectedPOIs) => {\n  if (!selectedPOIs || selectedPOIs.length === 0) {\n    return generateDirectRoute(startPoint, endPoint);\n  }\n  \n  // Algorithme simple de plus proche voisin pour optimiser l'ordre des POIs\n  const optimizedPoints = [startPoint];\n  const remaining = [...selectedPOIs];\n  \n  let current = startPoint;\n  \n  while (remaining.length > 0) {\n    let nearest = remaining[0];\n    let nearestIndex = 0;\n    let minDistance = calculateDistance(current.lat, current.lng, nearest.lat, nearest.lng);\n    \n    for (let i = 1; i < remaining.length; i++) {\n      const distance = calculateDistance(current.lat, current.lng, remaining[i].lat, remaining[i].lng);\n      if (distance < minDistance) {\n        minDistance = distance;\n        nearest = remaining[i];\n        nearestIndex = i;\n      }\n    }\n    \n    optimizedPoints.push(nearest);\n    remaining.splice(nearestIndex, 1);\n    current = nearest;\n  }\n  \n  optimizedPoints.push(endPoint);\n  \n  // Générer les points de route entre chaque waypoint\n  const routePoints = [];\n  for (let i = 0; i < optimizedPoints.length - 1; i++) {\n    const segmentPoints = generateDirectRoute(optimizedPoints[i], optimizedPoints[i + 1]);\n    routePoints.push(...segmentPoints.slice(0, -1)); // Éviter les doublons\n  }\n  routePoints.push(optimizedPoints[optimizedPoints.length - 1]);\n  \n  return routePoints;\n};\n\n/**\n * Génère une route directe entre deux points\n */\nconst generateDirectRoute = (start, end) => {\n  const points = [];\n  const numPoints = 10;\n  \n  for (let i = 0; i <= numPoints; i++) {\n    const progress = i / numPoints;\n    const lat = start.lat + (end.lat - start.lat) * progress;\n    const lng = start.lng + (end.lng - start.lng) * progress;\n    \n    points.push({\n      lat: lat,\n      lng: lng,\n      elevation: calculateElevation(lat, lng)\n    });\n  }\n  \n  return points;\n};\n\n/**\n * Position par défaut (Paris)\n */\nexport const DEFAULT_POSITION = {\n  lat: 48.8566,\n  lng: 2.3522\n};\n\n/**\n * Génère des segments pour les classements\n */\nexport const generateSegments = (centerLat, centerLng, count = 10) => {\n  const segments = [\n    {\n      id: 'seg_1',\n      name: 'Montée du Sacré-Cœur',\n      distance: 850,\n      elevation: 65,\n      difficulty: 'Difficile',\n      type: 'climb',\n      sport: 'running',\n      surface: 'road',\n      attempts: 234,\n      averageTime: '4:12',\n      recordTime: '3:45',\n      points: generateRoutePoints(centerLat + 0.01, centerLng + 0.005, 0.85, 15),\n      description: 'Segment emblématique avec une belle montée vers Montmartre'\n    },\n    {\n      id: 'seg_2',\n      name: 'Sprint des Champs',\n      distance: 400,\n      elevation: 5,\n      difficulty: 'Modéré',\n      type: 'sprint',\n      sport: 'running',\n      surface: 'road',\n      attempts: 456,\n      averageTime: '1:28',\n      recordTime: '1:15',\n      points: generateRoutePoints(centerLat - 0.008, centerLng + 0.01, 0.4, 8),\n      description: 'Sprint plat idéal pour tester sa vitesse de pointe'\n    },\n    {\n      id: 'seg_3',\n      name: 'Boucle du Bois',\n      distance: 2100,\n      elevation: 25,\n      difficulty: 'Facile',\n      type: 'loop',\n      sport: 'cycling',\n      surface: 'mixed',\n      attempts: 189,\n      averageTime: '6:45',\n      recordTime: '5:58',\n      points: generateRoutePoints(centerLat + 0.015, centerLng - 0.01, 2.1, 25),\n      description: 'Belle boucle dans un cadre verdoyant, parfaite pour le vélo'\n    },\n    {\n      id: 'seg_4',\n      name: 'Côte de Belleville',\n      distance: 650,\n      elevation: 45,\n      difficulty: 'Difficile',\n      type: 'climb',\n      sport: 'running',\n      surface: 'road',\n      attempts: 167,\n      averageTime: '3:22',\n      recordTime: '2:58',\n      points: generateRoutePoints(centerLat - 0.005, centerLng - 0.008, 0.65, 12),\n      description: 'Montée technique dans le quartier de Belleville'\n    },\n    {\n      id: 'seg_5',\n      name: 'Descente des Martyrs',\n      distance: 750,\n      elevation: -35,\n      difficulty: 'Modéré',\n      type: 'descent',\n      sport: 'running',\n      surface: 'road',\n      attempts: 298,\n      averageTime: '2:45',\n      recordTime: '2:18',\n      points: generateRoutePoints(centerLat + 0.008, centerLng + 0.012, 0.75, 14),\n      description: 'Descente rapide avec quelques virages techniques'\n    },\n    {\n      id: 'seg_6',\n      name: 'Trail de la Butte',\n      distance: 1200,\n      elevation: 80,\n      difficulty: 'Expert',\n      type: 'trail',\n      sport: 'trail',\n      surface: 'trail',\n      attempts: 89,\n      averageTime: '8:15',\n      recordTime: '6:42',\n      points: generateRoutePoints(centerLat + 0.02, centerLng + 0.008, 1.2, 20),\n      description: 'Segment trail technique avec dénivelé important'\n    },\n    {\n      id: 'seg_7',\n      name: 'Ligne droite République',\n      distance: 600,\n      elevation: 8,\n      difficulty: 'Facile',\n      type: 'flat',\n      sport: 'cycling',\n      surface: 'road',\n      attempts: 512,\n      averageTime: '1:45',\n      recordTime: '1:28',\n      points: generateRoutePoints(centerLat - 0.01, centerLng + 0.015, 0.6, 10),\n      description: 'Segment plat et rapide, idéal pour les cyclistes'\n    },\n    {\n      id: 'seg_8',\n      name: 'Escaliers de Montmartre',\n      distance: 320,\n      elevation: 55,\n      difficulty: 'Expert',\n      type: 'stairs',\n      sport: 'running',\n      surface: 'stairs',\n      attempts: 145,\n      averageTime: '2:58',\n      recordTime: '2:12',\n      points: generateRoutePoints(centerLat + 0.012, centerLng + 0.003, 0.32, 8),\n      description: 'Défi ultime : montée des escaliers de Montmartre'\n    }\n  ];\n\n  return segments;\n\n  routes.forEach((route, routeIndex) => {\n    if (route.points && route.points.length > 4) {\n      // Créer 2-3 segments par route\n      const numSegments = Math.min(3, Math.floor(route.points.length / 3));\n\n      for (let i = 0; i < numSegments; i++) {\n        const startIndex = Math.floor((route.points.length / numSegments) * i);\n        const endIndex = Math.floor((route.points.length / numSegments) * (i + 1));\n        const segmentPoints = route.points.slice(startIndex, endIndex);\n\n        if (segmentPoints.length >= 2) {\n          const distance = calculateTotalSegmentDistance(segmentPoints);\n\n          segments.push({\n            id: `segment_${routeIndex}_${i}`,\n            name: `Segment ${i + 1} - ${route.name}`,\n            description: `Portion de ${distance.toFixed(1)} km du parcours ${route.name}`,\n            points: segmentPoints,\n            distance: distance,\n            type: route.type,\n            difficulty: calculateDifficulty({ points: segmentPoints, distance }),\n            leaderboard: generateSegmentLeaderboard(),\n            totalAttempts: Math.floor(Math.random() * 1000) + 100,\n            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)\n          });\n        }\n      }\n    }\n  });\n\n  return segments;\n};\n\nconst calculateTotalSegmentDistance = (points) => {\n  let total = 0;\n  for (let i = 1; i < points.length; i++) {\n    total += calculateDistance(\n      points[i-1].lat, points[i-1].lng,\n      points[i].lat, points[i].lng\n    );\n  }\n  return total;\n};\n\nconst generateSegmentLeaderboard = () => {\n  const athletes = [\n    'Marie Dubois', 'Thomas Martin', 'Sophie Bernard', 'Pierre Durand',\n    'Julie Moreau', 'Antoine Petit', 'Camille Roux', 'Nicolas Leroy',\n    'Emma Fournier', 'Lucas Girard', 'Léa Bonnet', 'Hugo Dupont'\n  ];\n\n  const leaderboard = [];\n  const numEntries = 5 + Math.floor(Math.random() * 10);\n\n  for (let i = 0; i < numEntries; i++) {\n    const baseTime = 300 + Math.random() * 1800; // 5-35 minutes\n    const time = baseTime + (i * 10) + Math.random() * 30;\n\n    leaderboard.push({\n      rank: i + 1,\n      athlete: athletes[Math.floor(Math.random() * athletes.length)],\n      time: Math.round(time),\n      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),\n      pace: time / 1000, // pace par km en secondes\n      isLocalLegend: i === 0 && Math.random() > 0.7\n    });\n  }\n\n  return leaderboard.sort((a, b) => a.time - b.time);\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAC3D,MAAMC,CAAC,GAAG,IAAI,CAAC,CAAC;EAChB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAIM,IAAI,CAACC,EAAE,GAAG,GAAG;EAC1C,MAAMC,IAAI,GAAG,CAACL,IAAI,GAAGF,IAAI,IAAIK,IAAI,CAACC,EAAE,GAAG,GAAG;EAC1C,MAAME,CAAC,GACLH,IAAI,CAACI,GAAG,CAACL,IAAI,GAAC,CAAC,CAAC,GAAGC,IAAI,CAACI,GAAG,CAACL,IAAI,GAAC,CAAC,CAAC,GACnCC,IAAI,CAACK,GAAG,CAACX,IAAI,GAAGM,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,GAAGD,IAAI,CAACK,GAAG,CAACT,IAAI,GAAGI,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,GAC/DD,IAAI,CAACI,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC,GAAGF,IAAI,CAACI,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC;EACrC,MAAMI,CAAC,GAAG,CAAC,GAAGN,IAAI,CAACO,KAAK,CAACP,IAAI,CAACQ,IAAI,CAACL,CAAC,CAAC,EAAEH,IAAI,CAACQ,IAAI,CAAC,CAAC,GAACL,CAAC,CAAC,CAAC;EACtD,OAAOL,CAAC,GAAGQ,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC9C;EACA,MAAMC,aAAa,GAAGZ,IAAI,CAACI,GAAG,CAACM,GAAG,GAAG,GAAG,CAAC,GAAGV,IAAI,CAACK,GAAG,CAACM,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EACrE,MAAME,KAAK,GAAG,CAACb,IAAI,CAACI,GAAG,CAACM,GAAG,GAAG,EAAE,CAAC,GAAGV,IAAI,CAACK,GAAG,CAACM,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE;EAC5D,OAAOX,IAAI,CAACc,GAAG,CAAC,CAAC,EAAEF,aAAa,GAAGC,KAAK,GAAG,GAAG,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EAChD,MAAMC,QAAQ,GAAGzB,iBAAiB,CAACuB,MAAM,CAACN,GAAG,EAAEM,MAAM,CAACG,GAAG,EAAEF,MAAM,CAACP,GAAG,EAAEO,MAAM,CAACE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;EAC3F,MAAMC,aAAa,GAAGH,MAAM,CAACI,SAAS,GAAGL,MAAM,CAACK,SAAS;EACzD,OAAOH,QAAQ,GAAG,CAAC,GAAIE,aAAa,GAAGF,QAAQ,GAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9D,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,mBAAmB,GAAIC,KAAK,IAAK;EAC5C,IAAI,CAACA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,QAAQ;EAE7D,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACC,MAAM,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;IAC5C,MAAMC,KAAK,GAAG7B,IAAI,CAAC8B,GAAG,CAACf,cAAc,CAACQ,KAAK,CAACC,MAAM,CAACI,CAAC,GAAC,CAAC,CAAC,EAAEL,KAAK,CAACC,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC;IAC1EF,UAAU,IAAIG,KAAK;IACnBF,QAAQ,GAAG3B,IAAI,CAACc,GAAG,CAACa,QAAQ,EAAEE,KAAK,CAAC;EACtC;EAEA,MAAME,QAAQ,GAAGL,UAAU,IAAIH,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EACvD,MAAMP,QAAQ,GAAGK,KAAK,CAACL,QAAQ,IAAI,CAAC;;EAEpC;EACA,IAAIS,QAAQ,GAAG,EAAE,IAAII,QAAQ,GAAG,CAAC,IAAIb,QAAQ,GAAG,EAAE,EAAE,OAAO,WAAW;EACtE,IAAIS,QAAQ,GAAG,CAAC,IAAII,QAAQ,GAAG,CAAC,IAAIb,QAAQ,GAAG,EAAE,EAAE,OAAO,QAAQ;EAClE,OAAO,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMc,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,GAAG,EAAE,KAAK;EACzE,MAAMC,MAAM,GAAG,EAAE;EAEjB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,KAAK,EAAEP,CAAC,EAAE,EAAE;IAC9B,MAAMS,KAAK,GAAIT,CAAC,GAAGO,KAAK,GAAI,CAAC,GAAGnC,IAAI,CAACC,EAAE;IACvC,MAAMiB,QAAQ,GAAG,CAAC,GAAGlB,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IACzC,MAAMd,MAAM,GAAGe,mBAAmB,CAACN,SAAS,EAAEC,SAAS,EAAEG,KAAK,EAAEnB,QAAQ,CAAC;IAEzE,MAAMK,KAAK,GAAG;MACZiB,EAAE,EAAE,SAASZ,CAAC,GAAG,CAAC,EAAE;MACpBa,IAAI,EAAE,YAAYC,gBAAgB,CAAC,CAAC,IAAId,CAAC,GAAG,CAAC,EAAE;MAC/Ce,IAAI,EAAEC,kBAAkB,CAAC,CAAC;MAC1B1B,QAAQ,EAAEA,QAAQ;MAClBM,MAAM,EAAEA,MAAM;MACdH,SAAS,EAAE;QACTwB,IAAI,EAAEC,sBAAsB,CAACtB,MAAM,CAAC;QACpCuB,IAAI,EAAEC,sBAAsB,CAACxB,MAAM,CAAC;QACpCV,GAAG,EAAEd,IAAI,CAACc,GAAG,CAAC,GAAGU,MAAM,CAACyB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,CAAC,CAAC;QAC9C8B,GAAG,EAAEnD,IAAI,CAACmD,GAAG,CAAC,GAAG3B,MAAM,CAACyB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,CAAC;MAC/C,CAAC;MACD+B,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,CAAC,GAAGrD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,CAAC;MAAE;MAC/BgB,WAAW,EAAEtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;MACjDkB,SAAS,EAAEC,gBAAgB,CAAC,CAAC;MAC7BC,IAAI,EAAEC,aAAa,CAAC,CAAC;MACrBC,WAAW,EAAEC,wBAAwB,CAAC;IACxC,CAAC;IAEDtC,KAAK,CAAC6B,UAAU,GAAG9B,mBAAmB,CAACC,KAAK,CAAC;IAC7Ca,MAAM,CAAC0B,IAAI,CAACvC,KAAK,CAAC;EACpB;EAEA,OAAOa,MAAM,CAAC2B,IAAI,CAAC,CAAC5D,CAAC,EAAE6D,CAAC,KAAKA,CAAC,CAACV,WAAW,GAAGnD,CAAC,CAACmD,WAAW,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;AACA,MAAMf,mBAAmB,GAAGA,CAAC0B,QAAQ,EAAEC,QAAQ,EAAE7B,KAAK,EAAEnB,QAAQ,KAAK;EACnE,MAAMM,MAAM,GAAG,EAAE;EACjB,MAAM2C,SAAS,GAAGnE,IAAI,CAACuD,KAAK,CAACrC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;EAEhD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,SAAS,EAAEvC,CAAC,EAAE,EAAE;IAClC,MAAMwC,QAAQ,GAAGxC,CAAC,IAAIuC,SAAS,GAAG,CAAC,CAAC;IACpC,MAAME,eAAe,GAAGnD,QAAQ,GAAGkD,QAAQ;;IAE3C;IACA,MAAME,SAAS,GAAGtE,IAAI,CAACI,GAAG,CAACgE,QAAQ,GAAGpE,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMsE,YAAY,GAAGlC,KAAK,GAAGiC,SAAS;IAEtC,MAAM5D,GAAG,GAAGuD,QAAQ,GAAII,eAAe,GAAG,GAAG,GAAIrE,IAAI,CAACK,GAAG,CAACkE,YAAY,CAAC;IACvE,MAAMpD,GAAG,GAAG+C,QAAQ,GAAIG,eAAe,IAAI,GAAG,GAAGrE,IAAI,CAACK,GAAG,CAAC4D,QAAQ,GAAGjE,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,CAAC,GAAID,IAAI,CAACI,GAAG,CAACmE,YAAY,CAAC;IAE9G/C,MAAM,CAACsC,IAAI,CAAC;MACVpD,GAAG,EAAEA,GAAG;MACRS,GAAG,EAAEA,GAAG;MACRE,SAAS,EAAEZ,kBAAkB,CAACC,GAAG,EAAES,GAAG;IACxC,CAAC,CAAC;EACJ;EAEA,OAAOK,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,MAAMsB,sBAAsB,GAAItB,MAAM,IAAK;EACzC,IAAIqB,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;IACtC,MAAM4C,IAAI,GAAGhD,MAAM,CAACI,CAAC,CAAC,CAACP,SAAS,GAAGG,MAAM,CAACI,CAAC,GAAC,CAAC,CAAC,CAACP,SAAS;IACxD,IAAImD,IAAI,GAAG,CAAC,EAAE3B,IAAI,IAAI2B,IAAI;EAC5B;EACA,OAAOxE,IAAI,CAACyE,KAAK,CAAC5B,IAAI,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMG,sBAAsB,GAAIxB,MAAM,IAAK;EACzC,IAAIuB,IAAI,GAAG,CAAC;EACZ,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;IACtC,MAAM4C,IAAI,GAAGhD,MAAM,CAACI,CAAC,GAAC,CAAC,CAAC,CAACP,SAAS,GAAGG,MAAM,CAACI,CAAC,CAAC,CAACP,SAAS;IACxD,IAAImD,IAAI,GAAG,CAAC,EAAEzB,IAAI,IAAIyB,IAAI;EAC5B;EACA,OAAOxE,IAAI,CAACyE,KAAK,CAAC1B,IAAI,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMH,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,MAAM8B,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;EACzD,OAAOA,KAAK,CAAC1E,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAGoC,KAAK,CAACjD,MAAM,CAAC,CAAC;AACxD,CAAC;AAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMiC,KAAK,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;EAC1G,OAAOA,KAAK,CAAC3E,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAGqC,KAAK,CAAClD,MAAM,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA,MAAMgC,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMmB,QAAQ,GAAG,CACf,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAC7D,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,CAChE;EACD,OAAOA,QAAQ,CAAC5E,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAGsC,QAAQ,CAACnD,MAAM,CAAC,CAAC;AAC9D,CAAC;;AAED;AACA;AACA;AACA,MAAMkC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAMkB,OAAO,GAAG,CACd,QAAQ,EAAE,aAAa,EAAE,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,EAChE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,CACxD;EACD,MAAMC,OAAO,GAAG,CAAC,GAAG9E,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EACjD,MAAMyC,QAAQ,GAAGF,OAAO,CAACd,IAAI,CAAC,MAAM,GAAG,GAAG/D,IAAI,CAACsC,MAAM,CAAC,CAAC,CAAC;EACxD,OAAOyC,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC;AACnC,CAAC;;AAED;AACA;AACA;AACA,MAAMjB,wBAAwB,GAAGA,CAAA,KAAM;EACrC,MAAMoB,YAAY,GAAG,CACnB,qEAAqE,EACrE,yEAAyE,EACzE,uEAAuE,EACvE,+DAA+D,EAC/D,8DAA8D,EAC9D,2DAA2D,EAC3D,iFAAiF,CAClF;EACD,OAAOA,YAAY,CAACjF,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG2C,YAAY,CAACxD,MAAM,CAAC,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyD,YAAY,GAAGA,CAACjD,SAAS,EAAEC,SAAS,EAAEiD,MAAM,GAAG,CAAC,KAAK;EAChE,MAAMC,QAAQ,GAAG,CACf;IAAEzC,IAAI,EAAE,UAAU;IAAEF,IAAI,EAAE,UAAU;IAAE4C,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChE;IAAE3C,IAAI,EAAE,WAAW;IAAEF,IAAI,EAAE,cAAc;IAAE4C,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACxE;IAAE3C,IAAI,EAAE,UAAU;IAAEF,IAAI,EAAE,WAAW;IAAE4C,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpE;IAAE3C,IAAI,EAAE,SAAS;IAAEF,IAAI,EAAE,SAAS;IAAE4C,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChE;IAAE3C,IAAI,EAAE,MAAM;IAAEF,IAAI,EAAE,MAAM;IAAE4C,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACzD;IAAE3C,IAAI,EAAE,OAAO;IAAEF,IAAI,EAAE,MAAM;IAAE4C,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC5D;IAAE3C,IAAI,EAAE,SAAS;IAAEF,IAAI,EAAE,MAAM;IAAE4C,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC5D;EAED,MAAMC,IAAI,GAAG,EAAE;EACf,MAAMC,OAAO,GAAG,EAAE,GAAGxF,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EAEnD,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,OAAO,EAAE5D,CAAC,EAAE,EAAE;IAChC,MAAMS,KAAK,GAAGrC,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGtC,IAAI,CAACC,EAAE;IACzC,MAAMiB,QAAQ,GAAGlB,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG6C,MAAM;IACvC,MAAMM,OAAO,GAAGL,QAAQ,CAACpF,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG8C,QAAQ,CAAC3D,MAAM,CAAC,CAAC;IAErE,MAAMf,GAAG,GAAGuB,SAAS,GAAIf,QAAQ,GAAG,GAAG,GAAIlB,IAAI,CAACK,GAAG,CAACgC,KAAK,CAAC;IAC1D,MAAMlB,GAAG,GAAGe,SAAS,GAAIhB,QAAQ,IAAI,GAAG,GAAGlB,IAAI,CAACK,GAAG,CAAC4B,SAAS,GAAGjC,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,CAAC,GAAID,IAAI,CAACI,GAAG,CAACiC,KAAK,CAAC;IAElGkD,IAAI,CAACzB,IAAI,CAAC;MACRtB,EAAE,EAAE,OAAOZ,CAAC,GAAG,CAAC,EAAE;MAClBe,IAAI,EAAE8C,OAAO,CAAC9C,IAAI;MAClBF,IAAI,EAAE,GAAGgD,OAAO,CAAChD,IAAI,IAAIb,CAAC,GAAG,CAAC,EAAE;MAChCyD,IAAI,EAAEI,OAAO,CAACJ,IAAI;MAClBC,KAAK,EAAEG,OAAO,CAACH,KAAK;MACpB5E,GAAG,EAAEA,GAAG;MACRS,GAAG,EAAEA,GAAG;MACRE,SAAS,EAAEZ,kBAAkB,CAACC,GAAG,EAAES,GAAG,CAAC;MACvCyC,WAAW,EAAE,GAAG6B,OAAO,CAAChD,IAAI,iCAAiC;MAC7DiD,OAAO,EAAEjC,gBAAgB,CAAC,CAAC;MAC3BJ,MAAM,EAAE,CAAC,GAAGrD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,CAAC;MAC7BqD,QAAQ,EAAE3F,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG;IAC5B,CAAC,CAAC;EACJ;EAEA,OAAOiD,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAGA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,KAAK;EAC3E,IAAI,CAACA,YAAY,IAAIA,YAAY,CAACtE,MAAM,KAAK,CAAC,EAAE;IAC9C,OAAOuE,mBAAmB,CAACH,UAAU,EAAEC,QAAQ,CAAC;EAClD;;EAEA;EACA,MAAMG,eAAe,GAAG,CAACJ,UAAU,CAAC;EACpC,MAAMK,SAAS,GAAG,CAAC,GAAGH,YAAY,CAAC;EAEnC,IAAII,OAAO,GAAGN,UAAU;EAExB,OAAOK,SAAS,CAACzE,MAAM,GAAG,CAAC,EAAE;IAC3B,IAAI2E,OAAO,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC1B,IAAIG,YAAY,GAAG,CAAC;IACpB,IAAIC,WAAW,GAAG7G,iBAAiB,CAAC0G,OAAO,CAACzF,GAAG,EAAEyF,OAAO,CAAChF,GAAG,EAAEiF,OAAO,CAAC1F,GAAG,EAAE0F,OAAO,CAACjF,GAAG,CAAC;IAEvF,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,SAAS,CAACzE,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzC,MAAMV,QAAQ,GAAGzB,iBAAiB,CAAC0G,OAAO,CAACzF,GAAG,EAAEyF,OAAO,CAAChF,GAAG,EAAE+E,SAAS,CAACtE,CAAC,CAAC,CAAClB,GAAG,EAAEwF,SAAS,CAACtE,CAAC,CAAC,CAACT,GAAG,CAAC;MAChG,IAAID,QAAQ,GAAGoF,WAAW,EAAE;QAC1BA,WAAW,GAAGpF,QAAQ;QACtBkF,OAAO,GAAGF,SAAS,CAACtE,CAAC,CAAC;QACtByE,YAAY,GAAGzE,CAAC;MAClB;IACF;IAEAqE,eAAe,CAACnC,IAAI,CAACsC,OAAO,CAAC;IAC7BF,SAAS,CAACK,MAAM,CAACF,YAAY,EAAE,CAAC,CAAC;IACjCF,OAAO,GAAGC,OAAO;EACnB;EAEAH,eAAe,CAACnC,IAAI,CAACgC,QAAQ,CAAC;;EAE9B;EACA,MAAMU,WAAW,GAAG,EAAE;EACtB,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,eAAe,CAACxE,MAAM,GAAG,CAAC,EAAEG,CAAC,EAAE,EAAE;IACnD,MAAM6E,aAAa,GAAGT,mBAAmB,CAACC,eAAe,CAACrE,CAAC,CAAC,EAAEqE,eAAe,CAACrE,CAAC,GAAG,CAAC,CAAC,CAAC;IACrF4E,WAAW,CAAC1C,IAAI,CAAC,GAAG2C,aAAa,CAACzB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD;EACAwB,WAAW,CAAC1C,IAAI,CAACmC,eAAe,CAACA,eAAe,CAACxE,MAAM,GAAG,CAAC,CAAC,CAAC;EAE7D,OAAO+E,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA,MAAMR,mBAAmB,GAAGA,CAACU,KAAK,EAAEC,GAAG,KAAK;EAC1C,MAAMnF,MAAM,GAAG,EAAE;EACjB,MAAM2C,SAAS,GAAG,EAAE;EAEpB,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIuC,SAAS,EAAEvC,CAAC,EAAE,EAAE;IACnC,MAAMwC,QAAQ,GAAGxC,CAAC,GAAGuC,SAAS;IAC9B,MAAMzD,GAAG,GAAGgG,KAAK,CAAChG,GAAG,GAAG,CAACiG,GAAG,CAACjG,GAAG,GAAGgG,KAAK,CAAChG,GAAG,IAAI0D,QAAQ;IACxD,MAAMjD,GAAG,GAAGuF,KAAK,CAACvF,GAAG,GAAG,CAACwF,GAAG,CAACxF,GAAG,GAAGuF,KAAK,CAACvF,GAAG,IAAIiD,QAAQ;IAExD5C,MAAM,CAACsC,IAAI,CAAC;MACVpD,GAAG,EAAEA,GAAG;MACRS,GAAG,EAAEA,GAAG;MACRE,SAAS,EAAEZ,kBAAkB,CAACC,GAAG,EAAES,GAAG;IACxC,CAAC,CAAC;EACJ;EAEA,OAAOK,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMoF,gBAAgB,GAAG;EAC9BlG,GAAG,EAAE,OAAO;EACZS,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0F,gBAAgB,GAAGA,CAAC5E,SAAS,EAAEC,SAAS,EAAEC,KAAK,GAAG,EAAE,KAAK;EACpE,MAAM2E,QAAQ,GAAG,CACf;IACEtE,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,sBAAsB;IAC5BvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,EAAE;IACb+B,UAAU,EAAE,WAAW;IACvBT,IAAI,EAAE,OAAO;IACboE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,IAAI,EAAEC,SAAS,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;IAC1E0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,mBAAmB;IACzBvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,CAAC;IACZ+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,QAAQ;IACdoE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACxE0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,gBAAgB;IACtBvB,QAAQ,EAAE,IAAI;IACdG,SAAS,EAAE,EAAE;IACb+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,MAAM;IACZoE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;IACzE0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,oBAAoB;IAC1BvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,EAAE;IACb+B,UAAU,EAAE,WAAW;IACvBT,IAAI,EAAE,OAAO;IACboE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;IAC3E0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,sBAAsB;IAC5BvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,CAAC,EAAE;IACd+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,SAAS;IACfoE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;IAC3E0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,mBAAmB;IACzBvB,QAAQ,EAAE,IAAI;IACdG,SAAS,EAAE,EAAE;IACb+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,OAAO;IACboE,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,IAAI,EAAEC,SAAS,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;IACzE0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,yBAAyB;IAC/BvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,CAAC;IACZ+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,MAAM;IACZoE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,IAAI,EAAEC,SAAS,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;IACzE0B,WAAW,EAAE;EACf,CAAC,EACD;IACEpB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,yBAAyB;IAC/BvB,QAAQ,EAAE,GAAG;IACbG,SAAS,EAAE,EAAE;IACb+B,UAAU,EAAE,QAAQ;IACpBT,IAAI,EAAE,QAAQ;IACdoE,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,QAAQ;IACjBC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClB3F,MAAM,EAAEe,mBAAmB,CAACN,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1E0B,WAAW,EAAE;EACf,CAAC,CACF;EAED,OAAOkD,QAAQ;EAEf1E,MAAM,CAACgF,OAAO,CAAC,CAAC7F,KAAK,EAAE8F,UAAU,KAAK;IACpC,IAAI9F,KAAK,CAACC,MAAM,IAAID,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C;MACA,MAAM6F,WAAW,GAAGtH,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAEnD,IAAI,CAACuD,KAAK,CAAChC,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;MAEpE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,WAAW,EAAE1F,CAAC,EAAE,EAAE;QACpC,MAAM2F,UAAU,GAAGvH,IAAI,CAACuD,KAAK,CAAEhC,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG6F,WAAW,GAAI1F,CAAC,CAAC;QACtE,MAAM4F,QAAQ,GAAGxH,IAAI,CAACuD,KAAK,CAAEhC,KAAK,CAACC,MAAM,CAACC,MAAM,GAAG6F,WAAW,IAAK1F,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1E,MAAM6E,aAAa,GAAGlF,KAAK,CAACC,MAAM,CAACwD,KAAK,CAACuC,UAAU,EAAEC,QAAQ,CAAC;QAE9D,IAAIf,aAAa,CAAChF,MAAM,IAAI,CAAC,EAAE;UAC7B,MAAMP,QAAQ,GAAGuG,6BAA6B,CAAChB,aAAa,CAAC;UAE7DK,QAAQ,CAAChD,IAAI,CAAC;YACZtB,EAAE,EAAE,WAAW6E,UAAU,IAAIzF,CAAC,EAAE;YAChCa,IAAI,EAAE,WAAWb,CAAC,GAAG,CAAC,MAAML,KAAK,CAACkB,IAAI,EAAE;YACxCmB,WAAW,EAAE,cAAc1C,QAAQ,CAACwG,OAAO,CAAC,CAAC,CAAC,mBAAmBnG,KAAK,CAACkB,IAAI,EAAE;YAC7EjB,MAAM,EAAEiF,aAAa;YACrBvF,QAAQ,EAAEA,QAAQ;YAClByB,IAAI,EAAEpB,KAAK,CAACoB,IAAI;YAChBS,UAAU,EAAE9B,mBAAmB,CAAC;cAAEE,MAAM,EAAEiF,aAAa;cAAEvF;YAAS,CAAC,CAAC;YACpEyG,WAAW,EAAEC,0BAA0B,CAAC,CAAC;YACzCC,aAAa,EAAE7H,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG;YACrDwF,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhI,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;UAC5E,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,CAAC;EAEF,OAAOwE,QAAQ;AACjB,CAAC;AAED,MAAMW,6BAA6B,GAAIjG,MAAM,IAAK;EAChD,IAAIyG,KAAK,GAAG,CAAC;EACb,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;IACtCqG,KAAK,IAAIxI,iBAAiB,CACxB+B,MAAM,CAACI,CAAC,GAAC,CAAC,CAAC,CAAClB,GAAG,EAAEc,MAAM,CAACI,CAAC,GAAC,CAAC,CAAC,CAACT,GAAG,EAChCK,MAAM,CAACI,CAAC,CAAC,CAAClB,GAAG,EAAEc,MAAM,CAACI,CAAC,CAAC,CAACT,GAC3B,CAAC;EACH;EACA,OAAO8G,KAAK;AACd,CAAC;AAED,MAAML,0BAA0B,GAAGA,CAAA,KAAM;EACvC,MAAMM,QAAQ,GAAG,CACf,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAClE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAChE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,CAC7D;EAED,MAAMP,WAAW,GAAG,EAAE;EACtB,MAAMQ,UAAU,GAAG,CAAC,GAAGnI,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EAErD,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuG,UAAU,EAAEvG,CAAC,EAAE,EAAE;IACnC,MAAMwG,QAAQ,GAAG,GAAG,GAAGpI,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC7C,MAAM+F,IAAI,GAAGD,QAAQ,GAAIxG,CAAC,GAAG,EAAG,GAAG5B,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE;IAErDqF,WAAW,CAAC7D,IAAI,CAAC;MACfwE,IAAI,EAAE1G,CAAC,GAAG,CAAC;MACX2G,OAAO,EAAEL,QAAQ,CAAClI,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG4F,QAAQ,CAACzG,MAAM,CAAC,CAAC;MAC9D4G,IAAI,EAAErI,IAAI,CAACyE,KAAK,CAAC4D,IAAI,CAAC;MACtBG,IAAI,EAAE,IAAIT,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhI,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACrEmG,IAAI,EAAEJ,IAAI,GAAG,IAAI;MAAE;MACnBK,aAAa,EAAE9G,CAAC,KAAK,CAAC,IAAI5B,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG;IAC5C,CAAC,CAAC;EACJ;EAEA,OAAOqF,WAAW,CAAC5D,IAAI,CAAC,CAAC5D,CAAC,EAAE6D,CAAC,KAAK7D,CAAC,CAACkI,IAAI,GAAGrE,CAAC,CAACqE,IAAI,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}