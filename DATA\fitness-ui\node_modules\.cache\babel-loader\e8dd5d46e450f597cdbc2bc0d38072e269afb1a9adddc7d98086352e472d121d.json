{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\ActivityFeed.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiHeart, FiMessageCircle, FiShare2, FiMoreHorizontal, FiMapPin, FiClock, FiTrendingUp, FiZap, FiAward, FiCamera, FiThumbsUp } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActivityFeed = ({\n  activities = [],\n  onActivitySelect\n}) => {\n  _s();\n  const [likedActivities, setLikedActivities] = useState(new Set());\n  const [comments, setComments] = useState({});\n  const handleLike = activityId => {\n    setLikedActivities(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(activityId)) {\n        newSet.delete(activityId);\n      } else {\n        newSet.add(activityId);\n      }\n      return newSet;\n    });\n  };\n  const getActivityIcon = type => {\n    const icons = {\n      running: '🏃‍♂️',\n      cycling: '🚴‍♂️',\n      hiking: '🥾',\n      walking: '🚶‍♂️',\n      swimming: '🏊‍♂️'\n    };\n    return icons[type] || '🏃‍♂️';\n  };\n  const getActivityColor = type => {\n    const colors = {\n      running: 'text-orange-500',\n      cycling: 'text-blue-500',\n      hiking: 'text-green-500',\n      walking: 'text-purple-500',\n      swimming: 'text-cyan-500'\n    };\n    return colors[type] || 'text-orange-500';\n  };\n  const formatTime = seconds => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\n  };\n  const formatPace = (distance, time) => {\n    const paceSeconds = time / distance;\n    const minutes = Math.floor(paceSeconds / 60);\n    const seconds = Math.floor(paceSeconds % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}/km`;\n  };\n\n  // Générer des activités fictives si aucune n'est fournie\n  useEffect(() => {\n    if (activities.length === 0) {\n      // Vous pouvez générer des activités fictives ici\n    }\n  }, [activities]);\n  const mockActivities = [{\n    id: 1,\n    user: {\n      name: 'Sophie Martin',\n      avatar: 'https://randomuser.me/api/portraits/women/1.jpg',\n      verified: true\n    },\n    type: 'running',\n    title: 'Course matinale au Parc',\n    description: 'Belle session ce matin ! Le temps était parfait 🌅',\n    distance: 8.5,\n    time: 2580,\n    // en secondes\n    elevation: 120,\n    pace: '5:02',\n    heartRate: 165,\n    calories: 420,\n    timestamp: '2024-01-15T07:30:00Z',\n    photos: ['https://picsum.photos/400/300?random=1'],\n    likes: 12,\n    comments: 3,\n    achievements: ['PR', 'Top 10%'],\n    route: {\n      name: 'Parcours du Parc Central',\n      segments: 2\n    },\n    weather: {\n      temp: 15,\n      condition: 'sunny'\n    }\n  }, {\n    id: 2,\n    user: {\n      name: 'Thomas Dubois',\n      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',\n      verified: false\n    },\n    type: 'cycling',\n    title: 'Sortie vélo en montagne',\n    description: 'Défi relevé ! Cette montée était intense 💪',\n    distance: 45.2,\n    time: 7200,\n    elevation: 850,\n    pace: '25.1',\n    heartRate: 145,\n    calories: 1250,\n    timestamp: '2024-01-14T14:15:00Z',\n    photos: [],\n    likes: 8,\n    comments: 1,\n    achievements: ['KOM'],\n    route: {\n      name: 'Col de la Croix',\n      segments: 5\n    },\n    weather: {\n      temp: 12,\n      condition: 'cloudy'\n    }\n  }];\n  const displayActivities = activities.length > 0 ? activities : mockActivities;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: displayActivities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: activity.user.avatar,\n              alt: activity.user.name,\n              className: \"w-10 h-10 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: activity.user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this), activity.user.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-500\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: getActivityColor(activity.type),\n                  children: getActivityIcon(activity.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: activity.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: new Date(activity.timestamp).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(FiMoreHorizontal, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [activity.description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 mb-4\",\n          children: activity.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: activity.distance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"km\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: formatTime(activity.time)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"temps\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: activity.elevation\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"m D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: activity.pace\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: activity.type === 'cycling' ? 'km/h' : 'min/km'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), activity.achievements && activity.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mb-4\",\n          children: activity.achievements.map((achievement, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `px-2 py-1 rounded-full text-xs font-medium ${achievement === 'PR' ? 'bg-yellow-100 text-yellow-800' : achievement === 'KOM' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiAward, {\n              className: \"inline h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 21\n            }, this), achievement]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 15\n        }, this), activity.photos && activity.photos.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: activity.photos[0],\n            alt: \"Activity\",\n            className: \"w-full h-48 object-cover rounded-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this), activity.route && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                className: \"h-4 w-4 text-gray-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-900\",\n                children: activity.route.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onActivitySelect && onActivitySelect(activity),\n              className: \"text-blue-600 hover:text-blue-700 text-sm font-medium\",\n              children: \"Voir sur la carte\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 17\n          }, this), activity.route.segments && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500 mt-1\",\n            children: [activity.route.segments, \" segments\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-3 border-t border-gray-50 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLike(activity.id),\n            className: `flex items-center space-x-2 transition-colors ${likedActivities.has(activity.id) ? 'text-red-500' : 'text-gray-500 hover:text-red-500'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: `h-5 w-5 ${likedActivities.has(activity.id) ? 'fill-current' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: activity.likes + (likedActivities.has(activity.id) ? 1 : 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FiMessageCircle, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: activity.comments\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FiShare2, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Partager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-gray-500 hover:text-blue-500 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(FiThumbsUp, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)]\n    }, activity.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivityFeed, \"jqcwfGjDl1SaooHnykAKWN6xv8U=\");\n_c = ActivityFeed;\nexport default ActivityFeed;\nvar _c;\n$RefreshReg$(_c, \"ActivityFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "FiMessageCircle", "FiShare2", "FiMoreHorizontal", "FiMapPin", "<PERSON><PERSON><PERSON>", "FiTrendingUp", "FiZap", "FiAward", "FiCamera", "FiThumbsUp", "jsxDEV", "_jsxDEV", "ActivityFeed", "activities", "onActivitySelect", "_s", "likedActivities", "setLikedActivities", "Set", "comments", "setComments", "handleLike", "activityId", "prev", "newSet", "has", "delete", "add", "getActivityIcon", "type", "icons", "running", "cycling", "hiking", "walking", "swimming", "getActivityColor", "colors", "formatTime", "seconds", "hours", "Math", "floor", "minutes", "formatPace", "distance", "time", "paceSeconds", "toString", "padStart", "length", "mockActivities", "id", "user", "name", "avatar", "verified", "title", "description", "elevation", "pace", "heartRate", "calories", "timestamp", "photos", "likes", "achievements", "route", "segments", "weather", "temp", "condition", "displayActivities", "className", "children", "map", "activity", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleDateString", "achievement", "index", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/ActivityFeed.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiHeart,\n  FiMessageCircle,\n  FiShare2,\n  FiMoreHorizontal,\n  FiMapPin,\n  FiClock,\n  FiTrendingUp,\n  FiZap,\n  FiAward,\n  FiCamera,\n  FiThumbsUp\n} from 'react-icons/fi';\n\nconst ActivityFeed = ({ activities = [], onActivitySelect }) => {\n  const [likedActivities, setLikedActivities] = useState(new Set());\n  const [comments, setComments] = useState({});\n\n  const handleLike = (activityId) => {\n    setLikedActivities(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(activityId)) {\n        newSet.delete(activityId);\n      } else {\n        newSet.add(activityId);\n      }\n      return newSet;\n    });\n  };\n\n  const getActivityIcon = (type) => {\n    const icons = {\n      running: '🏃‍♂️',\n      cycling: '🚴‍♂️',\n      hiking: '🥾',\n      walking: '🚶‍♂️',\n      swimming: '🏊‍♂️'\n    };\n    return icons[type] || '🏃‍♂️';\n  };\n\n  const getActivityColor = (type) => {\n    const colors = {\n      running: 'text-orange-500',\n      cycling: 'text-blue-500',\n      hiking: 'text-green-500',\n      walking: 'text-purple-500',\n      swimming: 'text-cyan-500'\n    };\n    return colors[type] || 'text-orange-500';\n  };\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\n  };\n\n  const formatPace = (distance, time) => {\n    const paceSeconds = time / distance;\n    const minutes = Math.floor(paceSeconds / 60);\n    const seconds = Math.floor(paceSeconds % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}/km`;\n  };\n\n  // Générer des activités fictives si aucune n'est fournie\n  useEffect(() => {\n    if (activities.length === 0) {\n      // Vous pouvez générer des activités fictives ici\n    }\n  }, [activities]);\n\n  const mockActivities = [\n    {\n      id: 1,\n      user: {\n        name: 'Sophie Martin',\n        avatar: 'https://randomuser.me/api/portraits/women/1.jpg',\n        verified: true\n      },\n      type: 'running',\n      title: 'Course matinale au Parc',\n      description: 'Belle session ce matin ! Le temps était parfait 🌅',\n      distance: 8.5,\n      time: 2580, // en secondes\n      elevation: 120,\n      pace: '5:02',\n      heartRate: 165,\n      calories: 420,\n      timestamp: '2024-01-15T07:30:00Z',\n      photos: ['https://picsum.photos/400/300?random=1'],\n      likes: 12,\n      comments: 3,\n      achievements: ['PR', 'Top 10%'],\n      route: {\n        name: 'Parcours du Parc Central',\n        segments: 2\n      },\n      weather: {\n        temp: 15,\n        condition: 'sunny'\n      }\n    },\n    {\n      id: 2,\n      user: {\n        name: 'Thomas Dubois',\n        avatar: 'https://randomuser.me/api/portraits/men/2.jpg',\n        verified: false\n      },\n      type: 'cycling',\n      title: 'Sortie vélo en montagne',\n      description: 'Défi relevé ! Cette montée était intense 💪',\n      distance: 45.2,\n      time: 7200,\n      elevation: 850,\n      pace: '25.1',\n      heartRate: 145,\n      calories: 1250,\n      timestamp: '2024-01-14T14:15:00Z',\n      photos: [],\n      likes: 8,\n      comments: 1,\n      achievements: ['KOM'],\n      route: {\n        name: 'Col de la Croix',\n        segments: 5\n      },\n      weather: {\n        temp: 12,\n        condition: 'cloudy'\n      }\n    }\n  ];\n\n  const displayActivities = activities.length > 0 ? activities : mockActivities;\n\n  return (\n    <div className=\"space-y-6\">\n      {displayActivities.map((activity) => (\n        <div key={activity.id} className=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <img\n                  src={activity.user.avatar}\n                  alt={activity.user.name}\n                  className=\"w-10 h-10 rounded-full\"\n                />\n                <div>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-semibold text-gray-900\">{activity.user.name}</span>\n                    {activity.user.verified && (\n                      <span className=\"text-blue-500\">✓</span>\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                    <span className={getActivityColor(activity.type)}>\n                      {getActivityIcon(activity.type)}\n                    </span>\n                    <span>{activity.title}</span>\n                    <span>•</span>\n                    <span>{new Date(activity.timestamp).toLocaleDateString()}</span>\n                  </div>\n                </div>\n              </div>\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <FiMoreHorizontal className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4\">\n            {activity.description && (\n              <p className=\"text-gray-700 mb-4\">{activity.description}</p>\n            )}\n\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-gray-900\">{activity.distance}</div>\n                <div className=\"text-sm text-gray-500\">km</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-gray-900\">{formatTime(activity.time)}</div>\n                <div className=\"text-sm text-gray-500\">temps</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-gray-900\">{activity.elevation}</div>\n                <div className=\"text-sm text-gray-500\">m D+</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-gray-900\">{activity.pace}</div>\n                <div className=\"text-sm text-gray-500\">\n                  {activity.type === 'cycling' ? 'km/h' : 'min/km'}\n                </div>\n              </div>\n            </div>\n\n            {/* Achievements */}\n            {activity.achievements && activity.achievements.length > 0 && (\n              <div className=\"flex items-center space-x-2 mb-4\">\n                {activity.achievements.map((achievement, index) => (\n                  <span\n                    key={index}\n                    className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      achievement === 'PR' ? 'bg-yellow-100 text-yellow-800' :\n                      achievement === 'KOM' ? 'bg-red-100 text-red-800' :\n                      'bg-blue-100 text-blue-800'\n                    }`}\n                  >\n                    <FiAward className=\"inline h-3 w-3 mr-1\" />\n                    {achievement}\n                  </span>\n                ))}\n              </div>\n            )}\n\n            {/* Photos */}\n            {activity.photos && activity.photos.length > 0 && (\n              <div className=\"mb-4\">\n                <img\n                  src={activity.photos[0]}\n                  alt=\"Activity\"\n                  className=\"w-full h-48 object-cover rounded-lg\"\n                />\n              </div>\n            )}\n\n            {/* Route Info */}\n            {activity.route && (\n              <div className=\"bg-gray-50 rounded-lg p-3 mb-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <FiMapPin className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"font-medium text-gray-900\">{activity.route.name}</span>\n                  </div>\n                  <button \n                    onClick={() => onActivitySelect && onActivitySelect(activity)}\n                    className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                  >\n                    Voir sur la carte\n                  </button>\n                </div>\n                {activity.route.segments && (\n                  <div className=\"text-sm text-gray-500 mt-1\">\n                    {activity.route.segments} segments\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Actions */}\n          <div className=\"px-4 py-3 border-t border-gray-50 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <button\n                onClick={() => handleLike(activity.id)}\n                className={`flex items-center space-x-2 transition-colors ${\n                  likedActivities.has(activity.id)\n                    ? 'text-red-500'\n                    : 'text-gray-500 hover:text-red-500'\n                }`}\n              >\n                <FiHeart className={`h-5 w-5 ${likedActivities.has(activity.id) ? 'fill-current' : ''}`} />\n                <span className=\"text-sm\">{activity.likes + (likedActivities.has(activity.id) ? 1 : 0)}</span>\n              </button>\n\n              <button className=\"flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors\">\n                <FiMessageCircle className=\"h-5 w-5\" />\n                <span className=\"text-sm\">{activity.comments}</span>\n              </button>\n\n              <button className=\"flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors\">\n                <FiShare2 className=\"h-5 w-5\" />\n                <span className=\"text-sm\">Partager</span>\n              </button>\n            </div>\n\n            <button className=\"text-gray-500 hover:text-blue-500 transition-colors\">\n              <FiThumbsUp className=\"h-5 w-5\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ActivityFeed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,EAChBC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAY,GAAGA,CAAC;EAAEC,UAAU,GAAG,EAAE;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,IAAIqB,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE5C,MAAMwB,UAAU,GAAIC,UAAU,IAAK;IACjCL,kBAAkB,CAACM,IAAI,IAAI;MACzB,MAAMC,MAAM,GAAG,IAAIN,GAAG,CAACK,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACH,UAAU,CAAC,EAAE;QAC1BE,MAAM,CAACE,MAAM,CAACJ,UAAU,CAAC;MAC3B,CAAC,MAAM;QACLE,MAAM,CAACG,GAAG,CAACL,UAAU,CAAC;MACxB;MACA,OAAOE,MAAM;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,KAAK,GAAG;MACZC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOL,KAAK,CAACD,IAAI,CAAC,IAAI,OAAO;EAC/B,CAAC;EAED,MAAMO,gBAAgB,GAAIP,IAAI,IAAK;IACjC,MAAMQ,MAAM,GAAG;MACbN,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAE,gBAAgB;MACxBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOE,MAAM,CAACR,IAAI,CAAC,IAAI,iBAAiB;EAC1C,CAAC;EAED,MAAMS,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,OAAOC,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG;EAC5D,CAAC;EAED,MAAMC,UAAU,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACrC,MAAMC,WAAW,GAAGD,IAAI,GAAGD,QAAQ;IACnC,MAAMF,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACK,WAAW,GAAG,EAAE,CAAC;IAC5C,MAAMR,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACK,WAAW,GAAG,EAAE,CAAC;IAC5C,OAAO,GAAGJ,OAAO,IAAIJ,OAAO,CAACS,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;EAC/D,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,CAACqC,MAAM,KAAK,CAAC,EAAE;MAC3B;IAAA;EAEJ,CAAC,EAAE,CAACrC,UAAU,CAAC,CAAC;EAEhB,MAAMsC,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;MACJC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE,iDAAiD;MACzDC,QAAQ,EAAE;IACZ,CAAC;IACD3B,IAAI,EAAE,SAAS;IACf4B,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,oDAAoD;IACjEb,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,IAAI;IAAE;IACZa,SAAS,EAAE,GAAG;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,sBAAsB;IACjCC,MAAM,EAAE,CAAC,wCAAwC,CAAC;IAClDC,KAAK,EAAE,EAAE;IACT9C,QAAQ,EAAE,CAAC;IACX+C,YAAY,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;IAC/BC,KAAK,EAAE;MACLb,IAAI,EAAE,0BAA0B;MAChCc,QAAQ,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE;IACb;EACF,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;MACJC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE,+CAA+C;MACvDC,QAAQ,EAAE;IACZ,CAAC;IACD3B,IAAI,EAAE,SAAS;IACf4B,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,6CAA6C;IAC1Db,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVa,SAAS,EAAE,GAAG;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,sBAAsB;IACjCC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,CAAC;IACR9C,QAAQ,EAAE,CAAC;IACX+C,YAAY,EAAE,CAAC,KAAK,CAAC;IACrBC,KAAK,EAAE;MACLb,IAAI,EAAE,iBAAiB;MACvBc,QAAQ,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE;IACb;EACF,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAG3D,UAAU,CAACqC,MAAM,GAAG,CAAC,GAAGrC,UAAU,GAAGsC,cAAc;EAE7E,oBACExC,OAAA;IAAK8D,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBF,iBAAiB,CAACG,GAAG,CAAEC,QAAQ,iBAC9BjE,OAAA;MAAuB8D,SAAS,EAAC,wGAAwG;MAAAC,QAAA,gBAEvI/D,OAAA;QAAK8D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C/D,OAAA;UAAK8D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD/D,OAAA;YAAK8D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C/D,OAAA;cACEkE,GAAG,EAAED,QAAQ,CAACvB,IAAI,CAACE,MAAO;cAC1BuB,GAAG,EAAEF,QAAQ,CAACvB,IAAI,CAACC,IAAK;cACxBmB,SAAS,EAAC;YAAwB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACFvE,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAK8D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C/D,OAAA;kBAAM8D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEE,QAAQ,CAACvB,IAAI,CAACC;gBAAI;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACxEN,QAAQ,CAACvB,IAAI,CAACG,QAAQ,iBACrB7C,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACxC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvE,OAAA;gBAAK8D,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChE/D,OAAA;kBAAM8D,SAAS,EAAErC,gBAAgB,CAACwC,QAAQ,CAAC/C,IAAI,CAAE;kBAAA6C,QAAA,EAC9C9C,eAAe,CAACgD,QAAQ,CAAC/C,IAAI;gBAAC;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACPvE,OAAA;kBAAA+D,QAAA,EAAOE,QAAQ,CAACnB;gBAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BvE,OAAA;kBAAA+D,QAAA,EAAM;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACdvE,OAAA;kBAAA+D,QAAA,EAAO,IAAIS,IAAI,CAACP,QAAQ,CAACb,SAAS,CAAC,CAACqB,kBAAkB,CAAC;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvE,OAAA;YAAQ8D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eACnD/D,OAAA,CAACT,gBAAgB;cAACuE,SAAS,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvE,OAAA;QAAK8D,SAAS,EAAC,KAAK;QAAAC,QAAA,GACjBE,QAAQ,CAAClB,WAAW,iBACnB/C,OAAA;UAAG8D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEE,QAAQ,CAAClB;QAAW;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC5D,eAGDvE,OAAA;UAAK8D,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD/D,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAK8D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEE,QAAQ,CAAC/B;YAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3EvE,OAAA;cAAK8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNvE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAK8D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEpC,UAAU,CAACsC,QAAQ,CAAC9B,IAAI;YAAC;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFvE,OAAA;cAAK8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNvE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAK8D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEE,QAAQ,CAACjB;YAAS;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5EvE,OAAA;cAAK8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNvE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAK8D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEE,QAAQ,CAAChB;YAAI;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEvE,OAAA;cAAK8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCE,QAAQ,CAAC/C,IAAI,KAAK,SAAS,GAAG,MAAM,GAAG;YAAQ;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLN,QAAQ,CAACV,YAAY,IAAIU,QAAQ,CAACV,YAAY,CAAChB,MAAM,GAAG,CAAC,iBACxDvC,OAAA;UAAK8D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC9CE,QAAQ,CAACV,YAAY,CAACS,GAAG,CAAC,CAACU,WAAW,EAAEC,KAAK,kBAC5C3E,OAAA;YAEE8D,SAAS,EAAE,8CACTY,WAAW,KAAK,IAAI,GAAG,+BAA+B,GACtDA,WAAW,KAAK,KAAK,GAAG,yBAAyB,GACjD,2BAA2B,EAC1B;YAAAX,QAAA,gBAEH/D,OAAA,CAACJ,OAAO;cAACkE,SAAS,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CG,WAAW;UAAA,GARPC,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAN,QAAQ,CAACZ,MAAM,IAAIY,QAAQ,CAACZ,MAAM,CAACd,MAAM,GAAG,CAAC,iBAC5CvC,OAAA;UAAK8D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/D,OAAA;YACEkE,GAAG,EAAED,QAAQ,CAACZ,MAAM,CAAC,CAAC,CAAE;YACxBc,GAAG,EAAC,UAAU;YACdL,SAAS,EAAC;UAAqC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAN,QAAQ,CAACT,KAAK,iBACbxD,OAAA;UAAK8D,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C/D,OAAA;YAAK8D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/D,OAAA;cAAK8D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C/D,OAAA,CAACR,QAAQ;gBAACsE,SAAS,EAAC;cAAuB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CvE,OAAA;gBAAM8D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEE,QAAQ,CAACT,KAAK,CAACb;cAAI;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNvE,OAAA;cACE4E,OAAO,EAAEA,CAAA,KAAMzE,gBAAgB,IAAIA,gBAAgB,CAAC8D,QAAQ,CAAE;cAC9DH,SAAS,EAAC,uDAAuD;cAAAC,QAAA,EAClE;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLN,QAAQ,CAACT,KAAK,CAACC,QAAQ,iBACtBzD,OAAA;YAAK8D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACxCE,QAAQ,CAACT,KAAK,CAACC,QAAQ,EAAC,WAC3B;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNvE,OAAA;QAAK8D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF/D,OAAA;UAAK8D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/D,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAMlE,UAAU,CAACuD,QAAQ,CAACxB,EAAE,CAAE;YACvCqB,SAAS,EAAE,iDACTzD,eAAe,CAACS,GAAG,CAACmD,QAAQ,CAACxB,EAAE,CAAC,GAC5B,cAAc,GACd,kCAAkC,EACrC;YAAAsB,QAAA,gBAEH/D,OAAA,CAACZ,OAAO;cAAC0E,SAAS,EAAE,WAAWzD,eAAe,CAACS,GAAG,CAACmD,QAAQ,CAACxB,EAAE,CAAC,GAAG,cAAc,GAAG,EAAE;YAAG;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3FvE,OAAA;cAAM8D,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEE,QAAQ,CAACX,KAAK,IAAIjD,eAAe,CAACS,GAAG,CAACmD,QAAQ,CAACxB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YAAC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eAETvE,OAAA;YAAQ8D,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBACjG/D,OAAA,CAACX,eAAe;cAACyE,SAAS,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCvE,OAAA;cAAM8D,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEE,QAAQ,CAACzD;YAAQ;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAETvE,OAAA;YAAQ8D,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAClG/D,OAAA,CAACV,QAAQ;cAACwE,SAAS,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChCvE,OAAA;cAAM8D,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvE,OAAA;UAAQ8D,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eACrE/D,OAAA,CAACF,UAAU;YAACgE,SAAS,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,GAhJEN,QAAQ,CAACxB,EAAE;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiJhB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnE,EAAA,CAnRIH,YAAY;AAAA4E,EAAA,GAAZ5E,YAAY;AAqRlB,eAAeA,YAAY;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}