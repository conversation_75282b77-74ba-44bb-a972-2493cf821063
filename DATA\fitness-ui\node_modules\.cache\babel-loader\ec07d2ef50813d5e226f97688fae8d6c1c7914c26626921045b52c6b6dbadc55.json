{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\ModernMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { MapContainer, <PERSON>ile<PERSON><PERSON>er, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiLayers, FiMaximize2, FiMinimize2, FiNavigation, FiZoomIn, FiZoomOut, FiMapPin, FiTarget, FiMap, FiGlobe, FiMoon, FiTriangle } from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiSatellite\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiMountain\n  }\n};\n\n// Strava-style colors\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  green: '#00D924',\n  red: '#FF0000',\n  purple: '#8B5CF6',\n  yellow: '#FFC107'\n};\n\n// Custom Strava-style icons\nconst createStravaIcon = (type, color = stravaColors.orange) => {\n  const iconMap = {\n    start: '🏁',\n    finish: '🏆',\n    segment: '⚡',\n    poi: '📍',\n    user: '👤'\n  };\n  return L.divIcon({\n    html: `\n      <div style=\"\n        background: linear-gradient(135deg, ${color} 0%, ${color}dd 100%);\n        color: white;\n        border-radius: 12px;\n        width: 36px;\n        height: 36px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n      \">${iconMap[type] || '📍'}</div>\n    `,\n    className: 'strava-marker',\n    iconSize: [36, 36],\n    iconAnchor: [18, 18]\n  });\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({\n  onStyleChange,\n  currentStyle,\n  onLocate,\n  onFullscreen,\n  isFullscreen\n}) => {\n  _s();\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute top-4 right-4 z-[1000] space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowStyleSelector(!showStyleSelector),\n        className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\",\n        style: {\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n        },\n        title: \"Change map style\",\n        children: /*#__PURE__*/_jsxDEV(FiLayers, {\n          className: \"h-5 w-5 text-gray-700\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), showStyleSelector && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\",\n        style: {\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: Object.entries(mapStyles).map(([key, style]) => {\n          const IconComponent = style.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              onStyleChange(key);\n              setShowStyleSelector(false);\n            },\n            className: `w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'}`,\n            style: currentStyle === key ? {\n              backgroundColor: '#FFF7ED',\n              color: stravaColors.orange\n            } : {},\n            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: style.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onLocate,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: \"Center on your location\",\n      children: /*#__PURE__*/_jsxDEV(FiNavigation, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onFullscreen,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\",\n      children: isFullscreen ? /*#__PURE__*/_jsxDEV(FiMinimize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(FiMaximize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n\n// Map event handler\n_s(StravaMapControls, \"PY0GN+P+8EDZXt82pN67/JEoRp8=\");\n_c = StravaMapControls;\nconst MapEventHandler = ({\n  onMapClick,\n  onMapReady\n}) => {\n  _s2();\n  const map = useMap();\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    }\n  });\n  return null;\n};\n\n// Strava-style zoom controls\n_s2(MapEventHandler, \"tmcOhplWkk/SgX5HNxHxB5dt97g=\", false, function () {\n  return [useMap, useMapEvents];\n});\n_c2 = MapEventHandler;\nconst StravaZoomControl = () => {\n  _s3();\n  const map = useMap();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\",\n    style: {\n      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomIn(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\",\n      title: \"Zoom in\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomIn, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomOut(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150\",\n      title: \"Zoom out\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomOut, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s3(StravaZoomControl, \"cX187cvZ2hODbkaiLn05gMk1sCM=\", false, function () {\n  return [useMap];\n});\n_c3 = StravaZoomControl;\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  _s4();\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const mapContainerRef = useRef(null);\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        var _mapContainerRef$curr, _mapContainerRef$curr2;\n        (_mapContainerRef$curr = (_mapContainerRef$curr2 = mapContainerRef.current).requestFullscreen) === null || _mapContainerRef$curr === void 0 ? void 0 : _mapContainerRef$curr.call(_mapContainerRef$curr2);\n      } else {\n        var _document$exitFullscr, _document;\n        (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n  const currentMapStyle = mapStyles[mapStyle];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: mapContainerRef,\n    className: `relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`,\n    style: {\n      boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${height} relative`,\n      children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n        center: center,\n        zoom: zoom,\n        style: {\n          height: '100%',\n          width: '100%'\n        },\n        zoomControl: false,\n        attributionControl: false,\n        className: \"rounded-xl\",\n        children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n          url: currentMapStyle.url,\n          attribution: currentMapStyle.attribution,\n          maxZoom: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MapEventHandler, {\n          onMapClick: onMapClick,\n          onMapReady: setMapInstance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StravaZoomControl, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), userPosition && /*#__PURE__*/_jsxDEV(Marker, {\n          position: [userPosition.lat, userPosition.lng],\n          icon: createStravaIcon('user', stravaColors.blue),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-gray-900\",\n                children: \"Your Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Current position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this), routes.map(route => {\n          const isSelected = (selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id;\n          const routeColor = isSelected ? stravaColors.orange : stravaColors.blue;\n          return /*#__PURE__*/_jsxDEV(Polyline, {\n            positions: route.points.map(p => [p.lat, p.lng]),\n            color: routeColor,\n            weight: isSelected ? 6 : 4,\n            opacity: isSelected ? 1 : 0.7,\n            eventHandlers: {\n              click: () => onRouteSelect && onRouteSelect(route)\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900 mb-2\",\n                  style: {\n                    color: stravaColors.orange\n                  },\n                  children: route.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-2 text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Distance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: [route.distance, \"km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Difficulty\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: route.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)\n          }, route.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this);\n        }), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n          positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n          color: stravaColors.orange,\n          weight: 6,\n          opacity: 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n          positions: optimizedRoute.map(p => [p.lat, p.lng]),\n          color: stravaColors.green,\n          weight: 5,\n          opacity: 0.9,\n          dashArray: \"8, 8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [point.lat, point.lng],\n          icon: createStravaIcon('poi', stravaColors.yellow),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-gray-900\",\n                children: [\"Point \", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Planning waypoint\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [poi.lat, poi.lng],\n          icon: createStravaIcon('poi', stravaColors.green),\n          eventHandlers: {\n            click: () => onPOISelect && onPOISelect(poi)\n          },\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                style: {\n                  color: stravaColors.green\n                },\n                children: poi.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mb-2\",\n                children: poi.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), poi.rating && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center bg-yellow-50 p-2 rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-500\",\n                  children: \"\\u2B50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm ml-1 font-medium\",\n                  children: poi.rating.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, poi.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [poi.lat, poi.lng],\n          icon: createStravaIcon('poi', stravaColors.red)\n        }, `selected-${poi.id}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)), (showSegments || selectedSegment) && segments.map(segment => {\n          const isSelected = (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id;\n          const segmentColor = isSelected ? stravaColors.orange : segment.type === 'climb' ? stravaColors.red : segment.type === 'sprint' ? stravaColors.green : segment.type === 'descent' ? stravaColors.blue : stravaColors.purple;\n          return /*#__PURE__*/_jsxDEV(Polyline, {\n            positions: segment.points.map(point => [point.lat, point.lng]),\n            color: segmentColor,\n            weight: isSelected ? 7 : 5,\n            opacity: isSelected ? 1 : 0.8,\n            eventHandlers: {\n              click: () => onSegmentSelect && onSegmentSelect(segment)\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900 mb-2\",\n                  style: {\n                    color: segmentColor\n                  },\n                  children: segment.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3\",\n                  children: segment.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-2 text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Distance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Elevation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Record\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.recordTime\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Attempts\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.attempts\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)\n          }, segment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StravaMapControls, {\n        onStyleChange: setMapStyle,\n        currentStyle: mapStyle,\n        onLocate: handleLocate,\n        onFullscreen: handleFullscreen,\n        isFullscreen: isFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s4(ModernMap, \"3DbC9vvdlyb0msnTDPDDuDkiJw0=\");\n_c4 = ModernMap;\nexport default ModernMap;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StravaMapControls\");\n$RefreshReg$(_c2, \"MapEventHandler\");\n$RefreshReg$(_c3, \"StravaZoomControl\");\n$RefreshReg$(_c4, \"ModernMap\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "useMap", "L", "FiLayers", "FiMaximize2", "FiMinimize2", "FiNavigation", "FiZoomIn", "FiZoomOut", "FiMapPin", "<PERSON><PERSON><PERSON><PERSON>", "FiMap", "FiGlobe", "FiMoon", "FiTriangle", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "mapStyles", "strava", "url", "attribution", "name", "icon", "satellite", "FiSatellite", "dark", "terrain", "FiMountain", "stravaColors", "orange", "darkOrange", "blue", "green", "red", "purple", "yellow", "createStravaIcon", "type", "color", "iconMap", "start", "finish", "segment", "poi", "user", "divIcon", "html", "className", "iconSize", "iconAnchor", "StravaMapControls", "onStyleChange", "currentStyle", "onLocate", "onFullscreen", "isFullscreen", "_s", "showStyleSelector", "setShowStyleSelector", "children", "onClick", "style", "boxShadow", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "map", "key", "IconComponent", "backgroundColor", "_c", "MapEventHandler", "onMapClick", "onMapReady", "_s2", "click", "ready", "_c2", "StravaZoomControl", "_s3", "zoomIn", "zoomOut", "_c3", "ModernMap", "center", "zoom", "userPosition", "routes", "segments", "pois", "<PERSON><PERSON><PERSON><PERSON>", "selectedSegment", "selectedPOIs", "optimizedRoute", "planningPoints", "showPOIs", "showSegments", "onRouteSelect", "onSegmentSelect", "onPOISelect", "height", "_s4", "mapStyle", "setMapStyle", "setIsFullscreen", "mapInstance", "setMapInstance", "mapContainerRef", "handleLocate", "<PERSON><PERSON><PERSON><PERSON>", "lat", "lng", "animate", "duration", "handleFullscreen", "current", "_mapContainerRef$curr", "_mapContainerRef$curr2", "requestFullscreen", "call", "_document$exitFullscr", "_document", "document", "exitFullscreen", "currentMapStyle", "ref", "width", "zoomControl", "attributionControl", "max<PERSON><PERSON>", "position", "route", "isSelected", "id", "routeColor", "positions", "points", "p", "weight", "opacity", "eventHandlers", "description", "distance", "difficulty", "dashArray", "point", "index", "rating", "toFixed", "segmentColor", "elevation", "recordTime", "attempts", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/ModernMap.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiLayers,\n  FiMaximize2,\n  FiMinimize2,\n  FiNavigation,\n  FiZoomIn,\n  FiZoomOut,\n  FiMapPin,\n  FiTarget,\n  FiMap,\n  FiGlobe,\n  FiMoon,\n  FiTriangle\n} from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiSatellite\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiMountain\n  }\n};\n\n// Strava-style colors\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  green: '#00D924',\n  red: '#FF0000',\n  purple: '#8B5CF6',\n  yellow: '#FFC107'\n};\n\n// Custom Strava-style icons\nconst createStravaIcon = (type, color = stravaColors.orange) => {\n  const iconMap = {\n    start: '🏁',\n    finish: '🏆',\n    segment: '⚡',\n    poi: '📍',\n    user: '👤'\n  };\n\n  return L.divIcon({\n    html: `\n      <div style=\"\n        background: linear-gradient(135deg, ${color} 0%, ${color}dd 100%);\n        color: white;\n        border-radius: 12px;\n        width: 36px;\n        height: 36px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n      \">${iconMap[type] || '📍'}</div>\n    `,\n    className: 'strava-marker',\n    iconSize: [36, 36],\n    iconAnchor: [18, 18]\n  });\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n\n  return (\n    <div className=\"absolute top-4 right-4 z-[1000] space-y-3\">\n      {/* Style Selector with Strava design */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowStyleSelector(!showStyleSelector)}\n          className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\"\n          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n          title=\"Change map style\"\n        >\n          <FiLayers className=\"h-5 w-5 text-gray-700\" />\n        </button>\n\n        {showStyleSelector && (\n          <div className=\"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\"\n               style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}>\n            {Object.entries(mapStyles).map(([key, style]) => {\n              const IconComponent = style.icon;\n              return (\n                <button\n                  key={key}\n                  onClick={() => {\n                    onStyleChange(key);\n                    setShowStyleSelector(false);\n                  }}\n                  className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${\n                    currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'\n                  }`}\n                  style={currentStyle === key ? { backgroundColor: '#FFF7ED', color: stravaColors.orange } : {}}\n                >\n                  <IconComponent className=\"h-4 w-4\" />\n                  <span>{style.name}</span>\n                </button>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Locate button with Strava style */}\n      <button\n        onClick={onLocate}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title=\"Center on your location\"\n      >\n        <FiNavigation className=\"h-5 w-5 text-gray-700\" />\n      </button>\n\n      {/* Fullscreen button with Strava style */}\n      <button\n        onClick={onFullscreen}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title={isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n      >\n        {isFullscreen ? (\n          <FiMinimize2 className=\"h-5 w-5 text-gray-700\" />\n        ) : (\n          <FiMaximize2 className=\"h-5 w-5 text-gray-700\" />\n        )}\n      </button>\n    </div>\n  );\n};\n\n// Map event handler\nconst MapEventHandler = ({ onMapClick, onMapReady }) => {\n  const map = useMap();\n\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    }\n  });\n\n  return null;\n};\n\n// Strava-style zoom controls\nconst StravaZoomControl = () => {\n  const map = useMap();\n\n  return (\n    <div className=\"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\"\n         style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>\n      <button\n        onClick={() => map.zoomIn()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\"\n        title=\"Zoom in\"\n      >\n        <FiZoomIn className=\"h-5 w-5 text-gray-700\" />\n      </button>\n      <button\n        onClick={() => map.zoomOut()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150\"\n        title=\"Zoom out\"\n      >\n        <FiZoomOut className=\"h-5 w-5 text-gray-700\" />\n      </button>\n    </div>\n  );\n};\n\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const mapContainerRef = useRef(null);\n\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        mapContainerRef.current.requestFullscreen?.();\n      } else {\n        document.exitFullscreen?.();\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n\n  const currentMapStyle = mapStyles[mapStyle];\n\n  return (\n    <div\n      ref={mapContainerRef}\n      className={`relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`}\n      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}\n    >\n      <div className={`${height} relative`}>\n        <MapContainer\n          center={center}\n          zoom={zoom}\n          style={{ height: '100%', width: '100%' }}\n          zoomControl={false}\n          attributionControl={false}\n          className=\"rounded-xl\"\n        >\n          <TileLayer\n            url={currentMapStyle.url}\n            attribution={currentMapStyle.attribution}\n            maxZoom={18}\n          />\n\n          <MapEventHandler\n            onMapClick={onMapClick}\n            onMapReady={setMapInstance}\n          />\n\n          <StravaZoomControl />\n          \n          {/* User position marker with Strava style */}\n          {userPosition && (\n            <Marker\n              position={[userPosition.lat, userPosition.lng]}\n              icon={createStravaIcon('user', stravaColors.blue)}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"text-center p-2\">\n                  <strong className=\"text-gray-900\">Your Location</strong>\n                  <div className=\"text-sm text-gray-600 mt-1\">Current position</div>\n                </div>\n              </Popup>\n            </Marker>\n          )}\n\n          {/* Routes with Strava colors */}\n          {routes.map(route => {\n            const isSelected = selectedRoute?.id === route.id;\n            const routeColor = isSelected ? stravaColors.orange : stravaColors.blue;\n\n            return (\n              <Polyline\n                key={route.id}\n                positions={route.points.map(p => [p.lat, p.lng])}\n                color={routeColor}\n                weight={isSelected ? 6 : 4}\n                opacity={isSelected ? 1 : 0.7}\n                eventHandlers={{\n                  click: () => onRouteSelect && onRouteSelect(route)\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div className=\"p-3\">\n                    <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: stravaColors.orange }}>\n                      {route.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mb-3\">{route.description}</p>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Distance</div>\n                        <div className=\"text-gray-900\">{route.distance}km</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Difficulty</div>\n                        <div className=\"text-gray-900\">{route.difficulty}</div>\n                      </div>\n                    </div>\n                  </div>\n                </Popup>\n              </Polyline>\n            );\n          })}\n\n          {/* Selected route highlight with Strava orange */}\n          {selectedRoute && (\n            <Polyline\n              positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n              color={stravaColors.orange}\n              weight={6}\n              opacity={1}\n            />\n          )}\n\n          {/* Optimized route with Strava green */}\n          {optimizedRoute && (\n            <Polyline\n              positions={optimizedRoute.map(p => [p.lat, p.lng])}\n              color={stravaColors.green}\n              weight={5}\n              opacity={0.9}\n              dashArray=\"8, 8\"\n            />\n          )}\n\n          {/* Planning points with Strava style */}\n          {planningPoints.map((point, index) => (\n            <Marker\n              key={index}\n              position={[point.lat, point.lng]}\n              icon={createStravaIcon('poi', stravaColors.yellow)}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"text-center p-2\">\n                  <strong className=\"text-gray-900\">Point {index + 1}</strong>\n                  <div className=\"text-sm text-gray-600 mt-1\">Planning waypoint</div>\n                </div>\n              </Popup>\n            </Marker>\n          ))}\n\n          {/* POIs with Strava style */}\n          {showPOIs && pois.map(poi => (\n            <Marker\n              key={poi.id}\n              position={[poi.lat, poi.lng]}\n              icon={createStravaIcon('poi', stravaColors.green)}\n              eventHandlers={{\n                click: () => onPOISelect && onPOISelect(poi)\n              }}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"p-3\">\n                  <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: stravaColors.green }}>\n                    {poi.name}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 mb-2\">{poi.type}</p>\n                  {poi.rating && (\n                    <div className=\"flex items-center bg-yellow-50 p-2 rounded\">\n                      <span className=\"text-yellow-500\">⭐</span>\n                      <span className=\"text-sm ml-1 font-medium\">{poi.rating.toFixed(1)}</span>\n                    </div>\n                  )}\n                </div>\n              </Popup>\n            </Marker>\n          ))}\n\n          {/* Selected POIs highlight */}\n          {selectedPOIs.map(poi => (\n            <Marker\n              key={`selected-${poi.id}`}\n              position={[poi.lat, poi.lng]}\n              icon={createStravaIcon('poi', stravaColors.red)}\n            />\n          ))}\n\n          {/* Segments with Strava colors */}\n          {(showSegments || selectedSegment) && segments.map(segment => {\n            const isSelected = selectedSegment?.id === segment.id;\n            const segmentColor = isSelected ? stravaColors.orange :\n                               segment.type === 'climb' ? stravaColors.red :\n                               segment.type === 'sprint' ? stravaColors.green :\n                               segment.type === 'descent' ? stravaColors.blue :\n                               stravaColors.purple;\n\n            return (\n              <Polyline\n                key={segment.id}\n                positions={segment.points.map(point => [point.lat, point.lng])}\n                color={segmentColor}\n                weight={isSelected ? 7 : 5}\n                opacity={isSelected ? 1 : 0.8}\n                eventHandlers={{\n                  click: () => onSegmentSelect && onSegmentSelect(segment)\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div className=\"p-3\">\n                    <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: segmentColor }}>\n                      {segment.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mb-3\">{segment.description}</p>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Distance</div>\n                        <div className=\"text-gray-900\">\n                          {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                        </div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Elevation</div>\n                        <div className=\"text-gray-900\">\n                          {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}\n                        </div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Record</div>\n                        <div className=\"text-gray-900\">{segment.recordTime}</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Attempts</div>\n                        <div className=\"text-gray-900\">{segment.attempts}</div>\n                      </div>\n                    </div>\n                  </div>\n                </Popup>\n              </Polyline>\n            );\n          })}\n        </MapContainer>\n\n        {/* Strava-style controls overlay */}\n        <StravaMapControls\n          onStyleChange={setMapStyle}\n          currentStyle={mapStyle}\n          onLocate={handleLocate}\n          onFullscreen={handleFullscreen}\n          isFullscreen={isFullscreen}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ModernMap;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AACtG,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,gBAAgB;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOd,CAAC,CAACe,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3ClB,CAAC,CAACe,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE;IACNC,GAAG,EAAE,0EAA0E;IAC/EC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEnB;EACR,CAAC;EACDoB,SAAS,EAAE;IACTJ,GAAG,EAAE,+FAA+F;IACpGC,WAAW,EAAE,iDAAiD;IAC9DC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAEE;EACR,CAAC;EACDC,IAAI,EAAE;IACJN,GAAG,EAAE,+DAA+D;IACpEC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAEjB;EACR,CAAC;EACDqB,OAAO,EAAE;IACPP,GAAG,EAAE,kDAAkD;IACvDC,WAAW,EAAE,4NAA4N;IACzOC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAEK;EACR;AACF,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,GAAG,EAAE,SAAS;EACdC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,KAAK,GAAGV,YAAY,CAACC,MAAM,KAAK;EAC9D,MAAMU,OAAO,GAAG;IACdC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,GAAG;IACZC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE;EACR,CAAC;EAED,OAAOlD,CAAC,CAACmD,OAAO,CAAC;IACfC,IAAI,EAAE;AACV;AACA,8CAA8CR,KAAK,QAAQA,KAAK;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUC,OAAO,CAACF,IAAI,CAAC,IAAI,IAAI;AAC/B,KAAK;IACDU,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACnG,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAEjE,oBACEwB,OAAA;IAAKuC,SAAS,EAAC,2CAA2C;IAAAY,QAAA,gBAExDnD,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAAY,QAAA,gBACvBnD,OAAA;QACEoD,OAAO,EAAEA,CAAA,KAAMF,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;QACxDV,SAAS,EAAC,yGAAyG;QACnHc,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QACpDC,KAAK,EAAC,kBAAkB;QAAAJ,QAAA,eAExBnD,OAAA,CAACb,QAAQ;UAACoD,SAAS,EAAC;QAAuB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAERV,iBAAiB,iBAChBjD,OAAA;QAAKuC,SAAS,EAAC,sGAAsG;QAChHc,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QAAAH,QAAA,EACtDS,MAAM,CAACC,OAAO,CAACpD,SAAS,CAAC,CAACqD,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEV,KAAK,CAAC,KAAK;UAC/C,MAAMW,aAAa,GAAGX,KAAK,CAACvC,IAAI;UAChC,oBACEd,OAAA;YAEEoD,OAAO,EAAEA,CAAA,KAAM;cACbT,aAAa,CAACoB,GAAG,CAAC;cAClBb,oBAAoB,CAAC,KAAK,CAAC;YAC7B,CAAE;YACFX,SAAS,EAAE,+GACTK,YAAY,KAAKmB,GAAG,GAAG,0CAA0C,GAAG,eAAe,EAClF;YACHV,KAAK,EAAET,YAAY,KAAKmB,GAAG,GAAG;cAAEE,eAAe,EAAE,SAAS;cAAEnC,KAAK,EAAEV,YAAY,CAACC;YAAO,CAAC,GAAG,CAAC,CAAE;YAAA8B,QAAA,gBAE9FnD,OAAA,CAACgE,aAAa;cAACzB,SAAS,EAAC;YAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrC3D,OAAA;cAAAmD,QAAA,EAAOE,KAAK,CAACxC;YAAI;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAXpBI,GAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYF,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3D,OAAA;MACEoD,OAAO,EAAEP,QAAS;MAClBN,SAAS,EAAC,+GAA+G;MACzHc,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDC,KAAK,EAAC,yBAAyB;MAAAJ,QAAA,eAE/BnD,OAAA,CAACV,YAAY;QAACiD,SAAS,EAAC;MAAuB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAGT3D,OAAA;MACEoD,OAAO,EAAEN,YAAa;MACtBP,SAAS,EAAC,+GAA+G;MACzHc,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDC,KAAK,EAAER,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;MAAAI,QAAA,EAE5DJ,YAAY,gBACX/C,OAAA,CAACX,WAAW;QAACkD,SAAS,EAAC;MAAuB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEjD3D,OAAA,CAACZ,WAAW;QAACmD,SAAS,EAAC;MAAuB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAX,EAAA,CArEMN,iBAAiB;AAAAwB,EAAA,GAAjBxB,iBAAiB;AAsEvB,MAAMyB,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAW,CAAC,KAAK;EAAAC,GAAA;EACtD,MAAMR,GAAG,GAAG7E,MAAM,CAAC,CAAC;EAEpBD,YAAY,CAAC;IACXuF,KAAK,EAAEH,UAAU;IACjBI,KAAK,EAAEA,CAAA,KAAM;MACXH,UAAU,IAAIA,UAAU,CAACP,GAAG,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,OAAO,IAAI;AACb,CAAC;;AAED;AAAAQ,GAAA,CAbMH,eAAe;EAAA,QACPlF,MAAM,EAElBD,YAAY;AAAA;AAAAyF,GAAA,GAHRN,eAAe;AAcrB,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAMb,GAAG,GAAG7E,MAAM,CAAC,CAAC;EAEpB,oBACEe,OAAA;IAAKuC,SAAS,EAAC,gGAAgG;IAC1Gc,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,gBACvDnD,OAAA;MACEoD,OAAO,EAAEA,CAAA,KAAMU,GAAG,CAACc,MAAM,CAAC,CAAE;MAC5BrC,SAAS,EAAC,2EAA2E;MACrFgB,KAAK,EAAC,SAAS;MAAAJ,QAAA,eAEfnD,OAAA,CAACT,QAAQ;QAACgD,SAAS,EAAC;MAAuB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eACT3D,OAAA;MACEoD,OAAO,EAAEA,CAAA,KAAMU,GAAG,CAACe,OAAO,CAAC,CAAE;MAC7BtC,SAAS,EAAC,kDAAkD;MAC5DgB,KAAK,EAAC,UAAU;MAAAJ,QAAA,eAEhBnD,OAAA,CAACR,SAAS;QAAC+C,SAAS,EAAC;MAAuB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACgB,GAAA,CAtBID,iBAAiB;EAAA,QACTzF,MAAM;AAAA;AAAA6F,GAAA,GADdJ,iBAAiB;AAwBvB,MAAMK,SAAS,GAAGA,CAAC;EACjBC,MAAM;EACNC,IAAI,GAAG,EAAE;EACTC,YAAY;EACZC,MAAM,GAAG,EAAE;EACXC,QAAQ,GAAG,EAAE;EACbC,IAAI,GAAG,EAAE;EACTC,aAAa;EACbC,eAAe;EACfC,YAAY,GAAG,EAAE;EACjBC,cAAc;EACdC,cAAc,GAAG,EAAE;EACnBC,QAAQ,GAAG,IAAI;EACfC,YAAY,GAAG,KAAK;EACpBxB,UAAU;EACVyB,aAAa;EACbC,eAAe;EACfC,WAAW;EACXxD,SAAS,GAAG,EAAE;EACdyD,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3H,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACuE,YAAY,EAAEqD,eAAe,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6H,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM+H,eAAe,GAAG9H,MAAM,CAAC,IAAI,CAAC;EAEpC,MAAM+H,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,WAAW,IAAInB,YAAY,EAAE;MAC/BmB,WAAW,CAACI,OAAO,CAAC,CAACvB,YAAY,CAACwB,GAAG,EAAExB,YAAY,CAACyB,GAAG,CAAC,EAAE,EAAE,EAAE;QAC5DC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,eAAe,CAACQ,OAAO,EAAE;MAC3B,IAAI,CAAChE,YAAY,EAAE;QAAA,IAAAiE,qBAAA,EAAAC,sBAAA;QACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAV,eAAe,CAACQ,OAAO,EAACG,iBAAiB,cAAAF,qBAAA,uBAAzCA,qBAAA,CAAAG,IAAA,CAAAF,sBAA4C,CAAC;MAC/C,CAAC,MAAM;QAAA,IAAAG,qBAAA,EAAAC,SAAA;QACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAC,QAAQ,EAACC,cAAc,cAAAH,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC7B;MACAjB,eAAe,CAAC,CAACrD,YAAY,CAAC;IAChC;EACF,CAAC;EAED,MAAMyE,eAAe,GAAG/G,SAAS,CAACyF,QAAQ,CAAC;EAE3C,oBACElG,OAAA;IACEyH,GAAG,EAAElB,eAAgB;IACrBhE,SAAS,EAAE,0DAA0DA,SAAS,EAAG;IACjFc,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,eAEpDnD,OAAA;MAAKuC,SAAS,EAAE,GAAGyD,MAAM,WAAY;MAAA7C,QAAA,gBACnCnD,OAAA,CAACrB,YAAY;QACXqG,MAAM,EAAEA,MAAO;QACfC,IAAI,EAAEA,IAAK;QACX5B,KAAK,EAAE;UAAE2C,MAAM,EAAE,MAAM;UAAE0B,KAAK,EAAE;QAAO,CAAE;QACzCC,WAAW,EAAE,KAAM;QACnBC,kBAAkB,EAAE,KAAM;QAC1BrF,SAAS,EAAC,YAAY;QAAAY,QAAA,gBAEtBnD,OAAA,CAACpB,SAAS;UACR+B,GAAG,EAAE6G,eAAe,CAAC7G,GAAI;UACzBC,WAAW,EAAE4G,eAAe,CAAC5G,WAAY;UACzCiH,OAAO,EAAE;QAAG;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEF3D,OAAA,CAACmE,eAAe;UACdC,UAAU,EAAEA,UAAW;UACvBC,UAAU,EAAEiC;QAAe;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEF3D,OAAA,CAAC0E,iBAAiB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGpBuB,YAAY,iBACXlF,OAAA,CAACnB,MAAM;UACLiJ,QAAQ,EAAE,CAAC5C,YAAY,CAACwB,GAAG,EAAExB,YAAY,CAACyB,GAAG,CAAE;UAC/C7F,IAAI,EAAEc,gBAAgB,CAAC,MAAM,EAAER,YAAY,CAACG,IAAI,CAAE;UAAA4B,QAAA,eAElDnD,OAAA,CAAClB,KAAK;YAACyD,SAAS,EAAC,cAAc;YAAAY,QAAA,eAC7BnD,OAAA;cAAKuC,SAAS,EAAC,iBAAiB;cAAAY,QAAA,gBAC9BnD,OAAA;gBAAQuC,SAAS,EAAC,eAAe;gBAAAY,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxD3D,OAAA;gBAAKuC,SAAS,EAAC,4BAA4B;gBAAAY,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACT,EAGAwB,MAAM,CAACrB,GAAG,CAACiE,KAAK,IAAI;UACnB,MAAMC,UAAU,GAAG,CAAA1C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2C,EAAE,MAAKF,KAAK,CAACE,EAAE;UACjD,MAAMC,UAAU,GAAGF,UAAU,GAAG5G,YAAY,CAACC,MAAM,GAAGD,YAAY,CAACG,IAAI;UAEvE,oBACEvB,OAAA,CAACjB,QAAQ;YAEPoJ,SAAS,EAAEJ,KAAK,CAACK,MAAM,CAACtE,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;YACjD7E,KAAK,EAAEoG,UAAW;YAClBI,MAAM,EAAEN,UAAU,GAAG,CAAC,GAAG,CAAE;YAC3BO,OAAO,EAAEP,UAAU,GAAG,CAAC,GAAG,GAAI;YAC9BQ,aAAa,EAAE;cACbjE,KAAK,EAAEA,CAAA,KAAMsB,aAAa,IAAIA,aAAa,CAACkC,KAAK;YACnD,CAAE;YAAA5E,QAAA,eAEFnD,OAAA,CAAClB,KAAK;cAACyD,SAAS,EAAC,cAAc;cAAAY,QAAA,eAC7BnD,OAAA;gBAAKuC,SAAS,EAAC,KAAK;gBAAAY,QAAA,gBAClBnD,OAAA;kBAAIuC,SAAS,EAAC,8BAA8B;kBAACc,KAAK,EAAE;oBAAEvB,KAAK,EAAEV,YAAY,CAACC;kBAAO,CAAE;kBAAA8B,QAAA,EAChF4E,KAAK,CAAClH;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACL3D,OAAA;kBAAGuC,SAAS,EAAC,4BAA4B;kBAAAY,QAAA,EAAE4E,KAAK,CAACU;gBAAW;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjE3D,OAAA;kBAAKuC,SAAS,EAAC,gCAAgC;kBAAAY,QAAA,gBAC7CnD,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzD3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,GAAE4E,KAAK,CAACW,QAAQ,EAAC,IAAE;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACN3D,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3D3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,EAAE4E,KAAK,CAACY;oBAAU;sBAAAnF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GA1BHoE,KAAK,CAACE,EAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BL,CAAC;QAEf,CAAC,CAAC,EAGD2B,aAAa,iBACZtF,OAAA,CAACjB,QAAQ;UACPoJ,SAAS,EAAE7C,aAAa,CAAC8C,MAAM,CAACtE,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;UACzD7E,KAAK,EAAEV,YAAY,CAACC,MAAO;UAC3BiH,MAAM,EAAE,CAAE;UACVC,OAAO,EAAE;QAAE;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACF,EAGA8B,cAAc,iBACbzF,OAAA,CAACjB,QAAQ;UACPoJ,SAAS,EAAE1C,cAAc,CAAC3B,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;UACnD7E,KAAK,EAAEV,YAAY,CAACI,KAAM;UAC1B8G,MAAM,EAAE,CAAE;UACVC,OAAO,EAAE,GAAI;UACbK,SAAS,EAAC;QAAM;UAAApF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACF,EAGA+B,cAAc,CAAC5B,GAAG,CAAC,CAAC+E,KAAK,EAAEC,KAAK,kBAC/B9I,OAAA,CAACnB,MAAM;UAELiJ,QAAQ,EAAE,CAACe,KAAK,CAACnC,GAAG,EAAEmC,KAAK,CAAClC,GAAG,CAAE;UACjC7F,IAAI,EAAEc,gBAAgB,CAAC,KAAK,EAAER,YAAY,CAACO,MAAM,CAAE;UAAAwB,QAAA,eAEnDnD,OAAA,CAAClB,KAAK;YAACyD,SAAS,EAAC,cAAc;YAAAY,QAAA,eAC7BnD,OAAA;cAAKuC,SAAS,EAAC,iBAAiB;cAAAY,QAAA,gBAC9BnD,OAAA;gBAAQuC,SAAS,EAAC,eAAe;gBAAAY,QAAA,GAAC,QAAM,EAAC2F,KAAK,GAAG,CAAC;cAAA;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC5D3D,OAAA;gBAAKuC,SAAS,EAAC,4BAA4B;gBAAAY,QAAA,EAAC;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GATHmF,KAAK;UAAAtF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUJ,CACT,CAAC,EAGDgC,QAAQ,IAAIN,IAAI,CAACvB,GAAG,CAAC3B,GAAG,iBACvBnC,OAAA,CAACnB,MAAM;UAELiJ,QAAQ,EAAE,CAAC3F,GAAG,CAACuE,GAAG,EAAEvE,GAAG,CAACwE,GAAG,CAAE;UAC7B7F,IAAI,EAAEc,gBAAgB,CAAC,KAAK,EAAER,YAAY,CAACI,KAAK,CAAE;UAClDgH,aAAa,EAAE;YACbjE,KAAK,EAAEA,CAAA,KAAMwB,WAAW,IAAIA,WAAW,CAAC5D,GAAG;UAC7C,CAAE;UAAAgB,QAAA,eAEFnD,OAAA,CAAClB,KAAK;YAACyD,SAAS,EAAC,cAAc;YAAAY,QAAA,eAC7BnD,OAAA;cAAKuC,SAAS,EAAC,KAAK;cAAAY,QAAA,gBAClBnD,OAAA;gBAAIuC,SAAS,EAAC,8BAA8B;gBAACc,KAAK,EAAE;kBAAEvB,KAAK,EAAEV,YAAY,CAACI;gBAAM,CAAE;gBAAA2B,QAAA,EAC/EhB,GAAG,CAACtB;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACL3D,OAAA;gBAAGuC,SAAS,EAAC,4BAA4B;gBAAAY,QAAA,EAAEhB,GAAG,CAACN;cAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACvDxB,GAAG,CAAC4G,MAAM,iBACT/I,OAAA;gBAAKuC,SAAS,EAAC,4CAA4C;gBAAAY,QAAA,gBACzDnD,OAAA;kBAAMuC,SAAS,EAAC,iBAAiB;kBAAAY,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1C3D,OAAA;kBAAMuC,SAAS,EAAC,0BAA0B;kBAAAY,QAAA,EAAEhB,GAAG,CAAC4G,MAAM,CAACC,OAAO,CAAC,CAAC;gBAAC;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GApBHxB,GAAG,CAAC8F,EAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBL,CACT,CAAC,EAGD6B,YAAY,CAAC1B,GAAG,CAAC3B,GAAG,iBACnBnC,OAAA,CAACnB,MAAM;UAELiJ,QAAQ,EAAE,CAAC3F,GAAG,CAACuE,GAAG,EAAEvE,GAAG,CAACwE,GAAG,CAAE;UAC7B7F,IAAI,EAAEc,gBAAgB,CAAC,KAAK,EAAER,YAAY,CAACK,GAAG;QAAE,GAF3C,YAAYU,GAAG,CAAC8F,EAAE,EAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAG1B,CACF,CAAC,EAGD,CAACiC,YAAY,IAAIL,eAAe,KAAKH,QAAQ,CAACtB,GAAG,CAAC5B,OAAO,IAAI;UAC5D,MAAM8F,UAAU,GAAG,CAAAzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0C,EAAE,MAAK/F,OAAO,CAAC+F,EAAE;UACrD,MAAMgB,YAAY,GAAGjB,UAAU,GAAG5G,YAAY,CAACC,MAAM,GAClCa,OAAO,CAACL,IAAI,KAAK,OAAO,GAAGT,YAAY,CAACK,GAAG,GAC3CS,OAAO,CAACL,IAAI,KAAK,QAAQ,GAAGT,YAAY,CAACI,KAAK,GAC9CU,OAAO,CAACL,IAAI,KAAK,SAAS,GAAGT,YAAY,CAACG,IAAI,GAC9CH,YAAY,CAACM,MAAM;UAEtC,oBACE1B,OAAA,CAACjB,QAAQ;YAEPoJ,SAAS,EAAEjG,OAAO,CAACkG,MAAM,CAACtE,GAAG,CAAC+E,KAAK,IAAI,CAACA,KAAK,CAACnC,GAAG,EAAEmC,KAAK,CAAClC,GAAG,CAAC,CAAE;YAC/D7E,KAAK,EAAEmH,YAAa;YACpBX,MAAM,EAAEN,UAAU,GAAG,CAAC,GAAG,CAAE;YAC3BO,OAAO,EAAEP,UAAU,GAAG,CAAC,GAAG,GAAI;YAC9BQ,aAAa,EAAE;cACbjE,KAAK,EAAEA,CAAA,KAAMuB,eAAe,IAAIA,eAAe,CAAC5D,OAAO;YACzD,CAAE;YAAAiB,QAAA,eAEFnD,OAAA,CAAClB,KAAK;cAACyD,SAAS,EAAC,cAAc;cAAAY,QAAA,eAC7BnD,OAAA;gBAAKuC,SAAS,EAAC,KAAK;gBAAAY,QAAA,gBAClBnD,OAAA;kBAAIuC,SAAS,EAAC,8BAA8B;kBAACc,KAAK,EAAE;oBAAEvB,KAAK,EAAEmH;kBAAa,CAAE;kBAAA9F,QAAA,EACzEjB,OAAO,CAACrB;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACL3D,OAAA;kBAAGuC,SAAS,EAAC,4BAA4B;kBAAAY,QAAA,EAAEjB,OAAO,CAACuG;gBAAW;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE3D,OAAA;kBAAKuC,SAAS,EAAC,gCAAgC;kBAAAY,QAAA,gBAC7CnD,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzD3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,EAC3BjB,OAAO,CAACwG,QAAQ,GAAG,IAAI,GAAG,GAAGxG,OAAO,CAACwG,QAAQ,GAAG,GAAG,GAAG,CAACxG,OAAO,CAACwG,QAAQ,GAAG,IAAI,EAAEM,OAAO,CAAC,CAAC,CAAC;oBAAI;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3D,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1D3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,EAC3BjB,OAAO,CAACgH,SAAS,GAAG,CAAC,GAAG,IAAIhH,OAAO,CAACgH,SAAS,GAAG,GAAG,GAAGhH,OAAO,CAACgH,SAAS;oBAAG;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3D,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvD3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,EAAEjB,OAAO,CAACiH;oBAAU;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN3D,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAY,QAAA,gBACrCnD,OAAA;sBAAKuC,SAAS,EAAC,2BAA2B;sBAAAY,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzD3D,OAAA;sBAAKuC,SAAS,EAAC,eAAe;sBAAAY,QAAA,EAAEjB,OAAO,CAACkH;oBAAQ;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAtCHzB,OAAO,CAAC+F,EAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCP,CAAC;QAEf,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eAGf3D,OAAA,CAAC0C,iBAAiB;QAChBC,aAAa,EAAEwD,WAAY;QAC3BvD,YAAY,EAAEsD,QAAS;QACvBrD,QAAQ,EAAE2D,YAAa;QACvB1D,YAAY,EAAEgE,gBAAiB;QAC/B/D,YAAY,EAAEA;MAAa;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsC,GAAA,CA5QIlB,SAAS;AAAAsE,GAAA,GAATtE,SAAS;AA8Qf,eAAeA,SAAS;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAuE,GAAA;AAAAC,YAAA,CAAApF,EAAA;AAAAoF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}