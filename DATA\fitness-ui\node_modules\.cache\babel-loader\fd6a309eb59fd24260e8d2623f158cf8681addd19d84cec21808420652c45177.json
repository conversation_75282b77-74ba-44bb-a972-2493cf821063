{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\ModernMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { MapContainer, <PERSON>ile<PERSON><PERSON>er, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiLayers, FiMaximize2, FiMinimize2, FiNavigation, FiZoomIn, FiZoomOut, FiMap, FiGlobe, FiMoon, FiTriangle } from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiGlobe\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiTriangle\n  }\n};\n\n// Modern Strava-style colors with gradients\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  darkBlue: '#005BB5',\n  green: '#00D924',\n  darkGreen: '#00A01C',\n  red: '#FF0000',\n  darkRed: '#CC0000',\n  purple: '#8B5CF6',\n  darkPurple: '#7C3AED',\n  yellow: '#FFC107',\n  darkYellow: '#F59E0B',\n  // Activity type colors\n  running: '#FC4C02',\n  cycling: '#0073E6',\n  hiking: '#00D924',\n  swimming: '#06B6D4',\n  // Segment type colors\n  climb: '#EF4444',\n  sprint: '#10B981',\n  descent: '#3B82F6',\n  flat: '#8B5CF6'\n};\n\n// Route difficulty styling\nconst routeDifficultyStyles = {\n  easy: {\n    weight: 4,\n    opacity: 0.8,\n    color: stravaColors.green\n  },\n  moderate: {\n    weight: 5,\n    opacity: 0.85,\n    color: stravaColors.yellow\n  },\n  hard: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.orange\n  },\n  extreme: {\n    weight: 7,\n    opacity: 0.95,\n    color: stravaColors.red\n  }\n};\n\n// Activity type styling\nconst activityTypeStyles = {\n  running: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.running,\n    dashArray: null\n  },\n  cycling: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.cycling,\n    dashArray: null\n  },\n  hiking: {\n    weight: 4,\n    opacity: 0.85,\n    color: stravaColors.hiking,\n    dashArray: '8, 4'\n  },\n  swimming: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.swimming,\n    dashArray: '12, 8'\n  }\n};\n\n// Modern POI icons with detailed styling\nconst createModernPOIIcon = (poiType, isSelected = false) => {\n  const iconMap = {\n    restaurant: '🍽️',\n    cafe: '☕',\n    hotel: '🏨',\n    gas_station: '⛽',\n    hospital: '🏥',\n    pharmacy: '💊',\n    bank: '🏦',\n    atm: '💳',\n    parking: '🅿️',\n    toilet: '🚻',\n    water: '💧',\n    viewpoint: '👁️',\n    monument: '🏛️',\n    park: '🌳',\n    beach: '🏖️',\n    mountain: '⛰️',\n    default: '📍'\n  };\n  const size = isSelected ? 44 : 36;\n  const shadowSize = isSelected ? 16 : 12;\n  return L.divIcon({\n    html: `\n      <div class=\"modern-poi-marker ${isSelected ? 'selected' : ''}\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.blue} 0%, ${stravaColors.darkBlue} 100%);\n        color: white;\n        border-radius: 50%;\n        width: ${size}px;\n        height: ${size}px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: ${size * 0.4}px;\n        border: 3px solid white;\n        box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0,0,0,0.2),\n                    0 ${shadowSize / 2}px ${shadowSize}px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        cursor: pointer;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">\n          ${iconMap[poiType] || iconMap.default}\n        </span>\n      </div>\n    `,\n    className: 'modern-poi-icon',\n    iconSize: [size, size],\n    iconAnchor: [size / 2, size / 2]\n  });\n};\n\n// Modern segment markers (start/finish)\nconst createSegmentMarker = (type, segmentType = 'general') => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    general: stravaColors.purple\n  };\n  const icons = {\n    start: '🚀',\n    finish: '🎯'\n  };\n  return L.divIcon({\n    html: `\n      <div class=\"segment-marker ${type}\" style=\"\n        background: linear-gradient(135deg, ${colors[segmentType]} 0%, ${colors[segmentType]}dd 100%);\n        color: white;\n        border-radius: 8px;\n        width: 32px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 6px 20px rgba(0,0,0,0.15),\n                    0 2px 6px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.2);\n        font-weight: bold;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));\">\n          ${icons[type]}\n        </span>\n      </div>\n    `,\n    className: 'segment-marker-icon',\n    iconSize: [32, 32],\n    iconAnchor: [16, 16]\n  });\n};\n\n// User location icon\nconst createUserLocationIcon = () => {\n  return L.divIcon({\n    html: `\n      <div class=\"user-location-marker\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.orange} 0%, ${stravaColors.darkOrange} 100%);\n        color: white;\n        border-radius: 50%;\n        width: 40px;\n        height: 40px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 16px;\n        border: 4px solid white;\n        box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),\n                    0 3px 10px rgba(0,0,0,0.2),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: bold;\n        transform: translateZ(0);\n        animation: pulse 2s infinite;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">📍</span>\n      </div>\n    `,\n    className: 'user-location-icon',\n    iconSize: [40, 40],\n    iconAnchor: [20, 20]\n  });\n};\n\n// Modern route styling functions\nconst getRouteStyle = (route, isSelected = false, isHovered = false) => {\n  const baseStyle = activityTypeStyles[route.activityType] || activityTypeStyles.running;\n  const difficultyStyle = routeDifficultyStyles[route.difficulty] || routeDifficultyStyles.moderate;\n  return {\n    color: isSelected ? stravaColors.orange : baseStyle.color,\n    weight: isSelected ? difficultyStyle.weight + 2 : difficultyStyle.weight,\n    opacity: isSelected ? 1 : isHovered ? 0.9 : baseStyle.opacity,\n    dashArray: baseStyle.dashArray,\n    lineCap: 'round',\n    lineJoin: 'round'\n  };\n};\nconst getSegmentStyle = (segment, isSelected = false) => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    flat: stravaColors.purple\n  };\n  const color = colors[segment.type] || colors.flat;\n  return {\n    color: isSelected ? stravaColors.orange : color,\n    weight: isSelected ? 8 : 6,\n    opacity: isSelected ? 1 : 0.85,\n    dashArray: segment.type === 'sprint' ? '10, 5' : null,\n    lineCap: 'round',\n    lineJoin: 'round'\n  };\n};\n\n// Modern popup content generator\nconst createModernPopup = (title, content, type = 'default') => {\n  const typeColors = {\n    route: stravaColors.blue,\n    segment: stravaColors.red,\n    poi: stravaColors.green,\n    default: stravaColors.orange\n  };\n  return `\n    <div class=\"modern-popup\" style=\"\n      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n      min-width: 250px;\n      max-width: 350px;\n    \">\n      <div style=\"\n        background: linear-gradient(135deg, ${typeColors[type]} 0%, ${typeColors[type]}dd 100%);\n        color: white;\n        padding: 12px 16px;\n        margin: -12px -16px 12px -16px;\n        border-radius: 8px 8px 0 0;\n        font-weight: 600;\n        font-size: 16px;\n        text-shadow: 0 1px 2px rgba(0,0,0,0.2);\n      \">\n        ${title}\n      </div>\n      <div style=\"\n        color: #374151;\n        line-height: 1.5;\n      \">\n        ${content}\n      </div>\n    </div>\n  `;\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({\n  onStyleChange,\n  currentStyle,\n  onLocate,\n  onFullscreen,\n  isFullscreen\n}) => {\n  _s();\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute top-4 right-4 z-[1000] space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowStyleSelector(!showStyleSelector),\n        className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\",\n        style: {\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n        },\n        title: \"Change map style\",\n        children: /*#__PURE__*/_jsxDEV(FiLayers, {\n          className: \"h-5 w-5 text-gray-700\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), showStyleSelector && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\",\n        style: {\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: Object.entries(mapStyles).map(([key, style]) => {\n          const IconComponent = style.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              onStyleChange(key);\n              setShowStyleSelector(false);\n            },\n            className: `w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'}`,\n            style: currentStyle === key ? {\n              backgroundColor: '#FFF7ED',\n              color: stravaColors.orange\n            } : {},\n            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: style.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onLocate,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: \"Center on your location\",\n      children: /*#__PURE__*/_jsxDEV(FiNavigation, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onFullscreen,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\",\n      children: isFullscreen ? /*#__PURE__*/_jsxDEV(FiMinimize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(FiMaximize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 328,\n    columnNumber: 5\n  }, this);\n};\n\n// Map event handler\n_s(StravaMapControls, \"PY0GN+P+8EDZXt82pN67/JEoRp8=\");\n_c = StravaMapControls;\nconst MapEventHandler = ({\n  onMapClick,\n  onMapReady\n}) => {\n  _s2();\n  const map = useMap();\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    },\n    zoomend: () => {\n      if (onMapReady) {\n        // Notify parent component of zoom change\n        const currentZoom = map.getZoom();\n        map.fire('zoomchange', {\n          zoom: currentZoom\n        });\n      }\n    }\n  });\n  return null;\n};\n\n// Strava-style zoom controls\n_s2(MapEventHandler, \"tmcOhplWkk/SgX5HNxHxB5dt97g=\", false, function () {\n  return [useMap, useMapEvents];\n});\n_c2 = MapEventHandler;\nconst StravaZoomControl = () => {\n  _s3();\n  const map = useMap();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\",\n    style: {\n      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomIn(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\",\n      title: \"Zoom in\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomIn, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomOut(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150\",\n      title: \"Zoom out\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomOut, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 419,\n    columnNumber: 5\n  }, this);\n};\n_s3(StravaZoomControl, \"cX187cvZ2hODbkaiLn05gMk1sCM=\", false, function () {\n  return [useMap];\n});\n_c3 = StravaZoomControl;\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  _s4();\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const [currentZoom, setCurrentZoom] = useState(zoom);\n  const [visibleLayers, setVisibleLayers] = useState({\n    routes: true,\n    segments: false,\n    pois: true,\n    planning: true\n  });\n  const mapContainerRef = useRef(null);\n\n  // Smart layer visibility based on zoom level\n  const getLayerVisibility = zoomLevel => {\n    return {\n      routes: zoomLevel >= 10,\n      // Show routes at medium zoom\n      segments: zoomLevel >= 14,\n      // Show segments only at high zoom\n      pois: zoomLevel >= 12,\n      // Show POIs at medium-high zoom\n      planning: true,\n      // Always show planning points\n      routeLabels: zoomLevel >= 15,\n      // Show route labels only at very high zoom\n      detailedMarkers: zoomLevel >= 13 // Show detailed markers at high zoom\n    };\n  };\n\n  // Update layer visibility when zoom changes\n  const handleZoomChange = newZoom => {\n    setCurrentZoom(newZoom);\n    const newVisibility = getLayerVisibility(newZoom);\n    setVisibleLayers(prev => ({\n      ...prev,\n      ...newVisibility\n    }));\n  };\n\n  // Filter routes to reduce clutter\n  const getVisibleRoutes = () => {\n    if (!visibleLayers.routes) return [];\n\n    // If a route is selected, show it prominently and reduce others\n    if (selectedRoute) {\n      return routes.filter(route => route.id === selectedRoute.id || currentZoom >= 13 && route.difficulty !== 'easy' // Show only non-easy routes when zoomed in\n      );\n    }\n\n    // At lower zoom levels, show fewer routes to reduce clutter\n    if (currentZoom < 12) {\n      return routes.filter(route => route.difficulty === 'hard' || route.difficulty === 'extreme');\n    }\n    return routes;\n  };\n\n  // Filter segments based on zoom and selection\n  const getVisibleSegments = () => {\n    if (!visibleLayers.segments || !showSegments) return [];\n\n    // Always show selected segment\n    if (selectedSegment) {\n      return segments.filter(segment => segment.id === selectedSegment.id);\n    }\n\n    // At high zoom, show all segments\n    if (currentZoom >= 15) return segments;\n\n    // At medium zoom, show only important segments\n    return segments.filter(segment => segment.type === 'climb' || segment.type === 'sprint');\n  };\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        var _mapContainerRef$curr, _mapContainerRef$curr2;\n        (_mapContainerRef$curr = (_mapContainerRef$curr2 = mapContainerRef.current).requestFullscreen) === null || _mapContainerRef$curr === void 0 ? void 0 : _mapContainerRef$curr.call(_mapContainerRef$curr2);\n      } else {\n        var _document$exitFullscr, _document;\n        (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n  const currentMapStyle = mapStyles[mapStyle];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: mapContainerRef,\n    className: `relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`,\n    style: {\n      boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${height} relative`,\n      children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n        center: center,\n        zoom: zoom,\n        style: {\n          height: '100%',\n          width: '100%'\n        },\n        zoomControl: false,\n        attributionControl: false,\n        className: \"rounded-xl\",\n        children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n          url: currentMapStyle.url,\n          attribution: currentMapStyle.attribution,\n          maxZoom: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MapEventHandler, {\n          onMapClick: onMapClick,\n          onMapReady: setMapInstance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StravaZoomControl, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), userPosition && /*#__PURE__*/_jsxDEV(Marker, {\n          position: [userPosition.lat, userPosition.lng],\n          icon: createUserLocationIcon(),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              dangerouslySetInnerHTML: {\n                __html: createModernPopup(\"Your Location\", `\n                      <div class=\"text-center\">\n                        <div class=\"text-sm text-gray-600 mb-2\">Current position</div>\n                        <div class=\"text-xs text-gray-500\">\n                          Lat: ${userPosition.lat.toFixed(6)}<br>\n                          Lng: ${userPosition.lng.toFixed(6)}\n                        </div>\n                      </div>\n                    `, 'default')\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this), routes.map(route => {\n          const isSelected = (selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id;\n          const routeStyle = getRouteStyle(route, isSelected);\n          return /*#__PURE__*/_jsxDEV(Polyline, {\n            positions: route.points.map(p => [p.lat, p.lng]),\n            ...routeStyle,\n            eventHandlers: {\n              click: () => onRouteSelect && onRouteSelect(route),\n              mouseover: e => {\n                if (!isSelected) {\n                  e.target.setStyle({\n                    opacity: 0.9,\n                    weight: routeStyle.weight + 1\n                  });\n                }\n              },\n              mouseout: e => {\n                if (!isSelected) {\n                  e.target.setStyle({\n                    opacity: routeStyle.opacity,\n                    weight: routeStyle.weight\n                  });\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                dangerouslySetInnerHTML: {\n                  __html: createModernPopup(route.name, `\n                        <div class=\"space-y-3\">\n                          <p class=\"text-sm text-gray-600\">${route.description || 'No description available'}</p>\n\n                          <div class=\"grid grid-cols-2 gap-3\">\n                            <div class=\"bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Distance</div>\n                              <div class=\"text-lg font-bold text-blue-900\">${route.distance}km</div>\n                            </div>\n                            <div class=\"bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-orange-700 uppercase tracking-wide\">Difficulty</div>\n                              <div class=\"text-lg font-bold text-orange-900 capitalize\">${route.difficulty || 'moderate'}</div>\n                            </div>\n                          </div>\n\n                          <div class=\"grid grid-cols-2 gap-3\">\n                            <div class=\"bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-green-700 uppercase tracking-wide\">Activity</div>\n                              <div class=\"text-sm font-semibold text-green-900 capitalize\">${route.activityType || 'running'}</div>\n                            </div>\n                            <div class=\"bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-purple-700 uppercase tracking-wide\">Elevation</div>\n                              <div class=\"text-sm font-semibold text-purple-900\">${route.elevation || 0}m</div>\n                            </div>\n                          </div>\n                        </div>\n                      `, 'route')\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this)\n          }, route.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this);\n        }), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n          positions: optimizedRoute.map(p => [p.lat, p.lng]),\n          color: stravaColors.green,\n          weight: 6,\n          opacity: 0.95,\n          dashArray: \"12, 8\",\n          lineCap: \"round\",\n          lineJoin: \"round\",\n          className: \"animated-route\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [point.lat, point.lng],\n          icon: createModernPOIIcon('default', false),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              dangerouslySetInnerHTML: {\n                __html: createModernPopup(`Waypoint ${index + 1}`, `\n                      <div class=\"text-center\">\n                        <div class=\"text-sm text-gray-600 mb-2\">Planning waypoint</div>\n                        <div class=\"text-xs text-gray-500\">\n                          Lat: ${point.lat.toFixed(6)}<br>\n                          Lng: ${point.lng.toFixed(6)}\n                        </div>\n                      </div>\n                    `, 'poi')\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 13\n        }, this)), showPOIs && pois.map(poi => {\n          const isSelected = selectedPOIs.some(selected => selected.id === poi.id);\n          return /*#__PURE__*/_jsxDEV(Marker, {\n            position: [poi.lat, poi.lng],\n            icon: createModernPOIIcon(poi.type || 'default', isSelected),\n            eventHandlers: {\n              click: () => onPOISelect && onPOISelect(poi)\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                dangerouslySetInnerHTML: {\n                  __html: createModernPopup(poi.name, `\n                        <div class=\"space-y-3\">\n                          <div class=\"flex items-center space-x-2\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                              ${poi.type || 'POI'}\n                            </span>\n                          </div>\n\n                          ${poi.description ? `<p class=\"text-sm text-gray-600\">${poi.description}</p>` : ''}\n\n                          ${poi.rating ? `\n                            <div class=\"bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg\">\n                              <div class=\"flex items-center justify-center space-x-2\">\n                                <span class=\"text-yellow-500 text-lg\">⭐</span>\n                                <span class=\"text-lg font-bold text-yellow-700\">${poi.rating.toFixed(1)}</span>\n                                <span class=\"text-sm text-gray-500\">/ 5.0</span>\n                              </div>\n                            </div>\n                          ` : ''}\n\n                          ${poi.amenities ? `\n                            <div class=\"space-y-1\">\n                              <div class=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">Amenities</div>\n                              <div class=\"flex flex-wrap gap-1\">\n                                ${poi.amenities.map(amenity => `\n                                  <span class=\"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700\">\n                                    ${amenity}\n                                  </span>\n                                `).join('')}\n                              </div>\n                            </div>\n                          ` : ''}\n                        </div>\n                      `, 'poi')\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this)\n          }, poi.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 15\n          }, this);\n        }), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [poi.lat, poi.lng],\n          icon: createModernPOIIcon(poi.type || 'default', true)\n        }, `selected-${poi.id}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this)), (showSegments || selectedSegment) && segments.map(segment => {\n          const isSelected = (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id;\n          const segmentStyle = getSegmentStyle(segment, isSelected);\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [segment.points && segment.points.length > 0 && /*#__PURE__*/_jsxDEV(Marker, {\n              position: [segment.points[0].lat, segment.points[0].lng],\n              icon: createSegmentMarker('start', segment.type),\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                className: \"strava-popup\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  dangerouslySetInnerHTML: {\n                    __html: createModernPopup(`${segment.name} - Start`, `\n                            <div class=\"text-center\">\n                              <div class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-2\">\n                                🚀 Segment Start\n                              </div>\n                              <div class=\"text-sm text-gray-600\">\n                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}\n                              </div>\n                            </div>\n                          `, 'segment')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Polyline, {\n              positions: segment.points.map(point => [point.lat, point.lng]),\n              ...segmentStyle,\n              eventHandlers: {\n                click: () => onSegmentSelect && onSegmentSelect(segment),\n                mouseover: e => {\n                  if (!isSelected) {\n                    e.target.setStyle({\n                      opacity: 1,\n                      weight: segmentStyle.weight + 1\n                    });\n                  }\n                },\n                mouseout: e => {\n                  if (!isSelected) {\n                    e.target.setStyle({\n                      opacity: segmentStyle.opacity,\n                      weight: segmentStyle.weight\n                    });\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                className: \"strava-popup\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  dangerouslySetInnerHTML: {\n                    __html: createModernPopup(segment.name, `\n                          <div class=\"space-y-4\">\n                            <div class=\"flex items-center justify-center\">\n                              <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${segment.type === 'climb' ? 'bg-red-100 text-red-800' : segment.type === 'sprint' ? 'bg-green-100 text-green-800' : segment.type === 'descent' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}\">\n                                ${segment.type === 'climb' ? '⛰️' : segment.type === 'sprint' ? '💨' : segment.type === 'descent' ? '⬇️' : '📏'}\n                                ${segment.type ? segment.type.charAt(0).toUpperCase() + segment.type.slice(1) : 'Segment'}\n                              </span>\n                            </div>\n\n                            <p class=\"text-sm text-gray-600 text-center\">${segment.description || 'No description available'}</p>\n\n                            <div class=\"grid grid-cols-2 gap-3\">\n                              <div class=\"bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Distance</div>\n                                <div class=\"text-lg font-bold text-blue-900\">\n                                  ${segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                                </div>\n                              </div>\n                              <div class=\"bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-orange-700 uppercase tracking-wide\">Elevation</div>\n                                <div class=\"text-lg font-bold text-orange-900\">\n                                  ${segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation || 0}m`}\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"grid grid-cols-2 gap-3\">\n                              <div class=\"bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-green-700 uppercase tracking-wide\">Record</div>\n                                <div class=\"text-sm font-bold text-green-900\">${segment.recordTime || 'No record'}</div>\n                              </div>\n                              <div class=\"bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-purple-700 uppercase tracking-wide\">Attempts</div>\n                                <div class=\"text-sm font-bold text-purple-900\">${segment.attempts || 0}</div>\n                              </div>\n                            </div>\n                          </div>\n                        `, 'segment')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), segment.points && segment.points.length > 1 && /*#__PURE__*/_jsxDEV(Marker, {\n              position: [segment.points[segment.points.length - 1].lat, segment.points[segment.points.length - 1].lng],\n              icon: createSegmentMarker('finish', segment.type),\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                className: \"strava-popup\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  dangerouslySetInnerHTML: {\n                    __html: createModernPopup(`${segment.name} - Finish`, `\n                            <div class=\"text-center\">\n                              <div class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 mb-2\">\n                                🎯 Segment Finish\n                              </div>\n                              <div class=\"text-sm text-gray-600\">\n                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}\n                              </div>\n                            </div>\n                          `, 'segment')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 19\n            }, this)]\n          }, segment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StravaMapControls, {\n        onStyleChange: setMapStyle,\n        currentStyle: mapStyle,\n        onLocate: handleLocate,\n        onFullscreen: handleFullscreen,\n        isFullscreen: isFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 555,\n    columnNumber: 5\n  }, this);\n};\n_s4(ModernMap, \"CdXhrUPWlAB9qTgIKx8dBTkSqnQ=\");\n_c4 = ModernMap;\nexport default ModernMap;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StravaMapControls\");\n$RefreshReg$(_c2, \"MapEventHandler\");\n$RefreshReg$(_c3, \"StravaZoomControl\");\n$RefreshReg$(_c4, \"ModernMap\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "useMap", "L", "FiLayers", "FiMaximize2", "FiMinimize2", "FiNavigation", "FiZoomIn", "FiZoomOut", "FiMap", "FiGlobe", "FiMoon", "FiTriangle", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "mapStyles", "strava", "url", "attribution", "name", "icon", "satellite", "dark", "terrain", "stravaColors", "orange", "darkOrange", "blue", "darkBlue", "green", "<PERSON><PERSON><PERSON>", "red", "darkRed", "purple", "dark<PERSON><PERSON>ple", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "running", "cycling", "hiking", "swimming", "climb", "sprint", "descent", "flat", "routeDifficultyStyles", "easy", "weight", "opacity", "color", "moderate", "hard", "extreme", "activityTypeStyles", "dashArray", "createModernPOIIcon", "poiType", "isSelected", "iconMap", "restaurant", "cafe", "hotel", "gas_station", "hospital", "pharmacy", "bank", "atm", "parking", "toilet", "water", "viewpoint", "monument", "park", "beach", "mountain", "default", "size", "shadowSize", "divIcon", "html", "className", "iconSize", "iconAnchor", "createSegmentMarker", "type", "segmentType", "colors", "general", "icons", "start", "finish", "createUserLocationIcon", "getRouteStyle", "route", "isHovered", "baseStyle", "activityType", "difficultyStyle", "difficulty", "lineCap", "lineJoin", "getSegmentStyle", "segment", "createModernPopup", "title", "content", "typeColors", "poi", "StravaMapControls", "onStyleChange", "currentStyle", "onLocate", "onFullscreen", "isFullscreen", "_s", "showStyleSelector", "setShowStyleSelector", "children", "onClick", "style", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "map", "key", "IconComponent", "backgroundColor", "_c", "MapEventHandler", "onMapClick", "onMapReady", "_s2", "click", "ready", "zoomend", "currentZoom", "getZoom", "fire", "zoom", "_c2", "StravaZoomControl", "_s3", "zoomIn", "zoomOut", "_c3", "ModernMap", "center", "userPosition", "routes", "segments", "pois", "<PERSON><PERSON><PERSON><PERSON>", "selectedSegment", "selectedPOIs", "optimizedRoute", "planningPoints", "showPOIs", "showSegments", "onRouteSelect", "onSegmentSelect", "onPOISelect", "height", "_s4", "mapStyle", "setMapStyle", "setIsFullscreen", "mapInstance", "setMapInstance", "setCurrentZoom", "visibleLayers", "setVisibleLayers", "planning", "mapContainerRef", "getLayerVisibility", "zoomLevel", "routeLabels", "detailedMarkers", "handleZoomChange", "newZoom", "newVisibility", "prev", "getVisibleRoutes", "filter", "id", "getVisibleSegments", "handleLocate", "<PERSON><PERSON><PERSON><PERSON>", "lat", "lng", "animate", "duration", "handleFullscreen", "current", "_mapContainerRef$curr", "_mapContainerRef$curr2", "requestFullscreen", "call", "_document$exitFullscr", "_document", "document", "exitFullscreen", "currentMapStyle", "ref", "width", "zoomControl", "attributionControl", "max<PERSON><PERSON>", "position", "dangerouslySetInnerHTML", "__html", "toFixed", "routeStyle", "positions", "points", "p", "eventHandlers", "mouseover", "e", "target", "setStyle", "mouseout", "description", "distance", "elevation", "point", "index", "some", "selected", "rating", "amenities", "amenity", "join", "segmentStyle", "Fragment", "length", "char<PERSON>t", "toUpperCase", "slice", "recordTime", "attempts", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/ModernMap.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiLayers,\n  FiMaximize2,\n  FiMinimize2,\n  FiNavigation,\n  FiZoomIn,\n  FiZoomOut,\n  FiMap,\n  FiGlobe,\n  FiMoon,\n  FiTriangle\n} from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiGlobe\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiTriangle\n  }\n};\n\n// Modern Strava-style colors with gradients\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  darkBlue: '#005BB5',\n  green: '#00D924',\n  darkGreen: '#00A01C',\n  red: '#FF0000',\n  darkRed: '#CC0000',\n  purple: '#8B5CF6',\n  darkPurple: '#7C3AED',\n  yellow: '#FFC107',\n  darkYellow: '#F59E0B',\n  // Activity type colors\n  running: '#FC4C02',\n  cycling: '#0073E6',\n  hiking: '#00D924',\n  swimming: '#06B6D4',\n  // Segment type colors\n  climb: '#EF4444',\n  sprint: '#10B981',\n  descent: '#3B82F6',\n  flat: '#8B5CF6'\n};\n\n// Route difficulty styling\nconst routeDifficultyStyles = {\n  easy: { weight: 4, opacity: 0.8, color: stravaColors.green },\n  moderate: { weight: 5, opacity: 0.85, color: stravaColors.yellow },\n  hard: { weight: 6, opacity: 0.9, color: stravaColors.orange },\n  extreme: { weight: 7, opacity: 0.95, color: stravaColors.red }\n};\n\n// Activity type styling\nconst activityTypeStyles = {\n  running: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.running,\n    dashArray: null\n  },\n  cycling: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.cycling,\n    dashArray: null\n  },\n  hiking: {\n    weight: 4,\n    opacity: 0.85,\n    color: stravaColors.hiking,\n    dashArray: '8, 4'\n  },\n  swimming: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.swimming,\n    dashArray: '12, 8'\n  }\n};\n\n// Modern POI icons with detailed styling\nconst createModernPOIIcon = (poiType, isSelected = false) => {\n  const iconMap = {\n    restaurant: '🍽️',\n    cafe: '☕',\n    hotel: '🏨',\n    gas_station: '⛽',\n    hospital: '🏥',\n    pharmacy: '💊',\n    bank: '🏦',\n    atm: '💳',\n    parking: '🅿️',\n    toilet: '🚻',\n    water: '💧',\n    viewpoint: '👁️',\n    monument: '🏛️',\n    park: '🌳',\n    beach: '🏖️',\n    mountain: '⛰️',\n    default: '📍'\n  };\n\n  const size = isSelected ? 44 : 36;\n  const shadowSize = isSelected ? 16 : 12;\n\n  return L.divIcon({\n    html: `\n      <div class=\"modern-poi-marker ${isSelected ? 'selected' : ''}\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.blue} 0%, ${stravaColors.darkBlue} 100%);\n        color: white;\n        border-radius: 50%;\n        width: ${size}px;\n        height: ${size}px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: ${size * 0.4}px;\n        border: 3px solid white;\n        box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0,0,0,0.2),\n                    0 ${shadowSize/2}px ${shadowSize}px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        cursor: pointer;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">\n          ${iconMap[poiType] || iconMap.default}\n        </span>\n      </div>\n    `,\n    className: 'modern-poi-icon',\n    iconSize: [size, size],\n    iconAnchor: [size/2, size/2]\n  });\n};\n\n// Modern segment markers (start/finish)\nconst createSegmentMarker = (type, segmentType = 'general') => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    general: stravaColors.purple\n  };\n\n  const icons = {\n    start: '🚀',\n    finish: '🎯'\n  };\n\n  return L.divIcon({\n    html: `\n      <div class=\"segment-marker ${type}\" style=\"\n        background: linear-gradient(135deg, ${colors[segmentType]} 0%, ${colors[segmentType]}dd 100%);\n        color: white;\n        border-radius: 8px;\n        width: 32px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 6px 20px rgba(0,0,0,0.15),\n                    0 2px 6px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.2);\n        font-weight: bold;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));\">\n          ${icons[type]}\n        </span>\n      </div>\n    `,\n    className: 'segment-marker-icon',\n    iconSize: [32, 32],\n    iconAnchor: [16, 16]\n  });\n};\n\n// User location icon\nconst createUserLocationIcon = () => {\n  return L.divIcon({\n    html: `\n      <div class=\"user-location-marker\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.orange} 0%, ${stravaColors.darkOrange} 100%);\n        color: white;\n        border-radius: 50%;\n        width: 40px;\n        height: 40px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 16px;\n        border: 4px solid white;\n        box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),\n                    0 3px 10px rgba(0,0,0,0.2),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: bold;\n        transform: translateZ(0);\n        animation: pulse 2s infinite;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">📍</span>\n      </div>\n    `,\n    className: 'user-location-icon',\n    iconSize: [40, 40],\n    iconAnchor: [20, 20]\n  });\n};\n\n// Modern route styling functions\nconst getRouteStyle = (route, isSelected = false, isHovered = false) => {\n  const baseStyle = activityTypeStyles[route.activityType] || activityTypeStyles.running;\n  const difficultyStyle = routeDifficultyStyles[route.difficulty] || routeDifficultyStyles.moderate;\n\n  return {\n    color: isSelected ? stravaColors.orange : baseStyle.color,\n    weight: isSelected ? difficultyStyle.weight + 2 : difficultyStyle.weight,\n    opacity: isSelected ? 1 : (isHovered ? 0.9 : baseStyle.opacity),\n    dashArray: baseStyle.dashArray,\n    lineCap: 'round',\n    lineJoin: 'round'\n  };\n};\n\nconst getSegmentStyle = (segment, isSelected = false) => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    flat: stravaColors.purple\n  };\n\n  const color = colors[segment.type] || colors.flat;\n\n  return {\n    color: isSelected ? stravaColors.orange : color,\n    weight: isSelected ? 8 : 6,\n    opacity: isSelected ? 1 : 0.85,\n    dashArray: segment.type === 'sprint' ? '10, 5' : null,\n    lineCap: 'round',\n    lineJoin: 'round'\n  };\n};\n\n// Modern popup content generator\nconst createModernPopup = (title, content, type = 'default') => {\n  const typeColors = {\n    route: stravaColors.blue,\n    segment: stravaColors.red,\n    poi: stravaColors.green,\n    default: stravaColors.orange\n  };\n\n  return `\n    <div class=\"modern-popup\" style=\"\n      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n      min-width: 250px;\n      max-width: 350px;\n    \">\n      <div style=\"\n        background: linear-gradient(135deg, ${typeColors[type]} 0%, ${typeColors[type]}dd 100%);\n        color: white;\n        padding: 12px 16px;\n        margin: -12px -16px 12px -16px;\n        border-radius: 8px 8px 0 0;\n        font-weight: 600;\n        font-size: 16px;\n        text-shadow: 0 1px 2px rgba(0,0,0,0.2);\n      \">\n        ${title}\n      </div>\n      <div style=\"\n        color: #374151;\n        line-height: 1.5;\n      \">\n        ${content}\n      </div>\n    </div>\n  `;\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n\n  return (\n    <div className=\"absolute top-4 right-4 z-[1000] space-y-3\">\n      {/* Style Selector with Strava design */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowStyleSelector(!showStyleSelector)}\n          className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\"\n          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n          title=\"Change map style\"\n        >\n          <FiLayers className=\"h-5 w-5 text-gray-700\" />\n        </button>\n\n        {showStyleSelector && (\n          <div className=\"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\"\n               style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}>\n            {Object.entries(mapStyles).map(([key, style]) => {\n              const IconComponent = style.icon;\n              return (\n                <button\n                  key={key}\n                  onClick={() => {\n                    onStyleChange(key);\n                    setShowStyleSelector(false);\n                  }}\n                  className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${\n                    currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'\n                  }`}\n                  style={currentStyle === key ? { backgroundColor: '#FFF7ED', color: stravaColors.orange } : {}}\n                >\n                  <IconComponent className=\"h-4 w-4\" />\n                  <span>{style.name}</span>\n                </button>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Locate button with Strava style */}\n      <button\n        onClick={onLocate}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title=\"Center on your location\"\n      >\n        <FiNavigation className=\"h-5 w-5 text-gray-700\" />\n      </button>\n\n      {/* Fullscreen button with Strava style */}\n      <button\n        onClick={onFullscreen}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title={isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n      >\n        {isFullscreen ? (\n          <FiMinimize2 className=\"h-5 w-5 text-gray-700\" />\n        ) : (\n          <FiMaximize2 className=\"h-5 w-5 text-gray-700\" />\n        )}\n      </button>\n    </div>\n  );\n};\n\n// Map event handler\nconst MapEventHandler = ({ onMapClick, onMapReady }) => {\n  const map = useMap();\n\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    },\n    zoomend: () => {\n      if (onMapReady) {\n        // Notify parent component of zoom change\n        const currentZoom = map.getZoom();\n        map.fire('zoomchange', { zoom: currentZoom });\n      }\n    }\n  });\n\n  return null;\n};\n\n// Strava-style zoom controls\nconst StravaZoomControl = () => {\n  const map = useMap();\n\n  return (\n    <div className=\"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\"\n         style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>\n      <button\n        onClick={() => map.zoomIn()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\"\n        title=\"Zoom in\"\n      >\n        <FiZoomIn className=\"h-5 w-5 text-gray-700\" />\n      </button>\n      <button\n        onClick={() => map.zoomOut()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150\"\n        title=\"Zoom out\"\n      >\n        <FiZoomOut className=\"h-5 w-5 text-gray-700\" />\n      </button>\n    </div>\n  );\n};\n\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const [currentZoom, setCurrentZoom] = useState(zoom);\n  const [visibleLayers, setVisibleLayers] = useState({\n    routes: true,\n    segments: false,\n    pois: true,\n    planning: true\n  });\n  const mapContainerRef = useRef(null);\n\n  // Smart layer visibility based on zoom level\n  const getLayerVisibility = (zoomLevel) => {\n    return {\n      routes: zoomLevel >= 10, // Show routes at medium zoom\n      segments: zoomLevel >= 14, // Show segments only at high zoom\n      pois: zoomLevel >= 12, // Show POIs at medium-high zoom\n      planning: true, // Always show planning points\n      routeLabels: zoomLevel >= 15, // Show route labels only at very high zoom\n      detailedMarkers: zoomLevel >= 13 // Show detailed markers at high zoom\n    };\n  };\n\n  // Update layer visibility when zoom changes\n  const handleZoomChange = (newZoom) => {\n    setCurrentZoom(newZoom);\n    const newVisibility = getLayerVisibility(newZoom);\n    setVisibleLayers(prev => ({\n      ...prev,\n      ...newVisibility\n    }));\n  };\n\n  // Filter routes to reduce clutter\n  const getVisibleRoutes = () => {\n    if (!visibleLayers.routes) return [];\n\n    // If a route is selected, show it prominently and reduce others\n    if (selectedRoute) {\n      return routes.filter(route =>\n        route.id === selectedRoute.id ||\n        (currentZoom >= 13 && route.difficulty !== 'easy') // Show only non-easy routes when zoomed in\n      );\n    }\n\n    // At lower zoom levels, show fewer routes to reduce clutter\n    if (currentZoom < 12) {\n      return routes.filter(route => route.difficulty === 'hard' || route.difficulty === 'extreme');\n    }\n\n    return routes;\n  };\n\n  // Filter segments based on zoom and selection\n  const getVisibleSegments = () => {\n    if (!visibleLayers.segments || !showSegments) return [];\n\n    // Always show selected segment\n    if (selectedSegment) {\n      return segments.filter(segment => segment.id === selectedSegment.id);\n    }\n\n    // At high zoom, show all segments\n    if (currentZoom >= 15) return segments;\n\n    // At medium zoom, show only important segments\n    return segments.filter(segment =>\n      segment.type === 'climb' || segment.type === 'sprint'\n    );\n  };\n\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        mapContainerRef.current.requestFullscreen?.();\n      } else {\n        document.exitFullscreen?.();\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n\n  const currentMapStyle = mapStyles[mapStyle];\n\n  return (\n    <div\n      ref={mapContainerRef}\n      className={`relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`}\n      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}\n    >\n      <div className={`${height} relative`}>\n        <MapContainer\n          center={center}\n          zoom={zoom}\n          style={{ height: '100%', width: '100%' }}\n          zoomControl={false}\n          attributionControl={false}\n          className=\"rounded-xl\"\n        >\n          <TileLayer\n            url={currentMapStyle.url}\n            attribution={currentMapStyle.attribution}\n            maxZoom={18}\n          />\n\n          <MapEventHandler\n            onMapClick={onMapClick}\n            onMapReady={setMapInstance}\n          />\n\n          <StravaZoomControl />\n          \n          {/* Modern user position marker */}\n          {userPosition && (\n            <Marker\n              position={[userPosition.lat, userPosition.lng]}\n              icon={createUserLocationIcon()}\n            >\n              <Popup className=\"strava-popup\">\n                <div dangerouslySetInnerHTML={{\n                  __html: createModernPopup(\n                    \"Your Location\",\n                    `\n                      <div class=\"text-center\">\n                        <div class=\"text-sm text-gray-600 mb-2\">Current position</div>\n                        <div class=\"text-xs text-gray-500\">\n                          Lat: ${userPosition.lat.toFixed(6)}<br>\n                          Lng: ${userPosition.lng.toFixed(6)}\n                        </div>\n                      </div>\n                    `,\n                    'default'\n                  )\n                }} />\n              </Popup>\n            </Marker>\n          )}\n\n          {/* Modern routes with advanced styling */}\n          {routes.map(route => {\n            const isSelected = selectedRoute?.id === route.id;\n            const routeStyle = getRouteStyle(route, isSelected);\n\n            return (\n              <Polyline\n                key={route.id}\n                positions={route.points.map(p => [p.lat, p.lng])}\n                {...routeStyle}\n                eventHandlers={{\n                  click: () => onRouteSelect && onRouteSelect(route),\n                  mouseover: (e) => {\n                    if (!isSelected) {\n                      e.target.setStyle({ opacity: 0.9, weight: routeStyle.weight + 1 });\n                    }\n                  },\n                  mouseout: (e) => {\n                    if (!isSelected) {\n                      e.target.setStyle({ opacity: routeStyle.opacity, weight: routeStyle.weight });\n                    }\n                  }\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div dangerouslySetInnerHTML={{\n                    __html: createModernPopup(\n                      route.name,\n                      `\n                        <div class=\"space-y-3\">\n                          <p class=\"text-sm text-gray-600\">${route.description || 'No description available'}</p>\n\n                          <div class=\"grid grid-cols-2 gap-3\">\n                            <div class=\"bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Distance</div>\n                              <div class=\"text-lg font-bold text-blue-900\">${route.distance}km</div>\n                            </div>\n                            <div class=\"bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-orange-700 uppercase tracking-wide\">Difficulty</div>\n                              <div class=\"text-lg font-bold text-orange-900 capitalize\">${route.difficulty || 'moderate'}</div>\n                            </div>\n                          </div>\n\n                          <div class=\"grid grid-cols-2 gap-3\">\n                            <div class=\"bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-green-700 uppercase tracking-wide\">Activity</div>\n                              <div class=\"text-sm font-semibold text-green-900 capitalize\">${route.activityType || 'running'}</div>\n                            </div>\n                            <div class=\"bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg\">\n                              <div class=\"text-xs font-medium text-purple-700 uppercase tracking-wide\">Elevation</div>\n                              <div class=\"text-sm font-semibold text-purple-900\">${route.elevation || 0}m</div>\n                            </div>\n                          </div>\n                        </div>\n                      `,\n                      'route'\n                    )\n                  }} />\n                </Popup>\n              </Polyline>\n            );\n          })}\n\n          {/* Modern optimized route with animated dash */}\n          {optimizedRoute && (\n            <Polyline\n              positions={optimizedRoute.map(p => [p.lat, p.lng])}\n              color={stravaColors.green}\n              weight={6}\n              opacity={0.95}\n              dashArray=\"12, 8\"\n              lineCap=\"round\"\n              lineJoin=\"round\"\n              className=\"animated-route\"\n            />\n          )}\n\n          {/* Modern planning points */}\n          {planningPoints.map((point, index) => (\n            <Marker\n              key={index}\n              position={[point.lat, point.lng]}\n              icon={createModernPOIIcon('default', false)}\n            >\n              <Popup className=\"strava-popup\">\n                <div dangerouslySetInnerHTML={{\n                  __html: createModernPopup(\n                    `Waypoint ${index + 1}`,\n                    `\n                      <div class=\"text-center\">\n                        <div class=\"text-sm text-gray-600 mb-2\">Planning waypoint</div>\n                        <div class=\"text-xs text-gray-500\">\n                          Lat: ${point.lat.toFixed(6)}<br>\n                          Lng: ${point.lng.toFixed(6)}\n                        </div>\n                      </div>\n                    `,\n                    'poi'\n                  )\n                }} />\n              </Popup>\n            </Marker>\n          ))}\n\n          {/* Modern POIs with enhanced styling */}\n          {showPOIs && pois.map(poi => {\n            const isSelected = selectedPOIs.some(selected => selected.id === poi.id);\n\n            return (\n              <Marker\n                key={poi.id}\n                position={[poi.lat, poi.lng]}\n                icon={createModernPOIIcon(poi.type || 'default', isSelected)}\n                eventHandlers={{\n                  click: () => onPOISelect && onPOISelect(poi)\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div dangerouslySetInnerHTML={{\n                    __html: createModernPopup(\n                      poi.name,\n                      `\n                        <div class=\"space-y-3\">\n                          <div class=\"flex items-center space-x-2\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                              ${poi.type || 'POI'}\n                            </span>\n                          </div>\n\n                          ${poi.description ? `<p class=\"text-sm text-gray-600\">${poi.description}</p>` : ''}\n\n                          ${poi.rating ? `\n                            <div class=\"bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg\">\n                              <div class=\"flex items-center justify-center space-x-2\">\n                                <span class=\"text-yellow-500 text-lg\">⭐</span>\n                                <span class=\"text-lg font-bold text-yellow-700\">${poi.rating.toFixed(1)}</span>\n                                <span class=\"text-sm text-gray-500\">/ 5.0</span>\n                              </div>\n                            </div>\n                          ` : ''}\n\n                          ${poi.amenities ? `\n                            <div class=\"space-y-1\">\n                              <div class=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">Amenities</div>\n                              <div class=\"flex flex-wrap gap-1\">\n                                ${poi.amenities.map(amenity => `\n                                  <span class=\"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700\">\n                                    ${amenity}\n                                  </span>\n                                `).join('')}\n                              </div>\n                            </div>\n                          ` : ''}\n                        </div>\n                      `,\n                      'poi'\n                    )\n                  }} />\n                </Popup>\n              </Marker>\n            );\n          })}\n\n          {/* Selected POIs with enhanced highlight */}\n          {selectedPOIs.map(poi => (\n            <Marker\n              key={`selected-${poi.id}`}\n              position={[poi.lat, poi.lng]}\n              icon={createModernPOIIcon(poi.type || 'default', true)}\n            />\n          ))}\n\n          {/* Modern segments with enhanced styling */}\n          {(showSegments || selectedSegment) && segments.map(segment => {\n            const isSelected = selectedSegment?.id === segment.id;\n            const segmentStyle = getSegmentStyle(segment, isSelected);\n\n            return (\n              <React.Fragment key={segment.id}>\n                {/* Segment start marker */}\n                {segment.points && segment.points.length > 0 && (\n                  <Marker\n                    position={[segment.points[0].lat, segment.points[0].lng]}\n                    icon={createSegmentMarker('start', segment.type)}\n                  >\n                    <Popup className=\"strava-popup\">\n                      <div dangerouslySetInnerHTML={{\n                        __html: createModernPopup(\n                          `${segment.name} - Start`,\n                          `\n                            <div class=\"text-center\">\n                              <div class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-2\">\n                                🚀 Segment Start\n                              </div>\n                              <div class=\"text-sm text-gray-600\">\n                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}\n                              </div>\n                            </div>\n                          `,\n                          'segment'\n                        )\n                      }} />\n                    </Popup>\n                  </Marker>\n                )}\n\n                {/* Segment polyline */}\n                <Polyline\n                  positions={segment.points.map(point => [point.lat, point.lng])}\n                  {...segmentStyle}\n                  eventHandlers={{\n                    click: () => onSegmentSelect && onSegmentSelect(segment),\n                    mouseover: (e) => {\n                      if (!isSelected) {\n                        e.target.setStyle({\n                          opacity: 1,\n                          weight: segmentStyle.weight + 1\n                        });\n                      }\n                    },\n                    mouseout: (e) => {\n                      if (!isSelected) {\n                        e.target.setStyle({\n                          opacity: segmentStyle.opacity,\n                          weight: segmentStyle.weight\n                        });\n                      }\n                    }\n                  }}\n                >\n                  <Popup className=\"strava-popup\">\n                    <div dangerouslySetInnerHTML={{\n                      __html: createModernPopup(\n                        segment.name,\n                        `\n                          <div class=\"space-y-4\">\n                            <div class=\"flex items-center justify-center\">\n                              <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\n                                segment.type === 'climb' ? 'bg-red-100 text-red-800' :\n                                segment.type === 'sprint' ? 'bg-green-100 text-green-800' :\n                                segment.type === 'descent' ? 'bg-blue-100 text-blue-800' :\n                                'bg-purple-100 text-purple-800'\n                              }\">\n                                ${segment.type === 'climb' ? '⛰️' :\n                                  segment.type === 'sprint' ? '💨' :\n                                  segment.type === 'descent' ? '⬇️' : '📏'}\n                                ${segment.type ? segment.type.charAt(0).toUpperCase() + segment.type.slice(1) : 'Segment'}\n                              </span>\n                            </div>\n\n                            <p class=\"text-sm text-gray-600 text-center\">${segment.description || 'No description available'}</p>\n\n                            <div class=\"grid grid-cols-2 gap-3\">\n                              <div class=\"bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Distance</div>\n                                <div class=\"text-lg font-bold text-blue-900\">\n                                  ${segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                                </div>\n                              </div>\n                              <div class=\"bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-orange-700 uppercase tracking-wide\">Elevation</div>\n                                <div class=\"text-lg font-bold text-orange-900\">\n                                  ${segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation || 0}m`}\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"grid grid-cols-2 gap-3\">\n                              <div class=\"bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-green-700 uppercase tracking-wide\">Record</div>\n                                <div class=\"text-sm font-bold text-green-900\">${segment.recordTime || 'No record'}</div>\n                              </div>\n                              <div class=\"bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center\">\n                                <div class=\"text-xs font-medium text-purple-700 uppercase tracking-wide\">Attempts</div>\n                                <div class=\"text-sm font-bold text-purple-900\">${segment.attempts || 0}</div>\n                              </div>\n                            </div>\n                          </div>\n                        `,\n                        'segment'\n                      )\n                    }} />\n                  </Popup>\n                </Polyline>\n\n                {/* Segment finish marker */}\n                {segment.points && segment.points.length > 1 && (\n                  <Marker\n                    position={[segment.points[segment.points.length - 1].lat, segment.points[segment.points.length - 1].lng]}\n                    icon={createSegmentMarker('finish', segment.type)}\n                  >\n                    <Popup className=\"strava-popup\">\n                      <div dangerouslySetInnerHTML={{\n                        __html: createModernPopup(\n                          `${segment.name} - Finish`,\n                          `\n                            <div class=\"text-center\">\n                              <div class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 mb-2\">\n                                🎯 Segment Finish\n                              </div>\n                              <div class=\"text-sm text-gray-600\">\n                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}\n                              </div>\n                            </div>\n                          `,\n                          'segment'\n                        )\n                      }} />\n                    </Popup>\n                  </Marker>\n                )}\n              </React.Fragment>\n            );\n          })}\n        </MapContainer>\n\n        {/* Strava-style controls overlay */}\n        <StravaMapControls\n          onStyleChange={setMapStyle}\n          currentStyle={mapStyle}\n          onLocate={handleLocate}\n          onFullscreen={handleFullscreen}\n          isFullscreen={isFullscreen}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ModernMap;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AACtG,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,gBAAgB;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOZ,CAAC,CAACa,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3ChB,CAAC,CAACa,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE;IACNC,GAAG,EAAE,0EAA0E;IAC/EC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEnB;EACR,CAAC;EACDoB,SAAS,EAAE;IACTJ,GAAG,EAAE,+FAA+F;IACpGC,WAAW,EAAE,iDAAiD;IAC9DC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAElB;EACR,CAAC;EACDoB,IAAI,EAAE;IACJL,GAAG,EAAE,+DAA+D;IACpEC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAEjB;EACR,CAAC;EACDoB,OAAO,EAAE;IACPN,GAAG,EAAE,kDAAkD;IACvDC,WAAW,EAAE,4NAA4N;IACzOC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAEhB;EACR;AACF,CAAC;;AAED;AACA,MAAMoB,YAAY,GAAG;EACnBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE,SAAS;EACdC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrB;EACAC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,SAAS;EACnB;EACAC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,MAAMC,qBAAqB,GAAG;EAC5BC,IAAI,EAAE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,KAAK,EAAEzB,YAAY,CAACK;EAAM,CAAC;EAC5DqB,QAAQ,EAAE;IAAEH,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAEzB,YAAY,CAACW;EAAO,CAAC;EAClEgB,IAAI,EAAE;IAAEJ,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,KAAK,EAAEzB,YAAY,CAACC;EAAO,CAAC;EAC7D2B,OAAO,EAAE;IAAEL,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAEzB,YAAY,CAACO;EAAI;AAC/D,CAAC;;AAED;AACA,MAAMsB,kBAAkB,GAAG;EACzBhB,OAAO,EAAE;IACPU,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACa,OAAO;IAC3BiB,SAAS,EAAE;EACb,CAAC;EACDhB,OAAO,EAAE;IACPS,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACc,OAAO;IAC3BgB,SAAS,EAAE;EACb,CAAC;EACDf,MAAM,EAAE;IACNQ,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAEzB,YAAY,CAACe,MAAM;IAC1Be,SAAS,EAAE;EACb,CAAC;EACDd,QAAQ,EAAE;IACRO,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACgB,QAAQ;IAC5Bc,SAAS,EAAE;EACb;AACF,CAAC;;AAED;AACA,MAAMC,mBAAmB,GAAGA,CAACC,OAAO,EAAEC,UAAU,GAAG,KAAK,KAAK;EAC3D,MAAMC,OAAO,GAAG;IACdC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,GAAG;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,IAAI,GAAGnB,UAAU,GAAG,EAAE,GAAG,EAAE;EACjC,MAAMoB,UAAU,GAAGpB,UAAU,GAAG,EAAE,GAAG,EAAE;EAEvC,OAAO/D,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV,sCAAsCtB,UAAU,GAAG,UAAU,GAAG,EAAE;AAClE,8CAA8CjC,YAAY,CAACG,IAAI,QAAQH,YAAY,CAACI,QAAQ;AAC5F;AACA;AACA,iBAAiBgD,IAAI;AACrB,kBAAkBA,IAAI;AACtB;AACA;AACA;AACA,qBAAqBA,IAAI,GAAG,GAAG;AAC/B;AACA,wBAAwBC,UAAU,MAAMA,UAAU,GAAG,CAAC;AACtD,wBAAwBA,UAAU,GAAC,CAAC,MAAMA,UAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYnB,OAAO,CAACF,OAAO,CAAC,IAAIE,OAAO,CAACiB,OAAO;AAC/C;AACA;AACA,KAAK;IACDK,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAE,CAACL,IAAI,EAAEA,IAAI,CAAC;IACtBM,UAAU,EAAE,CAACN,IAAI,GAAC,CAAC,EAAEA,IAAI,GAAC,CAAC;EAC7B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMO,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,WAAW,GAAG,SAAS,KAAK;EAC7D,MAAMC,MAAM,GAAG;IACb7C,KAAK,EAAEjB,YAAY,CAACO,GAAG;IACvBW,MAAM,EAAElB,YAAY,CAACK,KAAK;IAC1Bc,OAAO,EAAEnB,YAAY,CAACG,IAAI;IAC1B4D,OAAO,EAAE/D,YAAY,CAACS;EACxB,CAAC;EAED,MAAMuD,KAAK,GAAG;IACZC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC;EAED,OAAOhG,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV,mCAAmCK,IAAI;AACvC,8CAA8CE,MAAM,CAACD,WAAW,CAAC,QAAQC,MAAM,CAACD,WAAW,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYG,KAAK,CAACJ,IAAI,CAAC;AACvB;AACA;AACA,KAAK;IACDJ,SAAS,EAAE,qBAAqB;IAChCC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;EACnC,OAAOjG,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV;AACA,8CAA8CvD,YAAY,CAACC,MAAM,QAAQD,YAAY,CAACE,UAAU;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDsD,SAAS,EAAE,oBAAoB;IAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMU,aAAa,GAAGA,CAACC,KAAK,EAAEpC,UAAU,GAAG,KAAK,EAAEqC,SAAS,GAAG,KAAK,KAAK;EACtE,MAAMC,SAAS,GAAG1C,kBAAkB,CAACwC,KAAK,CAACG,YAAY,CAAC,IAAI3C,kBAAkB,CAAChB,OAAO;EACtF,MAAM4D,eAAe,GAAGpD,qBAAqB,CAACgD,KAAK,CAACK,UAAU,CAAC,IAAIrD,qBAAqB,CAACK,QAAQ;EAEjG,OAAO;IACLD,KAAK,EAAEQ,UAAU,GAAGjC,YAAY,CAACC,MAAM,GAAGsE,SAAS,CAAC9C,KAAK;IACzDF,MAAM,EAAEU,UAAU,GAAGwC,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAGkD,eAAe,CAAClD,MAAM;IACxEC,OAAO,EAAES,UAAU,GAAG,CAAC,GAAIqC,SAAS,GAAG,GAAG,GAAGC,SAAS,CAAC/C,OAAQ;IAC/DM,SAAS,EAAEyC,SAAS,CAACzC,SAAS;IAC9B6C,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC;AAED,MAAMC,eAAe,GAAGA,CAACC,OAAO,EAAE7C,UAAU,GAAG,KAAK,KAAK;EACvD,MAAM6B,MAAM,GAAG;IACb7C,KAAK,EAAEjB,YAAY,CAACO,GAAG;IACvBW,MAAM,EAAElB,YAAY,CAACK,KAAK;IAC1Bc,OAAO,EAAEnB,YAAY,CAACG,IAAI;IAC1BiB,IAAI,EAAEpB,YAAY,CAACS;EACrB,CAAC;EAED,MAAMgB,KAAK,GAAGqC,MAAM,CAACgB,OAAO,CAAClB,IAAI,CAAC,IAAIE,MAAM,CAAC1C,IAAI;EAEjD,OAAO;IACLK,KAAK,EAAEQ,UAAU,GAAGjC,YAAY,CAACC,MAAM,GAAGwB,KAAK;IAC/CF,MAAM,EAAEU,UAAU,GAAG,CAAC,GAAG,CAAC;IAC1BT,OAAO,EAAES,UAAU,GAAG,CAAC,GAAG,IAAI;IAC9BH,SAAS,EAAEgD,OAAO,CAAClB,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAG,IAAI;IACrDe,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC;;AAED;AACA,MAAMG,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAErB,IAAI,GAAG,SAAS,KAAK;EAC9D,MAAMsB,UAAU,GAAG;IACjBb,KAAK,EAAErE,YAAY,CAACG,IAAI;IACxB2E,OAAO,EAAE9E,YAAY,CAACO,GAAG;IACzB4E,GAAG,EAAEnF,YAAY,CAACK,KAAK;IACvB8C,OAAO,EAAEnD,YAAY,CAACC;EACxB,CAAC;EAED,OAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8CiF,UAAU,CAACtB,IAAI,CAAC,QAAQsB,UAAU,CAACtB,IAAI,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUoB,KAAK;AACf;AACA;AACA;AACA;AACA;AACA,UAAUC,OAAO;AACjB;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA,MAAMG,iBAAiB,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACnG,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC;EAEjE,oBACEqB,OAAA;IAAK0E,SAAS,EAAC,2CAA2C;IAAAqC,QAAA,gBAExD/G,OAAA;MAAK0E,SAAS,EAAC,UAAU;MAAAqC,QAAA,gBACvB/G,OAAA;QACEgH,OAAO,EAAEA,CAAA,KAAMF,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;QACxDnC,SAAS,EAAC,yGAAyG;QACnHuC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QACpDhB,KAAK,EAAC,kBAAkB;QAAAa,QAAA,eAExB/G,OAAA,CAACX,QAAQ;UAACqF,SAAS,EAAC;QAAuB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAERT,iBAAiB,iBAChB7G,OAAA;QAAK0E,SAAS,EAAC,sGAAsG;QAChHuC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QAAAH,QAAA,EACtDQ,MAAM,CAACC,OAAO,CAAC/G,SAAS,CAAC,CAACgH,GAAG,CAAC,CAAC,CAACC,GAAG,EAAET,KAAK,CAAC,KAAK;UAC/C,MAAMU,aAAa,GAAGV,KAAK,CAACnG,IAAI;UAChC,oBACEd,OAAA;YAEEgH,OAAO,EAAEA,CAAA,KAAM;cACbT,aAAa,CAACmB,GAAG,CAAC;cAClBZ,oBAAoB,CAAC,KAAK,CAAC;YAC7B,CAAE;YACFpC,SAAS,EAAE,+GACT8B,YAAY,KAAKkB,GAAG,GAAG,0CAA0C,GAAG,eAAe,EAClF;YACHT,KAAK,EAAET,YAAY,KAAKkB,GAAG,GAAG;cAAEE,eAAe,EAAE,SAAS;cAAEjF,KAAK,EAAEzB,YAAY,CAACC;YAAO,CAAC,GAAG,CAAC,CAAE;YAAA4F,QAAA,gBAE9F/G,OAAA,CAAC2H,aAAa;cAACjD,SAAS,EAAC;YAAS;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCtH,OAAA;cAAA+G,QAAA,EAAOE,KAAK,CAACpG;YAAI;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAXpBI,GAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYF,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtH,OAAA;MACEgH,OAAO,EAAEP,QAAS;MAClB/B,SAAS,EAAC,+GAA+G;MACzHuC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDhB,KAAK,EAAC,yBAAyB;MAAAa,QAAA,eAE/B/G,OAAA,CAACR,YAAY;QAACkF,SAAS,EAAC;MAAuB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAGTtH,OAAA;MACEgH,OAAO,EAAEN,YAAa;MACtBhC,SAAS,EAAC,+GAA+G;MACzHuC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDhB,KAAK,EAAES,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;MAAAI,QAAA,EAE5DJ,YAAY,gBACX3G,OAAA,CAACT,WAAW;QAACmF,SAAS,EAAC;MAAuB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEjDtH,OAAA,CAACV,WAAW;QAACoF,SAAS,EAAC;MAAuB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAV,EAAA,CArEMN,iBAAiB;AAAAuB,EAAA,GAAjBvB,iBAAiB;AAsEvB,MAAMwB,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAW,CAAC,KAAK;EAAAC,GAAA;EACtD,MAAMR,GAAG,GAAGtI,MAAM,CAAC,CAAC;EAEpBD,YAAY,CAAC;IACXgJ,KAAK,EAAEH,UAAU;IACjBI,KAAK,EAAEA,CAAA,KAAM;MACXH,UAAU,IAAIA,UAAU,CAACP,GAAG,CAAC;IAC/B,CAAC;IACDW,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIJ,UAAU,EAAE;QACd;QACA,MAAMK,WAAW,GAAGZ,GAAG,CAACa,OAAO,CAAC,CAAC;QACjCb,GAAG,CAACc,IAAI,CAAC,YAAY,EAAE;UAAEC,IAAI,EAAEH;QAAY,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,CAAC;EAEF,OAAO,IAAI;AACb,CAAC;;AAED;AAAAJ,GAAA,CApBMH,eAAe;EAAA,QACP3I,MAAM,EAElBD,YAAY;AAAA;AAAAuJ,GAAA,GAHRX,eAAe;AAqBrB,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAMlB,GAAG,GAAGtI,MAAM,CAAC,CAAC;EAEpB,oBACEa,OAAA;IAAK0E,SAAS,EAAC,gGAAgG;IAC1GuC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,gBACvD/G,OAAA;MACEgH,OAAO,EAAEA,CAAA,KAAMS,GAAG,CAACmB,MAAM,CAAC,CAAE;MAC5BlE,SAAS,EAAC,2EAA2E;MACrFwB,KAAK,EAAC,SAAS;MAAAa,QAAA,eAEf/G,OAAA,CAACP,QAAQ;QAACiF,SAAS,EAAC;MAAuB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eACTtH,OAAA;MACEgH,OAAO,EAAEA,CAAA,KAAMS,GAAG,CAACoB,OAAO,CAAC,CAAE;MAC7BnE,SAAS,EAAC,kDAAkD;MAC5DwB,KAAK,EAAC,UAAU;MAAAa,QAAA,eAEhB/G,OAAA,CAACN,SAAS;QAACgF,SAAS,EAAC;MAAuB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACqB,GAAA,CAtBID,iBAAiB;EAAA,QACTvJ,MAAM;AAAA;AAAA2J,GAAA,GADdJ,iBAAiB;AAwBvB,MAAMK,SAAS,GAAGA,CAAC;EACjBC,MAAM;EACNR,IAAI,GAAG,EAAE;EACTS,YAAY;EACZC,MAAM,GAAG,EAAE;EACXC,QAAQ,GAAG,EAAE;EACbC,IAAI,GAAG,EAAE;EACTC,aAAa;EACbC,eAAe;EACfC,YAAY,GAAG,EAAE;EACjBC,cAAc;EACdC,cAAc,GAAG,EAAE;EACnBC,QAAQ,GAAG,IAAI;EACfC,YAAY,GAAG,KAAK;EACpB5B,UAAU;EACV6B,aAAa;EACbC,eAAe;EACfC,WAAW;EACXpF,SAAS,GAAG,EAAE;EACdqF,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvL,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgI,YAAY,EAAEwD,eAAe,CAAC,GAAGxL,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyL,WAAW,EAAEC,cAAc,CAAC,GAAG1L,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0J,WAAW,EAAEiC,cAAc,CAAC,GAAG3L,QAAQ,CAAC6J,IAAI,CAAC;EACpD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7L,QAAQ,CAAC;IACjDuK,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVqB,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAMC,eAAe,GAAG9L,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM+L,kBAAkB,GAAIC,SAAS,IAAK;IACxC,OAAO;MACL1B,MAAM,EAAE0B,SAAS,IAAI,EAAE;MAAE;MACzBzB,QAAQ,EAAEyB,SAAS,IAAI,EAAE;MAAE;MAC3BxB,IAAI,EAAEwB,SAAS,IAAI,EAAE;MAAE;MACvBH,QAAQ,EAAE,IAAI;MAAE;MAChBI,WAAW,EAAED,SAAS,IAAI,EAAE;MAAE;MAC9BE,eAAe,EAAEF,SAAS,IAAI,EAAE,CAAC;IACnC,CAAC;EACH,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIC,OAAO,IAAK;IACpCV,cAAc,CAACU,OAAO,CAAC;IACvB,MAAMC,aAAa,GAAGN,kBAAkB,CAACK,OAAO,CAAC;IACjDR,gBAAgB,CAACU,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,GAAGD;IACL,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACZ,aAAa,CAACrB,MAAM,EAAE,OAAO,EAAE;;IAEpC;IACA,IAAIG,aAAa,EAAE;MACjB,OAAOH,MAAM,CAACkC,MAAM,CAAC7F,KAAK,IACxBA,KAAK,CAAC8F,EAAE,KAAKhC,aAAa,CAACgC,EAAE,IAC5BhD,WAAW,IAAI,EAAE,IAAI9C,KAAK,CAACK,UAAU,KAAK,MAAO,CAAC;MACrD,CAAC;IACH;;IAEA;IACA,IAAIyC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOa,MAAM,CAACkC,MAAM,CAAC7F,KAAK,IAAIA,KAAK,CAACK,UAAU,KAAK,MAAM,IAAIL,KAAK,CAACK,UAAU,KAAK,SAAS,CAAC;IAC9F;IAEA,OAAOsD,MAAM;EACf,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACf,aAAa,CAACpB,QAAQ,IAAI,CAACQ,YAAY,EAAE,OAAO,EAAE;;IAEvD;IACA,IAAIL,eAAe,EAAE;MACnB,OAAOH,QAAQ,CAACiC,MAAM,CAACpF,OAAO,IAAIA,OAAO,CAACqF,EAAE,KAAK/B,eAAe,CAAC+B,EAAE,CAAC;IACtE;;IAEA;IACA,IAAIhD,WAAW,IAAI,EAAE,EAAE,OAAOc,QAAQ;;IAEtC;IACA,OAAOA,QAAQ,CAACiC,MAAM,CAACpF,OAAO,IAC5BA,OAAO,CAAClB,IAAI,KAAK,OAAO,IAAIkB,OAAO,CAAClB,IAAI,KAAK,QAC/C,CAAC;EACH,CAAC;EAED,MAAMyG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInB,WAAW,IAAInB,YAAY,EAAE;MAC/BmB,WAAW,CAACoB,OAAO,CAAC,CAACvC,YAAY,CAACwC,GAAG,EAAExC,YAAY,CAACyC,GAAG,CAAC,EAAE,EAAE,EAAE;QAC5DC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAInB,eAAe,CAACoB,OAAO,EAAE;MAC3B,IAAI,CAACnF,YAAY,EAAE;QAAA,IAAAoF,qBAAA,EAAAC,sBAAA;QACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAtB,eAAe,CAACoB,OAAO,EAACG,iBAAiB,cAAAF,qBAAA,uBAAzCA,qBAAA,CAAAG,IAAA,CAAAF,sBAA4C,CAAC;MAC/C,CAAC,MAAM;QAAA,IAAAG,qBAAA,EAAAC,SAAA;QACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAC,QAAQ,EAACC,cAAc,cAAAH,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC7B;MACAjC,eAAe,CAAC,CAACxD,YAAY,CAAC;IAChC;EACF,CAAC;EAED,MAAM4F,eAAe,GAAG9L,SAAS,CAACwJ,QAAQ,CAAC;EAE3C,oBACEjK,OAAA;IACEwM,GAAG,EAAE9B,eAAgB;IACrBhG,SAAS,EAAE,0DAA0DA,SAAS,EAAG;IACjFuC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,eAEpD/G,OAAA;MAAK0E,SAAS,EAAE,GAAGqF,MAAM,WAAY;MAAAhD,QAAA,gBACnC/G,OAAA,CAACnB,YAAY;QACXmK,MAAM,EAAEA,MAAO;QACfR,IAAI,EAAEA,IAAK;QACXvB,KAAK,EAAE;UAAE8C,MAAM,EAAE,MAAM;UAAE0C,KAAK,EAAE;QAAO,CAAE;QACzCC,WAAW,EAAE,KAAM;QACnBC,kBAAkB,EAAE,KAAM;QAC1BjI,SAAS,EAAC,YAAY;QAAAqC,QAAA,gBAEtB/G,OAAA,CAAClB,SAAS;UACR6B,GAAG,EAAE4L,eAAe,CAAC5L,GAAI;UACzBC,WAAW,EAAE2L,eAAe,CAAC3L,WAAY;UACzCgM,OAAO,EAAE;QAAG;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEFtH,OAAA,CAAC8H,eAAe;UACdC,UAAU,EAAEA,UAAW;UACvBC,UAAU,EAAEqC;QAAe;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEFtH,OAAA,CAAC0I,iBAAiB;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGpB2B,YAAY,iBACXjJ,OAAA,CAACjB,MAAM;UACL8N,QAAQ,EAAE,CAAC5D,YAAY,CAACwC,GAAG,EAAExC,YAAY,CAACyC,GAAG,CAAE;UAC/C5K,IAAI,EAAEuE,sBAAsB,CAAC,CAAE;UAAA0B,QAAA,eAE/B/G,OAAA,CAAChB,KAAK;YAAC0F,SAAS,EAAC,cAAc;YAAAqC,QAAA,eAC7B/G,OAAA;cAAK8M,uBAAuB,EAAE;gBAC5BC,MAAM,EAAE9G,iBAAiB,CACvB,eAAe,EACf;AACpB;AACA;AACA;AACA,iCAAiCgD,YAAY,CAACwC,GAAG,CAACuB,OAAO,CAAC,CAAC,CAAC;AAC5D,iCAAiC/D,YAAY,CAACyC,GAAG,CAACsB,OAAO,CAAC,CAAC,CAAC;AAC5D;AACA;AACA,qBAAqB,EACD,SACF;cACF;YAAE;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACT,EAGA4B,MAAM,CAACzB,GAAG,CAAClC,KAAK,IAAI;UACnB,MAAMpC,UAAU,GAAG,CAAAkG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgC,EAAE,MAAK9F,KAAK,CAAC8F,EAAE;UACjD,MAAM4B,UAAU,GAAG3H,aAAa,CAACC,KAAK,EAAEpC,UAAU,CAAC;UAEnD,oBACEnD,OAAA,CAACf,QAAQ;YAEPiO,SAAS,EAAE3H,KAAK,CAAC4H,MAAM,CAAC1F,GAAG,CAAC2F,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;YAAA,GAC7CuB,UAAU;YACdI,aAAa,EAAE;cACbnF,KAAK,EAAEA,CAAA,KAAM0B,aAAa,IAAIA,aAAa,CAACrE,KAAK,CAAC;cAClD+H,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAI,CAACpK,UAAU,EAAE;kBACfoK,CAAC,CAACC,MAAM,CAACC,QAAQ,CAAC;oBAAE/K,OAAO,EAAE,GAAG;oBAAED,MAAM,EAAEwK,UAAU,CAACxK,MAAM,GAAG;kBAAE,CAAC,CAAC;gBACpE;cACF,CAAC;cACDiL,QAAQ,EAAGH,CAAC,IAAK;gBACf,IAAI,CAACpK,UAAU,EAAE;kBACfoK,CAAC,CAACC,MAAM,CAACC,QAAQ,CAAC;oBAAE/K,OAAO,EAAEuK,UAAU,CAACvK,OAAO;oBAAED,MAAM,EAAEwK,UAAU,CAACxK;kBAAO,CAAC,CAAC;gBAC/E;cACF;YACF,CAAE;YAAAsE,QAAA,eAEF/G,OAAA,CAAChB,KAAK;cAAC0F,SAAS,EAAC,cAAc;cAAAqC,QAAA,eAC7B/G,OAAA;gBAAK8M,uBAAuB,EAAE;kBAC5BC,MAAM,EAAE9G,iBAAiB,CACvBV,KAAK,CAAC1E,IAAI,EACV;AACtB;AACA,6DAA6D0E,KAAK,CAACoI,WAAW,IAAI,0BAA0B;AAC5G;AACA;AACA;AACA;AACA,6EAA6EpI,KAAK,CAACqI,QAAQ;AAC3F;AACA;AACA;AACA,0FAA0FrI,KAAK,CAACK,UAAU,IAAI,UAAU;AACxH;AACA;AACA;AACA;AACA;AACA;AACA,6FAA6FL,KAAK,CAACG,YAAY,IAAI,SAAS;AAC5H;AACA;AACA;AACA,mFAAmFH,KAAK,CAACsI,SAAS,IAAI,CAAC;AACvG;AACA;AACA;AACA,uBAAuB,EACD,OACF;gBACF;cAAE;gBAAA1G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC,GAnDH/B,KAAK,CAAC8F,EAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDL,CAAC;QAEf,CAAC,CAAC,EAGDkC,cAAc,iBACbxJ,OAAA,CAACf,QAAQ;UACPiO,SAAS,EAAE1D,cAAc,CAAC/B,GAAG,CAAC2F,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;UACnD/I,KAAK,EAAEzB,YAAY,CAACK,KAAM;UAC1BkB,MAAM,EAAE,CAAE;UACVC,OAAO,EAAE,IAAK;UACdM,SAAS,EAAC,OAAO;UACjB6C,OAAO,EAAC,OAAO;UACfC,QAAQ,EAAC,OAAO;UAChBpB,SAAS,EAAC;QAAgB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACF,EAGAmC,cAAc,CAAChC,GAAG,CAAC,CAACqG,KAAK,EAAEC,KAAK,kBAC/B/N,OAAA,CAACjB,MAAM;UAEL8N,QAAQ,EAAE,CAACiB,KAAK,CAACrC,GAAG,EAAEqC,KAAK,CAACpC,GAAG,CAAE;UACjC5K,IAAI,EAAEmC,mBAAmB,CAAC,SAAS,EAAE,KAAK,CAAE;UAAA8D,QAAA,eAE5C/G,OAAA,CAAChB,KAAK;YAAC0F,SAAS,EAAC,cAAc;YAAAqC,QAAA,eAC7B/G,OAAA;cAAK8M,uBAAuB,EAAE;gBAC5BC,MAAM,EAAE9G,iBAAiB,CACvB,YAAY8H,KAAK,GAAG,CAAC,EAAE,EACvB;AACpB;AACA;AACA;AACA,iCAAiCD,KAAK,CAACrC,GAAG,CAACuB,OAAO,CAAC,CAAC,CAAC;AACrD,iCAAiCc,KAAK,CAACpC,GAAG,CAACsB,OAAO,CAAC,CAAC,CAAC;AACrD;AACA;AACA,qBAAqB,EACD,KACF;cACF;YAAE;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC,GApBHyG,KAAK;UAAA5G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBJ,CACT,CAAC,EAGDoC,QAAQ,IAAIN,IAAI,CAAC3B,GAAG,CAACpB,GAAG,IAAI;UAC3B,MAAMlD,UAAU,GAAGoG,YAAY,CAACyE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAC5C,EAAE,KAAKhF,GAAG,CAACgF,EAAE,CAAC;UAExE,oBACErL,OAAA,CAACjB,MAAM;YAEL8N,QAAQ,EAAE,CAACxG,GAAG,CAACoF,GAAG,EAAEpF,GAAG,CAACqF,GAAG,CAAE;YAC7B5K,IAAI,EAAEmC,mBAAmB,CAACoD,GAAG,CAACvB,IAAI,IAAI,SAAS,EAAE3B,UAAU,CAAE;YAC7DkK,aAAa,EAAE;cACbnF,KAAK,EAAEA,CAAA,KAAM4B,WAAW,IAAIA,WAAW,CAACzD,GAAG;YAC7C,CAAE;YAAAU,QAAA,eAEF/G,OAAA,CAAChB,KAAK;cAAC0F,SAAS,EAAC,cAAc;cAAAqC,QAAA,eAC7B/G,OAAA;gBAAK8M,uBAAuB,EAAE;kBAC5BC,MAAM,EAAE9G,iBAAiB,CACvBI,GAAG,CAACxF,IAAI,EACR;AACtB;AACA;AACA;AACA,gCAAgCwF,GAAG,CAACvB,IAAI,IAAI,KAAK;AACjD;AACA;AACA;AACA,4BAA4BuB,GAAG,CAACsH,WAAW,GAAG,oCAAoCtH,GAAG,CAACsH,WAAW,MAAM,GAAG,EAAE;AAC5G;AACA,4BAA4BtH,GAAG,CAAC6H,MAAM,GAAG;AACzC;AACA;AACA;AACA,kFAAkF7H,GAAG,CAAC6H,MAAM,CAAClB,OAAO,CAAC,CAAC,CAAC;AACvG;AACA;AACA;AACA,2BAA2B,GAAG,EAAE;AAChC;AACA,4BAA4B3G,GAAG,CAAC8H,SAAS,GAAG;AAC5C;AACA;AACA;AACA,kCAAkC9H,GAAG,CAAC8H,SAAS,CAAC1G,GAAG,CAAC2G,OAAO,IAAI;AAC/D;AACA,sCAAsCA,OAAO;AAC7C;AACA,iCAAiC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC3C;AACA;AACA,2BAA2B,GAAG,EAAE;AAChC;AACA,uBAAuB,EACD,KACF;gBACF;cAAE;gBAAAlH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC,GAhDHjB,GAAG,CAACgF,EAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDL,CAAC;QAEb,CAAC,CAAC,EAGDiC,YAAY,CAAC9B,GAAG,CAACpB,GAAG,iBACnBrG,OAAA,CAACjB,MAAM;UAEL8N,QAAQ,EAAE,CAACxG,GAAG,CAACoF,GAAG,EAAEpF,GAAG,CAACqF,GAAG,CAAE;UAC7B5K,IAAI,EAAEmC,mBAAmB,CAACoD,GAAG,CAACvB,IAAI,IAAI,SAAS,EAAE,IAAI;QAAE,GAFlD,YAAYuB,GAAG,CAACgF,EAAE,EAAE;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAG1B,CACF,CAAC,EAGD,CAACqC,YAAY,IAAIL,eAAe,KAAKH,QAAQ,CAAC1B,GAAG,CAACzB,OAAO,IAAI;UAC5D,MAAM7C,UAAU,GAAG,CAAAmG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+B,EAAE,MAAKrF,OAAO,CAACqF,EAAE;UACrD,MAAMiD,YAAY,GAAGvI,eAAe,CAACC,OAAO,EAAE7C,UAAU,CAAC;UAEzD,oBACEnD,OAAA,CAACtB,KAAK,CAAC6P,QAAQ;YAAAxH,QAAA,GAEZf,OAAO,CAACmH,MAAM,IAAInH,OAAO,CAACmH,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAC1CxO,OAAA,CAACjB,MAAM;cACL8N,QAAQ,EAAE,CAAC7G,OAAO,CAACmH,MAAM,CAAC,CAAC,CAAC,CAAC1B,GAAG,EAAEzF,OAAO,CAACmH,MAAM,CAAC,CAAC,CAAC,CAACzB,GAAG,CAAE;cACzD5K,IAAI,EAAE+D,mBAAmB,CAAC,OAAO,EAAEmB,OAAO,CAAClB,IAAI,CAAE;cAAAiC,QAAA,eAEjD/G,OAAA,CAAChB,KAAK;gBAAC0F,SAAS,EAAC,cAAc;gBAAAqC,QAAA,eAC7B/G,OAAA;kBAAK8M,uBAAuB,EAAE;oBAC5BC,MAAM,EAAE9G,iBAAiB,CACvB,GAAGD,OAAO,CAACnF,IAAI,UAAU,EACzB;AAC1B;AACA;AACA;AACA;AACA;AACA,kCAAkCmF,OAAO,CAAClB,IAAI,GAAG,SAASkB,OAAO,CAAClB,IAAI,CAAC2J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1I,OAAO,CAAClB,IAAI,CAAC6J,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;AAC7H;AACA;AACA,2BAA2B,EACD,SACF;kBACF;gBAAE;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACT,eAGDtH,OAAA,CAACf,QAAQ;cACPiO,SAAS,EAAElH,OAAO,CAACmH,MAAM,CAAC1F,GAAG,CAACqG,KAAK,IAAI,CAACA,KAAK,CAACrC,GAAG,EAAEqC,KAAK,CAACpC,GAAG,CAAC,CAAE;cAAA,GAC3D4C,YAAY;cAChBjB,aAAa,EAAE;gBACbnF,KAAK,EAAEA,CAAA,KAAM2B,eAAe,IAAIA,eAAe,CAAC7D,OAAO,CAAC;gBACxDsH,SAAS,EAAGC,CAAC,IAAK;kBAChB,IAAI,CAACpK,UAAU,EAAE;oBACfoK,CAAC,CAACC,MAAM,CAACC,QAAQ,CAAC;sBAChB/K,OAAO,EAAE,CAAC;sBACVD,MAAM,EAAE6L,YAAY,CAAC7L,MAAM,GAAG;oBAChC,CAAC,CAAC;kBACJ;gBACF,CAAC;gBACDiL,QAAQ,EAAGH,CAAC,IAAK;kBACf,IAAI,CAACpK,UAAU,EAAE;oBACfoK,CAAC,CAACC,MAAM,CAACC,QAAQ,CAAC;sBAChB/K,OAAO,EAAE4L,YAAY,CAAC5L,OAAO;sBAC7BD,MAAM,EAAE6L,YAAY,CAAC7L;oBACvB,CAAC,CAAC;kBACJ;gBACF;cACF,CAAE;cAAAsE,QAAA,eAEF/G,OAAA,CAAChB,KAAK;gBAAC0F,SAAS,EAAC,cAAc;gBAAAqC,QAAA,eAC7B/G,OAAA;kBAAK8M,uBAAuB,EAAE;oBAC5BC,MAAM,EAAE9G,iBAAiB,CACvBD,OAAO,CAACnF,IAAI,EACZ;AACxB;AACA;AACA,iHACgCmF,OAAO,CAAClB,IAAI,KAAK,OAAO,GAAG,yBAAyB,GACpDkB,OAAO,CAAClB,IAAI,KAAK,QAAQ,GAAG,6BAA6B,GACzDkB,OAAO,CAAClB,IAAI,KAAK,SAAS,GAAG,2BAA2B,GACxD,+BAA+B;AAC/D,kCACkCkB,OAAO,CAAClB,IAAI,KAAK,OAAO,GAAG,IAAI,GAC/BkB,OAAO,CAAClB,IAAI,KAAK,QAAQ,GAAG,IAAI,GAChCkB,OAAO,CAAClB,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;AAC1E,kCAAkCkB,OAAO,CAAClB,IAAI,GAAGkB,OAAO,CAAClB,IAAI,CAAC2J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1I,OAAO,CAAClB,IAAI,CAAC6J,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS;AACzH;AACA;AACA;AACA,2EAA2E3I,OAAO,CAAC2H,WAAW,IAAI,0BAA0B;AAC5H;AACA;AACA;AACA;AACA;AACA,oCAAoC3H,OAAO,CAAC4H,QAAQ,GAAG,IAAI,GAAG,GAAG5H,OAAO,CAAC4H,QAAQ,GAAG,GAAG,GAAG,CAAC5H,OAAO,CAAC4H,QAAQ,GAAG,IAAI,EAAEZ,OAAO,CAAC,CAAC,CAAC,IAAI;AAClI;AACA;AACA;AACA;AACA;AACA,oCAAoChH,OAAO,CAAC6H,SAAS,GAAG,CAAC,GAAG,IAAI7H,OAAO,CAAC6H,SAAS,GAAG,GAAG,GAAG7H,OAAO,CAAC6H,SAAS,IAAI,CAAC,GAAG;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF7H,OAAO,CAAC4I,UAAU,IAAI,WAAW;AACjH;AACA;AACA;AACA,iFAAiF5I,OAAO,CAAC6I,QAAQ,IAAI,CAAC;AACtG;AACA;AACA;AACA,yBAAyB,EACD,SACF;kBACF;gBAAE;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGVtB,OAAO,CAACmH,MAAM,IAAInH,OAAO,CAACmH,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAC1CxO,OAAA,CAACjB,MAAM;cACL8N,QAAQ,EAAE,CAAC7G,OAAO,CAACmH,MAAM,CAACnH,OAAO,CAACmH,MAAM,CAACqB,MAAM,GAAG,CAAC,CAAC,CAAC/C,GAAG,EAAEzF,OAAO,CAACmH,MAAM,CAACnH,OAAO,CAACmH,MAAM,CAACqB,MAAM,GAAG,CAAC,CAAC,CAAC9C,GAAG,CAAE;cACzG5K,IAAI,EAAE+D,mBAAmB,CAAC,QAAQ,EAAEmB,OAAO,CAAClB,IAAI,CAAE;cAAAiC,QAAA,eAElD/G,OAAA,CAAChB,KAAK;gBAAC0F,SAAS,EAAC,cAAc;gBAAAqC,QAAA,eAC7B/G,OAAA;kBAAK8M,uBAAuB,EAAE;oBAC5BC,MAAM,EAAE9G,iBAAiB,CACvB,GAAGD,OAAO,CAACnF,IAAI,WAAW,EAC1B;AAC1B;AACA;AACA;AACA;AACA;AACA,kCAAkCmF,OAAO,CAAClB,IAAI,GAAG,SAASkB,OAAO,CAAClB,IAAI,CAAC2J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1I,OAAO,CAAClB,IAAI,CAAC6J,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;AAC7H;AACA;AACA,2BAA2B,EACD,SACF;kBACF;gBAAE;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACT;UAAA,GApIkBtB,OAAO,CAACqF,EAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqIf,CAAC;QAErB,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eAGftH,OAAA,CAACsG,iBAAiB;QAChBC,aAAa,EAAE2D,WAAY;QAC3B1D,YAAY,EAAEyD,QAAS;QACvBxD,QAAQ,EAAE8E,YAAa;QACvB7E,YAAY,EAAEmF,gBAAiB;QAC/BlF,YAAY,EAAEA;MAAa;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC0C,GAAA,CAhfIjB,SAAS;AAAA+F,GAAA,GAAT/F,SAAS;AAkff,eAAeA,SAAS;AAAC,IAAAlB,EAAA,EAAAY,GAAA,EAAAK,GAAA,EAAAgG,GAAA;AAAAC,YAAA,CAAAlH,EAAA;AAAAkH,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}