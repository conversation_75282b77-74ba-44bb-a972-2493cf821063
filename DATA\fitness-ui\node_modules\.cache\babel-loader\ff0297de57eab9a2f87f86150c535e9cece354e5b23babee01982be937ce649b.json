{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\components\\\\ModernMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { MapContainer, <PERSON>ile<PERSON><PERSON>er, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiLayers, FiMaximize2, FiMinimize2, FiNavigation, FiZoomIn, FiZoomOut, FiMap, FiGlobe, FiMoon, FiTriangle } from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiGlobe\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiTriangle\n  }\n};\n\n// Modern Strava-style colors with gradients\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  darkBlue: '#005BB5',\n  green: '#00D924',\n  darkGreen: '#00A01C',\n  red: '#FF0000',\n  darkRed: '#CC0000',\n  purple: '#8B5CF6',\n  darkPurple: '#7C3AED',\n  yellow: '#FFC107',\n  darkYellow: '#F59E0B',\n  // Activity type colors\n  running: '#FC4C02',\n  cycling: '#0073E6',\n  hiking: '#00D924',\n  swimming: '#06B6D4',\n  // Segment type colors\n  climb: '#EF4444',\n  sprint: '#10B981',\n  descent: '#3B82F6',\n  flat: '#8B5CF6'\n};\n\n// Route difficulty styling\nconst routeDifficultyStyles = {\n  easy: {\n    weight: 4,\n    opacity: 0.8,\n    color: stravaColors.green\n  },\n  moderate: {\n    weight: 5,\n    opacity: 0.85,\n    color: stravaColors.yellow\n  },\n  hard: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.orange\n  },\n  extreme: {\n    weight: 7,\n    opacity: 0.95,\n    color: stravaColors.red\n  }\n};\n\n// Activity type styling\nconst activityTypeStyles = {\n  running: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.running,\n    dashArray: null\n  },\n  cycling: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.cycling,\n    dashArray: null\n  },\n  hiking: {\n    weight: 4,\n    opacity: 0.85,\n    color: stravaColors.hiking,\n    dashArray: '8, 4'\n  },\n  swimming: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.swimming,\n    dashArray: '12, 8'\n  }\n};\n\n// Modern POI icons with detailed styling\nconst createModernPOIIcon = (poiType, isSelected = false) => {\n  const iconMap = {\n    restaurant: '🍽️',\n    cafe: '☕',\n    hotel: '🏨',\n    gas_station: '⛽',\n    hospital: '🏥',\n    pharmacy: '💊',\n    bank: '🏦',\n    atm: '💳',\n    parking: '🅿️',\n    toilet: '🚻',\n    water: '💧',\n    viewpoint: '👁️',\n    monument: '🏛️',\n    park: '🌳',\n    beach: '🏖️',\n    mountain: '⛰️',\n    default: '📍'\n  };\n  const size = isSelected ? 44 : 36;\n  const shadowSize = isSelected ? 16 : 12;\n  return L.divIcon({\n    html: `\n      <div class=\"modern-poi-marker ${isSelected ? 'selected' : ''}\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.blue} 0%, ${stravaColors.darkBlue} 100%);\n        color: white;\n        border-radius: 50%;\n        width: ${size}px;\n        height: ${size}px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: ${size * 0.4}px;\n        border: 3px solid white;\n        box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0,0,0,0.2),\n                    0 ${shadowSize / 2}px ${shadowSize}px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        cursor: pointer;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">\n          ${iconMap[poiType] || iconMap.default}\n        </span>\n      </div>\n    `,\n    className: 'modern-poi-icon',\n    iconSize: [size, size],\n    iconAnchor: [size / 2, size / 2]\n  });\n};\n\n// Modern segment markers (start/finish)\nconst createSegmentMarker = (type, segmentType = 'general') => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    general: stravaColors.purple\n  };\n  const icons = {\n    start: '🚀',\n    finish: '🎯'\n  };\n  return L.divIcon({\n    html: `\n      <div class=\"segment-marker ${type}\" style=\"\n        background: linear-gradient(135deg, ${colors[segmentType]} 0%, ${colors[segmentType]}dd 100%);\n        color: white;\n        border-radius: 8px;\n        width: 32px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 6px 20px rgba(0,0,0,0.15),\n                    0 2px 6px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.2);\n        font-weight: bold;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));\">\n          ${icons[type]}\n        </span>\n      </div>\n    `,\n    className: 'segment-marker-icon',\n    iconSize: [32, 32],\n    iconAnchor: [16, 16]\n  });\n};\n\n// User location icon\nconst createUserLocationIcon = () => {\n  return L.divIcon({\n    html: `\n      <div class=\"user-location-marker\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.orange} 0%, ${stravaColors.darkOrange} 100%);\n        color: white;\n        border-radius: 50%;\n        width: 40px;\n        height: 40px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 16px;\n        border: 4px solid white;\n        box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),\n                    0 3px 10px rgba(0,0,0,0.2),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: bold;\n        transform: translateZ(0);\n        animation: pulse 2s infinite;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">📍</span>\n      </div>\n    `,\n    className: 'user-location-icon',\n    iconSize: [40, 40],\n    iconAnchor: [20, 20]\n  });\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({\n  onStyleChange,\n  currentStyle,\n  onLocate,\n  onFullscreen,\n  isFullscreen\n}) => {\n  _s();\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute top-4 right-4 z-[1000] space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowStyleSelector(!showStyleSelector),\n        className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\",\n        style: {\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n        },\n        title: \"Change map style\",\n        children: /*#__PURE__*/_jsxDEV(FiLayers, {\n          className: \"h-5 w-5 text-gray-700\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), showStyleSelector && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\",\n        style: {\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: Object.entries(mapStyles).map(([key, style]) => {\n          const IconComponent = style.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              onStyleChange(key);\n              setShowStyleSelector(false);\n            },\n            className: `w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'}`,\n            style: currentStyle === key ? {\n              backgroundColor: '#FFF7ED',\n              color: stravaColors.orange\n            } : {},\n            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: style.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onLocate,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: \"Center on your location\",\n      children: /*#__PURE__*/_jsxDEV(FiNavigation, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onFullscreen,\n      className: \"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\",\n      style: {\n        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n      },\n      title: isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\",\n      children: isFullscreen ? /*#__PURE__*/_jsxDEV(FiMinimize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(FiMaximize2, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n\n// Map event handler\n_s(StravaMapControls, \"PY0GN+P+8EDZXt82pN67/JEoRp8=\");\n_c = StravaMapControls;\nconst MapEventHandler = ({\n  onMapClick,\n  onMapReady\n}) => {\n  _s2();\n  const map = useMap();\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    }\n  });\n  return null;\n};\n\n// Strava-style zoom controls\n_s2(MapEventHandler, \"tmcOhplWkk/SgX5HNxHxB5dt97g=\", false, function () {\n  return [useMap, useMapEvents];\n});\n_c2 = MapEventHandler;\nconst StravaZoomControl = () => {\n  _s3();\n  const map = useMap();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\",\n    style: {\n      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomIn(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\",\n      title: \"Zoom in\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomIn, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => map.zoomOut(),\n      className: \"p-3 hover:bg-gray-50 transition-all duration-150\",\n      title: \"Zoom out\",\n      children: /*#__PURE__*/_jsxDEV(FiZoomOut, {\n        className: \"h-5 w-5 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 5\n  }, this);\n};\n_s3(StravaZoomControl, \"cX187cvZ2hODbkaiLn05gMk1sCM=\", false, function () {\n  return [useMap];\n});\n_c3 = StravaZoomControl;\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  _s4();\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const mapContainerRef = useRef(null);\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        var _mapContainerRef$curr, _mapContainerRef$curr2;\n        (_mapContainerRef$curr = (_mapContainerRef$curr2 = mapContainerRef.current).requestFullscreen) === null || _mapContainerRef$curr === void 0 ? void 0 : _mapContainerRef$curr.call(_mapContainerRef$curr2);\n      } else {\n        var _document$exitFullscr, _document;\n        (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n  const currentMapStyle = mapStyles[mapStyle];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: mapContainerRef,\n    className: `relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`,\n    style: {\n      boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${height} relative`,\n      children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n        center: center,\n        zoom: zoom,\n        style: {\n          height: '100%',\n          width: '100%'\n        },\n        zoomControl: false,\n        attributionControl: false,\n        className: \"rounded-xl\",\n        children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n          url: currentMapStyle.url,\n          attribution: currentMapStyle.attribution,\n          maxZoom: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MapEventHandler, {\n          onMapClick: onMapClick,\n          onMapReady: setMapInstance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StravaZoomControl, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), userPosition && /*#__PURE__*/_jsxDEV(Marker, {\n          position: [userPosition.lat, userPosition.lng],\n          icon: createStravaIcon('user', stravaColors.blue),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-gray-900\",\n                children: \"Your Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Current position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this), routes.map(route => {\n          const isSelected = (selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id;\n          const routeColor = isSelected ? stravaColors.orange : stravaColors.blue;\n          return /*#__PURE__*/_jsxDEV(Polyline, {\n            positions: route.points.map(p => [p.lat, p.lng]),\n            color: routeColor,\n            weight: isSelected ? 6 : 4,\n            opacity: isSelected ? 1 : 0.7,\n            eventHandlers: {\n              click: () => onRouteSelect && onRouteSelect(route)\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900 mb-2\",\n                  style: {\n                    color: stravaColors.orange\n                  },\n                  children: route.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-2 text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Distance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: [route.distance, \"km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Difficulty\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: route.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)\n          }, route.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this);\n        }), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n          positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n          color: stravaColors.orange,\n          weight: 6,\n          opacity: 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n          positions: optimizedRoute.map(p => [p.lat, p.lng]),\n          color: stravaColors.green,\n          weight: 5,\n          opacity: 0.9,\n          dashArray: \"8, 8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [point.lat, point.lng],\n          icon: createStravaIcon('poi', stravaColors.yellow),\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-gray-900\",\n                children: [\"Point \", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Planning waypoint\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this)), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [poi.lat, poi.lng],\n          icon: createStravaIcon('poi', stravaColors.green),\n          eventHandlers: {\n            click: () => onPOISelect && onPOISelect(poi)\n          },\n          children: /*#__PURE__*/_jsxDEV(Popup, {\n            className: \"strava-popup\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                style: {\n                  color: stravaColors.green\n                },\n                children: poi.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mb-2\",\n                children: poi.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), poi.rating && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center bg-yellow-50 p-2 rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-500\",\n                  children: \"\\u2B50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm ml-1 font-medium\",\n                  children: poi.rating.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 15\n          }, this)\n        }, poi.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n          position: [poi.lat, poi.lng],\n          icon: createStravaIcon('poi', stravaColors.red)\n        }, `selected-${poi.id}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this)), (showSegments || selectedSegment) && segments.map(segment => {\n          const isSelected = (selectedSegment === null || selectedSegment === void 0 ? void 0 : selectedSegment.id) === segment.id;\n          const segmentColor = isSelected ? stravaColors.orange : segment.type === 'climb' ? stravaColors.red : segment.type === 'sprint' ? stravaColors.green : segment.type === 'descent' ? stravaColors.blue : stravaColors.purple;\n          return /*#__PURE__*/_jsxDEV(Polyline, {\n            positions: segment.points.map(point => [point.lat, point.lng]),\n            color: segmentColor,\n            weight: isSelected ? 7 : 5,\n            opacity: isSelected ? 1 : 0.8,\n            eventHandlers: {\n              click: () => onSegmentSelect && onSegmentSelect(segment)\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              className: \"strava-popup\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900 mb-2\",\n                  style: {\n                    color: segmentColor\n                  },\n                  children: segment.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3\",\n                  children: segment.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-2 text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Distance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Elevation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Record\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.recordTime\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 p-2 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Attempts\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-900\",\n                      children: segment.attempts\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 607,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this)\n          }, segment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StravaMapControls, {\n        onStyleChange: setMapStyle,\n        currentStyle: mapStyle,\n        onLocate: handleLocate,\n        onFullscreen: handleFullscreen,\n        isFullscreen: isFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 409,\n    columnNumber: 5\n  }, this);\n};\n_s4(ModernMap, \"3DbC9vvdlyb0msnTDPDDuDkiJw0=\");\n_c4 = ModernMap;\nexport default ModernMap;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StravaMapControls\");\n$RefreshReg$(_c2, \"MapEventHandler\");\n$RefreshReg$(_c3, \"StravaZoomControl\");\n$RefreshReg$(_c4, \"ModernMap\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "useMap", "L", "FiLayers", "FiMaximize2", "FiMinimize2", "FiNavigation", "FiZoomIn", "FiZoomOut", "FiMap", "FiGlobe", "FiMoon", "FiTriangle", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "mapStyles", "strava", "url", "attribution", "name", "icon", "satellite", "dark", "terrain", "stravaColors", "orange", "darkOrange", "blue", "darkBlue", "green", "<PERSON><PERSON><PERSON>", "red", "darkRed", "purple", "dark<PERSON><PERSON>ple", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "running", "cycling", "hiking", "swimming", "climb", "sprint", "descent", "flat", "routeDifficultyStyles", "easy", "weight", "opacity", "color", "moderate", "hard", "extreme", "activityTypeStyles", "dashArray", "createModernPOIIcon", "poiType", "isSelected", "iconMap", "restaurant", "cafe", "hotel", "gas_station", "hospital", "pharmacy", "bank", "atm", "parking", "toilet", "water", "viewpoint", "monument", "park", "beach", "mountain", "default", "size", "shadowSize", "divIcon", "html", "className", "iconSize", "iconAnchor", "createSegmentMarker", "type", "segmentType", "colors", "general", "icons", "start", "finish", "createUserLocationIcon", "StravaMapControls", "onStyleChange", "currentStyle", "onLocate", "onFullscreen", "isFullscreen", "_s", "showStyleSelector", "setShowStyleSelector", "children", "onClick", "style", "boxShadow", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "map", "key", "IconComponent", "backgroundColor", "_c", "MapEventHandler", "onMapClick", "onMapReady", "_s2", "click", "ready", "_c2", "StravaZoomControl", "_s3", "zoomIn", "zoomOut", "_c3", "ModernMap", "center", "zoom", "userPosition", "routes", "segments", "pois", "<PERSON><PERSON><PERSON><PERSON>", "selectedSegment", "selectedPOIs", "optimizedRoute", "planningPoints", "showPOIs", "showSegments", "onRouteSelect", "onSegmentSelect", "onPOISelect", "height", "_s4", "mapStyle", "setMapStyle", "setIsFullscreen", "mapInstance", "setMapInstance", "mapContainerRef", "handleLocate", "<PERSON><PERSON><PERSON><PERSON>", "lat", "lng", "animate", "duration", "handleFullscreen", "current", "_mapContainerRef$curr", "_mapContainerRef$curr2", "requestFullscreen", "call", "_document$exitFullscr", "_document", "document", "exitFullscreen", "currentMapStyle", "ref", "width", "zoomControl", "attributionControl", "max<PERSON><PERSON>", "position", "createStravaIcon", "route", "id", "routeColor", "positions", "points", "p", "eventHandlers", "description", "distance", "difficulty", "point", "index", "poi", "rating", "toFixed", "segment", "segmentColor", "elevation", "recordTime", "attempts", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/components/ModernMap.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiLayers,\n  FiMaximize2,\n  FiMinimize2,\n  FiNavigation,\n  FiZoomIn,\n  FiZoomOut,\n  FiMap,\n  FiGlobe,\n  FiMoon,\n  FiTriangle\n} from 'react-icons/fi';\n\n// Fix Leaflet default icon issue\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\n// Modern Strava-like map styles with high quality tiles\nconst mapStyles = {\n  strava: {\n    url: \"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Strava Style\",\n    icon: FiMap\n  },\n  satellite: {\n    url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n    attribution: '&copy; <a href=\"https://www.esri.com/\">Esri</a>',\n    name: \"Satellite\",\n    icon: FiGlobe\n  },\n  dark: {\n    url: \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",\n    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n    name: \"Dark\",\n    icon: FiMoon\n  },\n  terrain: {\n    url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n    attribution: 'Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, <a href=\"http://viewfinderpanoramas.org\">SRTM</a> | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a>',\n    name: \"Terrain\",\n    icon: FiTriangle\n  }\n};\n\n// Modern Strava-style colors with gradients\nconst stravaColors = {\n  orange: '#FC4C02',\n  darkOrange: '#E34402',\n  blue: '#0073E6',\n  darkBlue: '#005BB5',\n  green: '#00D924',\n  darkGreen: '#00A01C',\n  red: '#FF0000',\n  darkRed: '#CC0000',\n  purple: '#8B5CF6',\n  darkPurple: '#7C3AED',\n  yellow: '#FFC107',\n  darkYellow: '#F59E0B',\n  // Activity type colors\n  running: '#FC4C02',\n  cycling: '#0073E6',\n  hiking: '#00D924',\n  swimming: '#06B6D4',\n  // Segment type colors\n  climb: '#EF4444',\n  sprint: '#10B981',\n  descent: '#3B82F6',\n  flat: '#8B5CF6'\n};\n\n// Route difficulty styling\nconst routeDifficultyStyles = {\n  easy: { weight: 4, opacity: 0.8, color: stravaColors.green },\n  moderate: { weight: 5, opacity: 0.85, color: stravaColors.yellow },\n  hard: { weight: 6, opacity: 0.9, color: stravaColors.orange },\n  extreme: { weight: 7, opacity: 0.95, color: stravaColors.red }\n};\n\n// Activity type styling\nconst activityTypeStyles = {\n  running: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.running,\n    dashArray: null\n  },\n  cycling: {\n    weight: 6,\n    opacity: 0.9,\n    color: stravaColors.cycling,\n    dashArray: null\n  },\n  hiking: {\n    weight: 4,\n    opacity: 0.85,\n    color: stravaColors.hiking,\n    dashArray: '8, 4'\n  },\n  swimming: {\n    weight: 5,\n    opacity: 0.9,\n    color: stravaColors.swimming,\n    dashArray: '12, 8'\n  }\n};\n\n// Modern POI icons with detailed styling\nconst createModernPOIIcon = (poiType, isSelected = false) => {\n  const iconMap = {\n    restaurant: '🍽️',\n    cafe: '☕',\n    hotel: '🏨',\n    gas_station: '⛽',\n    hospital: '🏥',\n    pharmacy: '💊',\n    bank: '🏦',\n    atm: '💳',\n    parking: '🅿️',\n    toilet: '🚻',\n    water: '💧',\n    viewpoint: '👁️',\n    monument: '🏛️',\n    park: '🌳',\n    beach: '🏖️',\n    mountain: '⛰️',\n    default: '📍'\n  };\n\n  const size = isSelected ? 44 : 36;\n  const shadowSize = isSelected ? 16 : 12;\n\n  return L.divIcon({\n    html: `\n      <div class=\"modern-poi-marker ${isSelected ? 'selected' : ''}\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.blue} 0%, ${stravaColors.darkBlue} 100%);\n        color: white;\n        border-radius: 50%;\n        width: ${size}px;\n        height: ${size}px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: ${size * 0.4}px;\n        border: 3px solid white;\n        box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0,0,0,0.2),\n                    0 ${shadowSize/2}px ${shadowSize}px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: 600;\n        transform: translateZ(0);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        cursor: pointer;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">\n          ${iconMap[poiType] || iconMap.default}\n        </span>\n      </div>\n    `,\n    className: 'modern-poi-icon',\n    iconSize: [size, size],\n    iconAnchor: [size/2, size/2]\n  });\n};\n\n// Modern segment markers (start/finish)\nconst createSegmentMarker = (type, segmentType = 'general') => {\n  const colors = {\n    climb: stravaColors.red,\n    sprint: stravaColors.green,\n    descent: stravaColors.blue,\n    general: stravaColors.purple\n  };\n\n  const icons = {\n    start: '🚀',\n    finish: '🎯'\n  };\n\n  return L.divIcon({\n    html: `\n      <div class=\"segment-marker ${type}\" style=\"\n        background: linear-gradient(135deg, ${colors[segmentType]} 0%, ${colors[segmentType]}dd 100%);\n        color: white;\n        border-radius: 8px;\n        width: 32px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        border: 2px solid white;\n        box-shadow: 0 6px 20px rgba(0,0,0,0.15),\n                    0 2px 6px rgba(0,0,0,0.1),\n                    inset 0 1px 0 rgba(255,255,255,0.2);\n        font-weight: bold;\n        transform: translateZ(0);\n        transition: all 0.2s ease;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));\">\n          ${icons[type]}\n        </span>\n      </div>\n    `,\n    className: 'segment-marker-icon',\n    iconSize: [32, 32],\n    iconAnchor: [16, 16]\n  });\n};\n\n// User location icon\nconst createUserLocationIcon = () => {\n  return L.divIcon({\n    html: `\n      <div class=\"user-location-marker\" style=\"\n        background: linear-gradient(135deg, ${stravaColors.orange} 0%, ${stravaColors.darkOrange} 100%);\n        color: white;\n        border-radius: 50%;\n        width: 40px;\n        height: 40px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 16px;\n        border: 4px solid white;\n        box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),\n                    0 3px 10px rgba(0,0,0,0.2),\n                    inset 0 1px 0 rgba(255,255,255,0.3);\n        font-weight: bold;\n        transform: translateZ(0);\n        animation: pulse 2s infinite;\n        position: relative;\n      \">\n        <span style=\"filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));\">📍</span>\n      </div>\n    `,\n    className: 'user-location-icon',\n    iconSize: [40, 40],\n    iconAnchor: [20, 20]\n  });\n};\n\n// Strava-style map controls\nconst StravaMapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {\n  const [showStyleSelector, setShowStyleSelector] = useState(false);\n\n  return (\n    <div className=\"absolute top-4 right-4 z-[1000] space-y-3\">\n      {/* Style Selector with Strava design */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowStyleSelector(!showStyleSelector)}\n          className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl\"\n          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n          title=\"Change map style\"\n        >\n          <FiLayers className=\"h-5 w-5 text-gray-700\" />\n        </button>\n\n        {showStyleSelector && (\n          <div className=\"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0\"\n               style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}>\n            {Object.entries(mapStyles).map(([key, style]) => {\n              const IconComponent = style.icon;\n              return (\n                <button\n                  key={key}\n                  onClick={() => {\n                    onStyleChange(key);\n                    setShowStyleSelector(false);\n                  }}\n                  className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${\n                    currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'\n                  }`}\n                  style={currentStyle === key ? { backgroundColor: '#FFF7ED', color: stravaColors.orange } : {}}\n                >\n                  <IconComponent className=\"h-4 w-4\" />\n                  <span>{style.name}</span>\n                </button>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Locate button with Strava style */}\n      <button\n        onClick={onLocate}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title=\"Center on your location\"\n      >\n        <FiNavigation className=\"h-5 w-5 text-gray-700\" />\n      </button>\n\n      {/* Fullscreen button with Strava style */}\n      <button\n        onClick={onFullscreen}\n        className=\"bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block\"\n        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}\n        title={isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n      >\n        {isFullscreen ? (\n          <FiMinimize2 className=\"h-5 w-5 text-gray-700\" />\n        ) : (\n          <FiMaximize2 className=\"h-5 w-5 text-gray-700\" />\n        )}\n      </button>\n    </div>\n  );\n};\n\n// Map event handler\nconst MapEventHandler = ({ onMapClick, onMapReady }) => {\n  const map = useMap();\n\n  useMapEvents({\n    click: onMapClick,\n    ready: () => {\n      onMapReady && onMapReady(map);\n    }\n  });\n\n  return null;\n};\n\n// Strava-style zoom controls\nconst StravaZoomControl = () => {\n  const map = useMap();\n\n  return (\n    <div className=\"absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg\"\n         style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>\n      <button\n        onClick={() => map.zoomIn()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100\"\n        title=\"Zoom in\"\n      >\n        <FiZoomIn className=\"h-5 w-5 text-gray-700\" />\n      </button>\n      <button\n        onClick={() => map.zoomOut()}\n        className=\"p-3 hover:bg-gray-50 transition-all duration-150\"\n        title=\"Zoom out\"\n      >\n        <FiZoomOut className=\"h-5 w-5 text-gray-700\" />\n      </button>\n    </div>\n  );\n};\n\nconst ModernMap = ({\n  center,\n  zoom = 13,\n  userPosition,\n  routes = [],\n  segments = [],\n  pois = [],\n  selectedRoute,\n  selectedSegment,\n  selectedPOIs = [],\n  optimizedRoute,\n  planningPoints = [],\n  showPOIs = true,\n  showSegments = false,\n  onMapClick,\n  onRouteSelect,\n  onSegmentSelect,\n  onPOISelect,\n  className = \"\",\n  height = \"h-96 lg:h-[600px]\"\n}) => {\n  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapInstance, setMapInstance] = useState(null);\n  const mapContainerRef = useRef(null);\n\n  const handleLocate = () => {\n    if (mapInstance && userPosition) {\n      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {\n        animate: true,\n        duration: 1\n      });\n    }\n  };\n\n  const handleFullscreen = () => {\n    if (mapContainerRef.current) {\n      if (!isFullscreen) {\n        mapContainerRef.current.requestFullscreen?.();\n      } else {\n        document.exitFullscreen?.();\n      }\n      setIsFullscreen(!isFullscreen);\n    }\n  };\n\n  const currentMapStyle = mapStyles[mapStyle];\n\n  return (\n    <div\n      ref={mapContainerRef}\n      className={`relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`}\n      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}\n    >\n      <div className={`${height} relative`}>\n        <MapContainer\n          center={center}\n          zoom={zoom}\n          style={{ height: '100%', width: '100%' }}\n          zoomControl={false}\n          attributionControl={false}\n          className=\"rounded-xl\"\n        >\n          <TileLayer\n            url={currentMapStyle.url}\n            attribution={currentMapStyle.attribution}\n            maxZoom={18}\n          />\n\n          <MapEventHandler\n            onMapClick={onMapClick}\n            onMapReady={setMapInstance}\n          />\n\n          <StravaZoomControl />\n          \n          {/* User position marker with Strava style */}\n          {userPosition && (\n            <Marker\n              position={[userPosition.lat, userPosition.lng]}\n              icon={createStravaIcon('user', stravaColors.blue)}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"text-center p-2\">\n                  <strong className=\"text-gray-900\">Your Location</strong>\n                  <div className=\"text-sm text-gray-600 mt-1\">Current position</div>\n                </div>\n              </Popup>\n            </Marker>\n          )}\n\n          {/* Routes with Strava colors */}\n          {routes.map(route => {\n            const isSelected = selectedRoute?.id === route.id;\n            const routeColor = isSelected ? stravaColors.orange : stravaColors.blue;\n\n            return (\n              <Polyline\n                key={route.id}\n                positions={route.points.map(p => [p.lat, p.lng])}\n                color={routeColor}\n                weight={isSelected ? 6 : 4}\n                opacity={isSelected ? 1 : 0.7}\n                eventHandlers={{\n                  click: () => onRouteSelect && onRouteSelect(route)\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div className=\"p-3\">\n                    <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: stravaColors.orange }}>\n                      {route.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mb-3\">{route.description}</p>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Distance</div>\n                        <div className=\"text-gray-900\">{route.distance}km</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Difficulty</div>\n                        <div className=\"text-gray-900\">{route.difficulty}</div>\n                      </div>\n                    </div>\n                  </div>\n                </Popup>\n              </Polyline>\n            );\n          })}\n\n          {/* Selected route highlight with Strava orange */}\n          {selectedRoute && (\n            <Polyline\n              positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n              color={stravaColors.orange}\n              weight={6}\n              opacity={1}\n            />\n          )}\n\n          {/* Optimized route with Strava green */}\n          {optimizedRoute && (\n            <Polyline\n              positions={optimizedRoute.map(p => [p.lat, p.lng])}\n              color={stravaColors.green}\n              weight={5}\n              opacity={0.9}\n              dashArray=\"8, 8\"\n            />\n          )}\n\n          {/* Planning points with Strava style */}\n          {planningPoints.map((point, index) => (\n            <Marker\n              key={index}\n              position={[point.lat, point.lng]}\n              icon={createStravaIcon('poi', stravaColors.yellow)}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"text-center p-2\">\n                  <strong className=\"text-gray-900\">Point {index + 1}</strong>\n                  <div className=\"text-sm text-gray-600 mt-1\">Planning waypoint</div>\n                </div>\n              </Popup>\n            </Marker>\n          ))}\n\n          {/* POIs with Strava style */}\n          {showPOIs && pois.map(poi => (\n            <Marker\n              key={poi.id}\n              position={[poi.lat, poi.lng]}\n              icon={createStravaIcon('poi', stravaColors.green)}\n              eventHandlers={{\n                click: () => onPOISelect && onPOISelect(poi)\n              }}\n            >\n              <Popup className=\"strava-popup\">\n                <div className=\"p-3\">\n                  <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: stravaColors.green }}>\n                    {poi.name}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 mb-2\">{poi.type}</p>\n                  {poi.rating && (\n                    <div className=\"flex items-center bg-yellow-50 p-2 rounded\">\n                      <span className=\"text-yellow-500\">⭐</span>\n                      <span className=\"text-sm ml-1 font-medium\">{poi.rating.toFixed(1)}</span>\n                    </div>\n                  )}\n                </div>\n              </Popup>\n            </Marker>\n          ))}\n\n          {/* Selected POIs highlight */}\n          {selectedPOIs.map(poi => (\n            <Marker\n              key={`selected-${poi.id}`}\n              position={[poi.lat, poi.lng]}\n              icon={createStravaIcon('poi', stravaColors.red)}\n            />\n          ))}\n\n          {/* Segments with Strava colors */}\n          {(showSegments || selectedSegment) && segments.map(segment => {\n            const isSelected = selectedSegment?.id === segment.id;\n            const segmentColor = isSelected ? stravaColors.orange :\n                               segment.type === 'climb' ? stravaColors.red :\n                               segment.type === 'sprint' ? stravaColors.green :\n                               segment.type === 'descent' ? stravaColors.blue :\n                               stravaColors.purple;\n\n            return (\n              <Polyline\n                key={segment.id}\n                positions={segment.points.map(point => [point.lat, point.lng])}\n                color={segmentColor}\n                weight={isSelected ? 7 : 5}\n                opacity={isSelected ? 1 : 0.8}\n                eventHandlers={{\n                  click: () => onSegmentSelect && onSegmentSelect(segment)\n                }}\n              >\n                <Popup className=\"strava-popup\">\n                  <div className=\"p-3\">\n                    <h3 className=\"font-bold text-gray-900 mb-2\" style={{ color: segmentColor }}>\n                      {segment.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mb-3\">{segment.description}</p>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Distance</div>\n                        <div className=\"text-gray-900\">\n                          {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}\n                        </div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Elevation</div>\n                        <div className=\"text-gray-900\">\n                          {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}\n                        </div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Record</div>\n                        <div className=\"text-gray-900\">{segment.recordTime}</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-2 rounded\">\n                        <div className=\"font-medium text-gray-700\">Attempts</div>\n                        <div className=\"text-gray-900\">{segment.attempts}</div>\n                      </div>\n                    </div>\n                  </div>\n                </Popup>\n              </Polyline>\n            );\n          })}\n        </MapContainer>\n\n        {/* Strava-style controls overlay */}\n        <StravaMapControls\n          onStyleChange={setMapStyle}\n          currentStyle={mapStyle}\n          onLocate={handleLocate}\n          onFullscreen={handleFullscreen}\n          isFullscreen={isFullscreen}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ModernMap;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AACtG,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,gBAAgB;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOZ,CAAC,CAACa,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3ChB,CAAC,CAACa,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE;IACNC,GAAG,EAAE,0EAA0E;IAC/EC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEnB;EACR,CAAC;EACDoB,SAAS,EAAE;IACTJ,GAAG,EAAE,+FAA+F;IACpGC,WAAW,EAAE,iDAAiD;IAC9DC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAElB;EACR,CAAC;EACDoB,IAAI,EAAE;IACJL,GAAG,EAAE,+DAA+D;IACpEC,WAAW,EAAE,mJAAmJ;IAChKC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAEjB;EACR,CAAC;EACDoB,OAAO,EAAE;IACPN,GAAG,EAAE,kDAAkD;IACvDC,WAAW,EAAE,4NAA4N;IACzOC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAEhB;EACR;AACF,CAAC;;AAED;AACA,MAAMoB,YAAY,GAAG;EACnBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE,SAAS;EACdC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrB;EACAC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,SAAS;EACnB;EACAC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,MAAMC,qBAAqB,GAAG;EAC5BC,IAAI,EAAE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,KAAK,EAAEzB,YAAY,CAACK;EAAM,CAAC;EAC5DqB,QAAQ,EAAE;IAAEH,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAEzB,YAAY,CAACW;EAAO,CAAC;EAClEgB,IAAI,EAAE;IAAEJ,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,KAAK,EAAEzB,YAAY,CAACC;EAAO,CAAC;EAC7D2B,OAAO,EAAE;IAAEL,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAEzB,YAAY,CAACO;EAAI;AAC/D,CAAC;;AAED;AACA,MAAMsB,kBAAkB,GAAG;EACzBhB,OAAO,EAAE;IACPU,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACa,OAAO;IAC3BiB,SAAS,EAAE;EACb,CAAC;EACDhB,OAAO,EAAE;IACPS,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACc,OAAO;IAC3BgB,SAAS,EAAE;EACb,CAAC;EACDf,MAAM,EAAE;IACNQ,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAEzB,YAAY,CAACe,MAAM;IAC1Be,SAAS,EAAE;EACb,CAAC;EACDd,QAAQ,EAAE;IACRO,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAEzB,YAAY,CAACgB,QAAQ;IAC5Bc,SAAS,EAAE;EACb;AACF,CAAC;;AAED;AACA,MAAMC,mBAAmB,GAAGA,CAACC,OAAO,EAAEC,UAAU,GAAG,KAAK,KAAK;EAC3D,MAAMC,OAAO,GAAG;IACdC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,GAAG;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,IAAI,GAAGnB,UAAU,GAAG,EAAE,GAAG,EAAE;EACjC,MAAMoB,UAAU,GAAGpB,UAAU,GAAG,EAAE,GAAG,EAAE;EAEvC,OAAO/D,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV,sCAAsCtB,UAAU,GAAG,UAAU,GAAG,EAAE;AAClE,8CAA8CjC,YAAY,CAACG,IAAI,QAAQH,YAAY,CAACI,QAAQ;AAC5F;AACA;AACA,iBAAiBgD,IAAI;AACrB,kBAAkBA,IAAI;AACtB;AACA;AACA;AACA,qBAAqBA,IAAI,GAAG,GAAG;AAC/B;AACA,wBAAwBC,UAAU,MAAMA,UAAU,GAAG,CAAC;AACtD,wBAAwBA,UAAU,GAAC,CAAC,MAAMA,UAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYnB,OAAO,CAACF,OAAO,CAAC,IAAIE,OAAO,CAACiB,OAAO;AAC/C;AACA;AACA,KAAK;IACDK,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAE,CAACL,IAAI,EAAEA,IAAI,CAAC;IACtBM,UAAU,EAAE,CAACN,IAAI,GAAC,CAAC,EAAEA,IAAI,GAAC,CAAC;EAC7B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMO,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,WAAW,GAAG,SAAS,KAAK;EAC7D,MAAMC,MAAM,GAAG;IACb7C,KAAK,EAAEjB,YAAY,CAACO,GAAG;IACvBW,MAAM,EAAElB,YAAY,CAACK,KAAK;IAC1Bc,OAAO,EAAEnB,YAAY,CAACG,IAAI;IAC1B4D,OAAO,EAAE/D,YAAY,CAACS;EACxB,CAAC;EAED,MAAMuD,KAAK,GAAG;IACZC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC;EAED,OAAOhG,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV,mCAAmCK,IAAI;AACvC,8CAA8CE,MAAM,CAACD,WAAW,CAAC,QAAQC,MAAM,CAACD,WAAW,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYG,KAAK,CAACJ,IAAI,CAAC;AACvB;AACA;AACA,KAAK;IACDJ,SAAS,EAAE,qBAAqB;IAChCC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;EACnC,OAAOjG,CAAC,CAACoF,OAAO,CAAC;IACfC,IAAI,EAAE;AACV;AACA,8CAA8CvD,YAAY,CAACC,MAAM,QAAQD,YAAY,CAACE,UAAU;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDsD,SAAS,EAAE,oBAAoB;IAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMU,iBAAiB,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACnG,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnH,QAAQ,CAAC,KAAK,CAAC;EAEjE,oBACEqB,OAAA;IAAK0E,SAAS,EAAC,2CAA2C;IAAAqB,QAAA,gBAExD/F,OAAA;MAAK0E,SAAS,EAAC,UAAU;MAAAqB,QAAA,gBACvB/F,OAAA;QACEgG,OAAO,EAAEA,CAAA,KAAMF,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;QACxDnB,SAAS,EAAC,yGAAyG;QACnHuB,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QACpDC,KAAK,EAAC,kBAAkB;QAAAJ,QAAA,eAExB/F,OAAA,CAACX,QAAQ;UAACqF,SAAS,EAAC;QAAuB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAERV,iBAAiB,iBAChB7F,OAAA;QAAK0E,SAAS,EAAC,sGAAsG;QAChHuB,KAAK,EAAE;UAAEC,SAAS,EAAE;QAA8B,CAAE;QAAAH,QAAA,EACtDS,MAAM,CAACC,OAAO,CAAChG,SAAS,CAAC,CAACiG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEV,KAAK,CAAC,KAAK;UAC/C,MAAMW,aAAa,GAAGX,KAAK,CAACnF,IAAI;UAChC,oBACEd,OAAA;YAEEgG,OAAO,EAAEA,CAAA,KAAM;cACbT,aAAa,CAACoB,GAAG,CAAC;cAClBb,oBAAoB,CAAC,KAAK,CAAC;YAC7B,CAAE;YACFpB,SAAS,EAAE,+GACTc,YAAY,KAAKmB,GAAG,GAAG,0CAA0C,GAAG,eAAe,EAClF;YACHV,KAAK,EAAET,YAAY,KAAKmB,GAAG,GAAG;cAAEE,eAAe,EAAE,SAAS;cAAElE,KAAK,EAAEzB,YAAY,CAACC;YAAO,CAAC,GAAG,CAAC,CAAE;YAAA4E,QAAA,gBAE9F/F,OAAA,CAAC4G,aAAa;cAAClC,SAAS,EAAC;YAAS;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCvG,OAAA;cAAA+F,QAAA,EAAOE,KAAK,CAACpF;YAAI;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAXpBI,GAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYF,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvG,OAAA;MACEgG,OAAO,EAAEP,QAAS;MAClBf,SAAS,EAAC,+GAA+G;MACzHuB,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDC,KAAK,EAAC,yBAAyB;MAAAJ,QAAA,eAE/B/F,OAAA,CAACR,YAAY;QAACkF,SAAS,EAAC;MAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAGTvG,OAAA;MACEgG,OAAO,EAAEN,YAAa;MACtBhB,SAAS,EAAC,+GAA+G;MACzHuB,KAAK,EAAE;QAAEC,SAAS,EAAE;MAA8B,CAAE;MACpDC,KAAK,EAAER,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;MAAAI,QAAA,EAE5DJ,YAAY,gBACX3F,OAAA,CAACT,WAAW;QAACmF,SAAS,EAAC;MAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEjDvG,OAAA,CAACV,WAAW;QAACoF,SAAS,EAAC;MAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAX,EAAA,CArEMN,iBAAiB;AAAAwB,EAAA,GAAjBxB,iBAAiB;AAsEvB,MAAMyB,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAW,CAAC,KAAK;EAAAC,GAAA;EACtD,MAAMR,GAAG,GAAGvH,MAAM,CAAC,CAAC;EAEpBD,YAAY,CAAC;IACXiI,KAAK,EAAEH,UAAU;IACjBI,KAAK,EAAEA,CAAA,KAAM;MACXH,UAAU,IAAIA,UAAU,CAACP,GAAG,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,OAAO,IAAI;AACb,CAAC;;AAED;AAAAQ,GAAA,CAbMH,eAAe;EAAA,QACP5H,MAAM,EAElBD,YAAY;AAAA;AAAAmI,GAAA,GAHRN,eAAe;AAcrB,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAMb,GAAG,GAAGvH,MAAM,CAAC,CAAC;EAEpB,oBACEa,OAAA;IAAK0E,SAAS,EAAC,gGAAgG;IAC1GuB,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,gBACvD/F,OAAA;MACEgG,OAAO,EAAEA,CAAA,KAAMU,GAAG,CAACc,MAAM,CAAC,CAAE;MAC5B9C,SAAS,EAAC,2EAA2E;MACrFyB,KAAK,EAAC,SAAS;MAAAJ,QAAA,eAEf/F,OAAA,CAACP,QAAQ;QAACiF,SAAS,EAAC;MAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eACTvG,OAAA;MACEgG,OAAO,EAAEA,CAAA,KAAMU,GAAG,CAACe,OAAO,CAAC,CAAE;MAC7B/C,SAAS,EAAC,kDAAkD;MAC5DyB,KAAK,EAAC,UAAU;MAAAJ,QAAA,eAEhB/F,OAAA,CAACN,SAAS;QAACgF,SAAS,EAAC;MAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACgB,GAAA,CAtBID,iBAAiB;EAAA,QACTnI,MAAM;AAAA;AAAAuI,GAAA,GADdJ,iBAAiB;AAwBvB,MAAMK,SAAS,GAAGA,CAAC;EACjBC,MAAM;EACNC,IAAI,GAAG,EAAE;EACTC,YAAY;EACZC,MAAM,GAAG,EAAE;EACXC,QAAQ,GAAG,EAAE;EACbC,IAAI,GAAG,EAAE;EACTC,aAAa;EACbC,eAAe;EACfC,YAAY,GAAG,EAAE;EACjBC,cAAc;EACdC,cAAc,GAAG,EAAE;EACnBC,QAAQ,GAAG,IAAI;EACfC,YAAY,GAAG,KAAK;EACpBxB,UAAU;EACVyB,aAAa;EACbC,eAAe;EACfC,WAAW;EACXjE,SAAS,GAAG,EAAE;EACdkE,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgH,YAAY,EAAEqD,eAAe,CAAC,GAAGrK,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsK,WAAW,EAAEC,cAAc,CAAC,GAAGvK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMwK,eAAe,GAAGvK,MAAM,CAAC,IAAI,CAAC;EAEpC,MAAMwK,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,WAAW,IAAInB,YAAY,EAAE;MAC/BmB,WAAW,CAACI,OAAO,CAAC,CAACvB,YAAY,CAACwB,GAAG,EAAExB,YAAY,CAACyB,GAAG,CAAC,EAAE,EAAE,EAAE;QAC5DC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,eAAe,CAACQ,OAAO,EAAE;MAC3B,IAAI,CAAChE,YAAY,EAAE;QAAA,IAAAiE,qBAAA,EAAAC,sBAAA;QACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAV,eAAe,CAACQ,OAAO,EAACG,iBAAiB,cAAAF,qBAAA,uBAAzCA,qBAAA,CAAAG,IAAA,CAAAF,sBAA4C,CAAC;MAC/C,CAAC,MAAM;QAAA,IAAAG,qBAAA,EAAAC,SAAA;QACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAC,QAAQ,EAACC,cAAc,cAAAH,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC7B;MACAjB,eAAe,CAAC,CAACrD,YAAY,CAAC;IAChC;EACF,CAAC;EAED,MAAMyE,eAAe,GAAG3J,SAAS,CAACqI,QAAQ,CAAC;EAE3C,oBACE9I,OAAA;IACEqK,GAAG,EAAElB,eAAgB;IACrBzE,SAAS,EAAE,0DAA0DA,SAAS,EAAG;IACjFuB,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA8B,CAAE;IAAAH,QAAA,eAEpD/F,OAAA;MAAK0E,SAAS,EAAE,GAAGkE,MAAM,WAAY;MAAA7C,QAAA,gBACnC/F,OAAA,CAACnB,YAAY;QACX+I,MAAM,EAAEA,MAAO;QACfC,IAAI,EAAEA,IAAK;QACX5B,KAAK,EAAE;UAAE2C,MAAM,EAAE,MAAM;UAAE0B,KAAK,EAAE;QAAO,CAAE;QACzCC,WAAW,EAAE,KAAM;QACnBC,kBAAkB,EAAE,KAAM;QAC1B9F,SAAS,EAAC,YAAY;QAAAqB,QAAA,gBAEtB/F,OAAA,CAAClB,SAAS;UACR6B,GAAG,EAAEyJ,eAAe,CAACzJ,GAAI;UACzBC,WAAW,EAAEwJ,eAAe,CAACxJ,WAAY;UACzC6J,OAAO,EAAE;QAAG;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEFvG,OAAA,CAAC+G,eAAe;UACdC,UAAU,EAAEA,UAAW;UACvBC,UAAU,EAAEiC;QAAe;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEFvG,OAAA,CAACsH,iBAAiB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGpBuB,YAAY,iBACX9H,OAAA,CAACjB,MAAM;UACL2L,QAAQ,EAAE,CAAC5C,YAAY,CAACwB,GAAG,EAAExB,YAAY,CAACyB,GAAG,CAAE;UAC/CzI,IAAI,EAAE6J,gBAAgB,CAAC,MAAM,EAAEzJ,YAAY,CAACG,IAAI,CAAE;UAAA0E,QAAA,eAElD/F,OAAA,CAAChB,KAAK;YAAC0F,SAAS,EAAC,cAAc;YAAAqB,QAAA,eAC7B/F,OAAA;cAAK0E,SAAS,EAAC,iBAAiB;cAAAqB,QAAA,gBAC9B/F,OAAA;gBAAQ0E,SAAS,EAAC,eAAe;gBAAAqB,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxDvG,OAAA;gBAAK0E,SAAS,EAAC,4BAA4B;gBAAAqB,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACT,EAGAwB,MAAM,CAACrB,GAAG,CAACkE,KAAK,IAAI;UACnB,MAAMzH,UAAU,GAAG,CAAA+E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2C,EAAE,MAAKD,KAAK,CAACC,EAAE;UACjD,MAAMC,UAAU,GAAG3H,UAAU,GAAGjC,YAAY,CAACC,MAAM,GAAGD,YAAY,CAACG,IAAI;UAEvE,oBACErB,OAAA,CAACf,QAAQ;YAEP8L,SAAS,EAAEH,KAAK,CAACI,MAAM,CAACtE,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;YACjD5G,KAAK,EAAEmI,UAAW;YAClBrI,MAAM,EAAEU,UAAU,GAAG,CAAC,GAAG,CAAE;YAC3BT,OAAO,EAAES,UAAU,GAAG,CAAC,GAAG,GAAI;YAC9B+H,aAAa,EAAE;cACb/D,KAAK,EAAEA,CAAA,KAAMsB,aAAa,IAAIA,aAAa,CAACmC,KAAK;YACnD,CAAE;YAAA7E,QAAA,eAEF/F,OAAA,CAAChB,KAAK;cAAC0F,SAAS,EAAC,cAAc;cAAAqB,QAAA,eAC7B/F,OAAA;gBAAK0E,SAAS,EAAC,KAAK;gBAAAqB,QAAA,gBAClB/F,OAAA;kBAAI0E,SAAS,EAAC,8BAA8B;kBAACuB,KAAK,EAAE;oBAAEtD,KAAK,EAAEzB,YAAY,CAACC;kBAAO,CAAE;kBAAA4E,QAAA,EAChF6E,KAAK,CAAC/J;gBAAI;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACLvG,OAAA;kBAAG0E,SAAS,EAAC,4BAA4B;kBAAAqB,QAAA,EAAE6E,KAAK,CAACO;gBAAW;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjEvG,OAAA;kBAAK0E,SAAS,EAAC,gCAAgC;kBAAAqB,QAAA,gBAC7C/F,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,GAAE6E,KAAK,CAACQ,QAAQ,EAAC,IAAE;oBAAA;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACNvG,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3DvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,EAAE6E,KAAK,CAACS;oBAAU;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GA1BHqE,KAAK,CAACC,EAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BL,CAAC;QAEf,CAAC,CAAC,EAGD2B,aAAa,iBACZlI,OAAA,CAACf,QAAQ;UACP8L,SAAS,EAAE7C,aAAa,CAAC8C,MAAM,CAACtE,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;UACzD5G,KAAK,EAAEzB,YAAY,CAACC,MAAO;UAC3BsB,MAAM,EAAE,CAAE;UACVC,OAAO,EAAE;QAAE;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACF,EAGA8B,cAAc,iBACbrI,OAAA,CAACf,QAAQ;UACP8L,SAAS,EAAE1C,cAAc,CAAC3B,GAAG,CAACuE,CAAC,IAAI,CAACA,CAAC,CAAC3B,GAAG,EAAE2B,CAAC,CAAC1B,GAAG,CAAC,CAAE;UACnD5G,KAAK,EAAEzB,YAAY,CAACK,KAAM;UAC1BkB,MAAM,EAAE,CAAE;UACVC,OAAO,EAAE,GAAI;UACbM,SAAS,EAAC;QAAM;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACF,EAGA+B,cAAc,CAAC5B,GAAG,CAAC,CAAC4E,KAAK,EAAEC,KAAK,kBAC/BvL,OAAA,CAACjB,MAAM;UAEL2L,QAAQ,EAAE,CAACY,KAAK,CAAChC,GAAG,EAAEgC,KAAK,CAAC/B,GAAG,CAAE;UACjCzI,IAAI,EAAE6J,gBAAgB,CAAC,KAAK,EAAEzJ,YAAY,CAACW,MAAM,CAAE;UAAAkE,QAAA,eAEnD/F,OAAA,CAAChB,KAAK;YAAC0F,SAAS,EAAC,cAAc;YAAAqB,QAAA,eAC7B/F,OAAA;cAAK0E,SAAS,EAAC,iBAAiB;cAAAqB,QAAA,gBAC9B/F,OAAA;gBAAQ0E,SAAS,EAAC,eAAe;gBAAAqB,QAAA,GAAC,QAAM,EAACwF,KAAK,GAAG,CAAC;cAAA;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC5DvG,OAAA;gBAAK0E,SAAS,EAAC,4BAA4B;gBAAAqB,QAAA,EAAC;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GATHgF,KAAK;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUJ,CACT,CAAC,EAGDgC,QAAQ,IAAIN,IAAI,CAACvB,GAAG,CAAC8E,GAAG,iBACvBxL,OAAA,CAACjB,MAAM;UAEL2L,QAAQ,EAAE,CAACc,GAAG,CAAClC,GAAG,EAAEkC,GAAG,CAACjC,GAAG,CAAE;UAC7BzI,IAAI,EAAE6J,gBAAgB,CAAC,KAAK,EAAEzJ,YAAY,CAACK,KAAK,CAAE;UAClD2J,aAAa,EAAE;YACb/D,KAAK,EAAEA,CAAA,KAAMwB,WAAW,IAAIA,WAAW,CAAC6C,GAAG;UAC7C,CAAE;UAAAzF,QAAA,eAEF/F,OAAA,CAAChB,KAAK;YAAC0F,SAAS,EAAC,cAAc;YAAAqB,QAAA,eAC7B/F,OAAA;cAAK0E,SAAS,EAAC,KAAK;cAAAqB,QAAA,gBAClB/F,OAAA;gBAAI0E,SAAS,EAAC,8BAA8B;gBAACuB,KAAK,EAAE;kBAAEtD,KAAK,EAAEzB,YAAY,CAACK;gBAAM,CAAE;gBAAAwE,QAAA,EAC/EyF,GAAG,CAAC3K;cAAI;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACLvG,OAAA;gBAAG0E,SAAS,EAAC,4BAA4B;gBAAAqB,QAAA,EAAEyF,GAAG,CAAC1G;cAAI;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACvDiF,GAAG,CAACC,MAAM,iBACTzL,OAAA;gBAAK0E,SAAS,EAAC,4CAA4C;gBAAAqB,QAAA,gBACzD/F,OAAA;kBAAM0E,SAAS,EAAC,iBAAiB;kBAAAqB,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1CvG,OAAA;kBAAM0E,SAAS,EAAC,0BAA0B;kBAAAqB,QAAA,EAAEyF,GAAG,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC;gBAAC;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GApBHiF,GAAG,CAACX,EAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBL,CACT,CAAC,EAGD6B,YAAY,CAAC1B,GAAG,CAAC8E,GAAG,iBACnBxL,OAAA,CAACjB,MAAM;UAEL2L,QAAQ,EAAE,CAACc,GAAG,CAAClC,GAAG,EAAEkC,GAAG,CAACjC,GAAG,CAAE;UAC7BzI,IAAI,EAAE6J,gBAAgB,CAAC,KAAK,EAAEzJ,YAAY,CAACO,GAAG;QAAE,GAF3C,YAAY+J,GAAG,CAACX,EAAE,EAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAG1B,CACF,CAAC,EAGD,CAACiC,YAAY,IAAIL,eAAe,KAAKH,QAAQ,CAACtB,GAAG,CAACiF,OAAO,IAAI;UAC5D,MAAMxI,UAAU,GAAG,CAAAgF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0C,EAAE,MAAKc,OAAO,CAACd,EAAE;UACrD,MAAMe,YAAY,GAAGzI,UAAU,GAAGjC,YAAY,CAACC,MAAM,GAClCwK,OAAO,CAAC7G,IAAI,KAAK,OAAO,GAAG5D,YAAY,CAACO,GAAG,GAC3CkK,OAAO,CAAC7G,IAAI,KAAK,QAAQ,GAAG5D,YAAY,CAACK,KAAK,GAC9CoK,OAAO,CAAC7G,IAAI,KAAK,SAAS,GAAG5D,YAAY,CAACG,IAAI,GAC9CH,YAAY,CAACS,MAAM;UAEtC,oBACE3B,OAAA,CAACf,QAAQ;YAEP8L,SAAS,EAAEY,OAAO,CAACX,MAAM,CAACtE,GAAG,CAAC4E,KAAK,IAAI,CAACA,KAAK,CAAChC,GAAG,EAAEgC,KAAK,CAAC/B,GAAG,CAAC,CAAE;YAC/D5G,KAAK,EAAEiJ,YAAa;YACpBnJ,MAAM,EAAEU,UAAU,GAAG,CAAC,GAAG,CAAE;YAC3BT,OAAO,EAAES,UAAU,GAAG,CAAC,GAAG,GAAI;YAC9B+H,aAAa,EAAE;cACb/D,KAAK,EAAEA,CAAA,KAAMuB,eAAe,IAAIA,eAAe,CAACiD,OAAO;YACzD,CAAE;YAAA5F,QAAA,eAEF/F,OAAA,CAAChB,KAAK;cAAC0F,SAAS,EAAC,cAAc;cAAAqB,QAAA,eAC7B/F,OAAA;gBAAK0E,SAAS,EAAC,KAAK;gBAAAqB,QAAA,gBAClB/F,OAAA;kBAAI0E,SAAS,EAAC,8BAA8B;kBAACuB,KAAK,EAAE;oBAAEtD,KAAK,EAAEiJ;kBAAa,CAAE;kBAAA7F,QAAA,EACzE4F,OAAO,CAAC9K;gBAAI;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACLvG,OAAA;kBAAG0E,SAAS,EAAC,4BAA4B;kBAAAqB,QAAA,EAAE4F,OAAO,CAACR;gBAAW;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEvG,OAAA;kBAAK0E,SAAS,EAAC,gCAAgC;kBAAAqB,QAAA,gBAC7C/F,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,EAC3B4F,OAAO,CAACP,QAAQ,GAAG,IAAI,GAAG,GAAGO,OAAO,CAACP,QAAQ,GAAG,GAAG,GAAG,CAACO,OAAO,CAACP,QAAQ,GAAG,IAAI,EAAEM,OAAO,CAAC,CAAC,CAAC;oBAAI;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvG,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1DvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,EAC3B4F,OAAO,CAACE,SAAS,GAAG,CAAC,GAAG,IAAIF,OAAO,CAACE,SAAS,GAAG,GAAG,GAAGF,OAAO,CAACE,SAAS;oBAAG;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvG,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,EAAE4F,OAAO,CAACG;oBAAU;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNvG,OAAA;oBAAK0E,SAAS,EAAC,wBAAwB;oBAAAqB,QAAA,gBACrC/F,OAAA;sBAAK0E,SAAS,EAAC,2BAA2B;sBAAAqB,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDvG,OAAA;sBAAK0E,SAAS,EAAC,eAAe;sBAAAqB,QAAA,EAAE4F,OAAO,CAACI;oBAAQ;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAtCHoF,OAAO,CAACd,EAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCP,CAAC;QAEf,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eAGfvG,OAAA,CAACsF,iBAAiB;QAChBC,aAAa,EAAEwD,WAAY;QAC3BvD,YAAY,EAAEsD,QAAS;QACvBrD,QAAQ,EAAE2D,YAAa;QACvB1D,YAAY,EAAEgE,gBAAiB;QAC/B/D,YAAY,EAAEA;MAAa;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsC,GAAA,CA5QIlB,SAAS;AAAAqE,GAAA,GAATrE,SAAS;AA8Qf,eAAeA,SAAS;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAsE,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}