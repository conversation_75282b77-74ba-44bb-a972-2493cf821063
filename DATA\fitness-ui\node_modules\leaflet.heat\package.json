{"name": "leaflet.heat", "version": "0.2.0", "description": "A tiny and fast Leaflet heatmap plugin.", "homepage": "https://github.com/Leaflet/Leaflet.heat", "keywords": ["heatmap", "canvas", "visualization", "gis", "leaflet", "plugin"], "author": "<PERSON>", "repository": {"type": "git", "url": "git://github.com/Leaflet/Leaflet.heat.git"}, "main": "dist/leaflet-heat.js", "devDependencies": {"simpleheat": "~0.2.0", "uglify-js": "~2.4.15"}, "scripts": {"test": "jshint src", "prepublish": "uglifyjs node_modules/simpleheat/simpleheat.js src/HeatLayer.js -c -m --comments /<PERSON>/ -o dist/leaflet-heat.js"}, "jshintConfig": {"quotmark": "single", "globals": {"L": true, "simpleheat": true}, "trailing": true, "camelcase": true, "curly": true, "eqeqeq": true, "noempty": true, "nonbsp": true, "undef": true, "unused": true, "browser": true}}