{"name": "fittracker", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.1", "@tanstack/react-query": "^4.32.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.5.0", "chart.js": "^4.3.0", "date-fns": "^2.30.0", "leaflet": "^1.9.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-icons": "^4.8.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "tailwindcss": "^3.3.2", "uuid": "^9.0.0", "web-vitals": "^2.1.4", "yup": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.23"}}