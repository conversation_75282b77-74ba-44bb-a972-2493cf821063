import React, { useState, useEffect } from 'react';
import {
  FiHeart,
  FiMessageCircle,
  FiShare2,
  FiMoreHorizontal,
  FiMapPin,
  FiClock,
  FiTrendingUp,
  FiZap,
  FiAward,
  FiCamera,
  FiThumbsUp
} from 'react-icons/fi';

const ActivityFeed = ({ activities = [], onActivitySelect }) => {
  const [likedActivities, setLikedActivities] = useState(new Set());
  const [comments, setComments] = useState({});

  const handleLike = (activityId) => {
    setLikedActivities(prev => {
      const newSet = new Set(prev);
      if (newSet.has(activityId)) {
        newSet.delete(activityId);
      } else {
        newSet.add(activityId);
      }
      return newSet;
    });
  };

  const getActivityIcon = (type) => {
    const icons = {
      running: '🏃‍♂️',
      cycling: '🚴‍♂️',
      hiking: '🥾',
      walking: '🚶‍♂️',
      swimming: '🏊‍♂️'
    };
    return icons[type] || '🏃‍♂️';
  };

  const getActivityColor = (type) => {
    const colors = {
      running: 'text-orange-500',
      cycling: 'text-blue-500',
      hiking: 'text-green-500',
      walking: 'text-purple-500',
      swimming: 'text-cyan-500'
    };
    return colors[type] || 'text-orange-500';
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const formatPace = (distance, time) => {
    const paceSeconds = time / distance;
    const minutes = Math.floor(paceSeconds / 60);
    const seconds = Math.floor(paceSeconds % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}/km`;
  };

  // Générer des activités fictives si aucune n'est fournie
  useEffect(() => {
    if (activities.length === 0) {
      // Vous pouvez générer des activités fictives ici
    }
  }, [activities]);

  const mockActivities = [
    {
      id: 1,
      user: {
        name: 'Sophie Martin',
        avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
        verified: true
      },
      type: 'running',
      title: 'Course matinale au Parc',
      description: 'Belle session ce matin ! Le temps était parfait 🌅',
      distance: 8.5,
      time: 2580, // en secondes
      elevation: 120,
      pace: '5:02',
      heartRate: 165,
      calories: 420,
      timestamp: '2024-01-15T07:30:00Z',
      photos: ['https://picsum.photos/400/300?random=1'],
      likes: 12,
      comments: 3,
      achievements: ['PR', 'Top 10%'],
      route: {
        name: 'Parcours du Parc Central',
        segments: 2
      },
      weather: {
        temp: 15,
        condition: 'sunny'
      }
    },
    {
      id: 2,
      user: {
        name: 'Thomas Dubois',
        avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
        verified: false
      },
      type: 'cycling',
      title: 'Sortie vélo en montagne',
      description: 'Défi relevé ! Cette montée était intense 💪',
      distance: 45.2,
      time: 7200,
      elevation: 850,
      pace: '25.1',
      heartRate: 145,
      calories: 1250,
      timestamp: '2024-01-14T14:15:00Z',
      photos: [],
      likes: 8,
      comments: 1,
      achievements: ['KOM'],
      route: {
        name: 'Col de la Croix',
        segments: 5
      },
      weather: {
        temp: 12,
        condition: 'cloudy'
      }
    }
  ];

  const displayActivities = activities.length > 0 ? activities : mockActivities;

  return (
    <div className="space-y-6">
      {displayActivities.map((activity) => (
        <div key={activity.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
          {/* Header */}
          <div className="p-4 border-b border-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <img
                  src={activity.user.avatar}
                  alt={activity.user.name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-900">{activity.user.name}</span>
                    {activity.user.verified && (
                      <span className="text-blue-500">✓</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span className={getActivityColor(activity.type)}>
                      {getActivityIcon(activity.type)}
                    </span>
                    <span>{activity.title}</span>
                    <span>•</span>
                    <span>{new Date(activity.timestamp).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <FiMoreHorizontal className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            {activity.description && (
              <p className="text-gray-700 mb-4">{activity.description}</p>
            )}

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{activity.distance}</div>
                <div className="text-sm text-gray-500">km</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{formatTime(activity.time)}</div>
                <div className="text-sm text-gray-500">temps</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{activity.elevation}</div>
                <div className="text-sm text-gray-500">m D+</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{activity.pace}</div>
                <div className="text-sm text-gray-500">
                  {activity.type === 'cycling' ? 'km/h' : 'min/km'}
                </div>
              </div>
            </div>

            {/* Achievements */}
            {activity.achievements && activity.achievements.length > 0 && (
              <div className="flex items-center space-x-2 mb-4">
                {activity.achievements.map((achievement, index) => (
                  <span
                    key={index}
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      achievement === 'PR' ? 'bg-yellow-100 text-yellow-800' :
                      achievement === 'KOM' ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}
                  >
                    <FiAward className="inline h-3 w-3 mr-1" />
                    {achievement}
                  </span>
                ))}
              </div>
            )}

            {/* Photos */}
            {activity.photos && activity.photos.length > 0 && (
              <div className="mb-4">
                <img
                  src={activity.photos[0]}
                  alt="Activity"
                  className="w-full h-48 object-cover rounded-lg"
                />
              </div>
            )}

            {/* Route Info */}
            {activity.route && (
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FiMapPin className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-gray-900">{activity.route.name}</span>
                  </div>
                  <button 
                    onClick={() => onActivitySelect && onActivitySelect(activity)}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Voir sur la carte
                  </button>
                </div>
                {activity.route.segments && (
                  <div className="text-sm text-gray-500 mt-1">
                    {activity.route.segments} segments
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="px-4 py-3 border-t border-gray-50 flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => handleLike(activity.id)}
                className={`flex items-center space-x-2 transition-colors ${
                  likedActivities.has(activity.id)
                    ? 'text-red-500'
                    : 'text-gray-500 hover:text-red-500'
                }`}
              >
                <FiHeart className={`h-5 w-5 ${likedActivities.has(activity.id) ? 'fill-current' : ''}`} />
                <span className="text-sm">{activity.likes + (likedActivities.has(activity.id) ? 1 : 0)}</span>
              </button>

              <button className="flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors">
                <FiMessageCircle className="h-5 w-5" />
                <span className="text-sm">{activity.comments}</span>
              </button>

              <button className="flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors">
                <FiShare2 className="h-5 w-5" />
                <span className="text-sm">Partager</span>
              </button>
            </div>

            <button className="text-gray-500 hover:text-blue-500 transition-colors">
              <FiThumbsUp className="h-5 w-5" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ActivityFeed;
