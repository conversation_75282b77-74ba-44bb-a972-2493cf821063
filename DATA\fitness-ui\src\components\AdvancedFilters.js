import React, { useState } from 'react';
import {
  FiFilter,
  FiX,
  FiChevronDown,
  FiMapPin,
  FiActivity,
  FiTarget,
  FiClock,
  FiTrendingUp
} from 'react-icons/fi';

const AdvancedFilters = ({ filters, onFiltersChange, onClose }) => {
  const [isOpen, setIsOpen] = useState(false);

  const sportTypes = [
    { value: 'all', label: 'Tous les sports', icon: '🏃' },
    { value: 'running', label: 'Course à pied', icon: '🏃‍♂️' },
    { value: 'cycling', label: '<PERSON>é<PERSON>', icon: '🚴‍♂️' },
    { value: 'walking', label: 'Marche', icon: '🚶‍♂️' },
    { value: 'hiking', label: 'Randonnée', icon: '🥾' },
    { value: 'trail', label: 'Trail', icon: '⛰️' }
  ];

  const surfaceTypes = [
    { value: 'all', label: 'Toutes surfaces' },
    { value: 'road', label: 'Route' },
    { value: 'trail', label: 'Sentier' },
    { value: 'track', label: '<PERSON>ste' },
    { value: 'mixed', label: 'Mixte' }
  ];

  const difficultyLevels = [
    { value: 'all', label: 'Tous niveaux' },
    { value: 'easy', label: 'Facile', color: 'text-green-600' },
    { value: 'moderate', label: 'Modéré', color: 'text-yellow-600' },
    { value: 'hard', label: 'Difficile', color: 'text-red-600' },
    { value: 'expert', label: 'Expert', color: 'text-purple-600' }
  ];

  const distanceRanges = [
    { value: 'all', label: 'Toutes distances' },
    { value: '0-5', label: '0-5 km' },
    { value: '5-10', label: '5-10 km' },
    { value: '10-20', label: '10-20 km' },
    { value: '20-50', label: '20-50 km' },
    { value: '50+', label: '50+ km' }
  ];

  const handleFilterChange = (filterType, value) => {
    onFiltersChange({
      ...filters,
      [filterType]: value
    });
  };

  const resetFilters = () => {
    onFiltersChange({
      type: 'all',
      difficulty: 'all',
      distance: 'all',
      sport: 'all',
      surface: 'all'
    });
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== 'all').length;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <FiFilter className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Filtres</h3>
          {getActiveFiltersCount() > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={resetFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Réinitialiser
          </button>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FiX className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Filters Content */}
      <div className="p-4 space-y-6">
        {/* Sport Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <FiActivity className="inline h-4 w-4 mr-2" />
            Type de sport
          </label>
          <div className="grid grid-cols-2 gap-2">
            {sportTypes.map(sport => (
              <button
                key={sport.value}
                onClick={() => handleFilterChange('sport', sport.value)}
                className={`flex items-center space-x-2 p-2 rounded-lg border text-sm transition-colors ${
                  filters.sport === sport.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <span>{sport.icon}</span>
                <span>{sport.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Difficulty */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <FiTrendingUp className="inline h-4 w-4 mr-2" />
            Difficulté
          </label>
          <div className="space-y-2">
            {difficultyLevels.map(level => (
              <button
                key={level.value}
                onClick={() => handleFilterChange('difficulty', level.value)}
                className={`w-full flex items-center justify-between p-2 rounded-lg border text-sm transition-colors ${
                  filters.difficulty === level.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <span className={level.color || 'text-gray-700'}>{level.label}</span>
                {filters.difficulty === level.value && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Distance */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <FiMapPin className="inline h-4 w-4 mr-2" />
            Distance
          </label>
          <div className="space-y-2">
            {distanceRanges.map(range => (
              <button
                key={range.value}
                onClick={() => handleFilterChange('distance', range.value)}
                className={`w-full flex items-center justify-between p-2 rounded-lg border text-sm transition-colors ${
                  filters.distance === range.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <span>{range.label}</span>
                {filters.distance === range.value && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Surface */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <FiTarget className="inline h-4 w-4 mr-2" />
            Surface
          </label>
          <div className="grid grid-cols-2 gap-2">
            {surfaceTypes.map(surface => (
              <button
                key={surface.value}
                onClick={() => handleFilterChange('surface', surface.value)}
                className={`p-2 rounded-lg border text-sm transition-colors ${
                  filters.surface === surface.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                {surface.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilters;
