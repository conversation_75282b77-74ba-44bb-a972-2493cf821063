import React, { useEffect, useState } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet.heat';

const HeatmapLayer = ({ 
  data = [], 
  intensity = 0.6, 
  radius = 25, 
  blur = 15,
  maxZoom = 18,
  gradient = null,
  activityType = 'all',
  timeRange = 'all'
}) => {
  const map = useMap();
  const [heatLayer, setHeatLayer] = useState(null);

  // Gradients prédéfinis pour différents types d'activités
  const defaultGradients = {
    running: {
      0.0: '#000080',
      0.2: '#0000FF',
      0.4: '#00FFFF',
      0.6: '#00FF00',
      0.8: '#FFFF00',
      1.0: '#FF0000'
    },
    cycling: {
      0.0: '#000040',
      0.2: '#0040FF',
      0.4: '#0080FF',
      0.6: '#40C0FF',
      0.8: '#80E0FF',
      1.0: '#C0F0FF'
    },
    hiking: {
      0.0: '#004000',
      0.2: '#008000',
      0.4: '#40A000',
      0.6: '#80C000',
      0.8: '#C0E000',
      1.0: '#FFFF00'
    },
    all: {
      0.0: '#330066',
      0.2: '#660099',
      0.4: '#9900CC',
      0.6: '#CC00FF',
      0.8: '#FF3399',
      1.0: '#FF6666'
    }
  };

  // Générer des données de heatmap fictives si aucune n'est fournie
  const generateMockHeatmapData = (centerLat, centerLng, count = 1000) => {
    const points = [];
    
    for (let i = 0; i < count; i++) {
      // Créer des clusters autour de points populaires
      const clusterCenters = [
        { lat: centerLat + 0.01, lng: centerLng + 0.01, weight: 0.8 },
        { lat: centerLat - 0.005, lng: centerLng + 0.015, weight: 0.6 },
        { lat: centerLat + 0.02, lng: centerLng - 0.01, weight: 0.7 },
        { lat: centerLat - 0.015, lng: centerLng - 0.005, weight: 0.5 }
      ];
      
      const cluster = clusterCenters[Math.floor(Math.random() * clusterCenters.length)];
      
      // Ajouter du bruit gaussien autour du cluster
      const lat = cluster.lat + (Math.random() - 0.5) * 0.02;
      const lng = cluster.lng + (Math.random() - 0.5) * 0.02;
      const intensity = cluster.weight * (0.3 + Math.random() * 0.7);
      
      points.push([lat, lng, intensity]);
    }
    
    return points;
  };

  // Filtrer les données selon le type d'activité et la période
  const filterData = (rawData) => {
    if (!rawData || rawData.length === 0) {
      // Générer des données fictives centrées sur la carte
      const center = map.getCenter();
      return generateMockHeatmapData(center.lat, center.lng);
    }

    let filtered = rawData;

    // Filtrer par type d'activité
    if (activityType !== 'all') {
      filtered = filtered.filter(point => 
        point.activityType === activityType || !point.activityType
      );
    }

    // Filtrer par période (implémentation basique)
    if (timeRange !== 'all') {
      const now = new Date();
      const cutoffDate = new Date();
      
      switch (timeRange) {
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          break;
      }
      
      filtered = filtered.filter(point => 
        !point.timestamp || new Date(point.timestamp) >= cutoffDate
      );
    }

    return filtered.map(point => [
      point.lat || point[0],
      point.lng || point[1],
      point.intensity || point[2] || 0.5
    ]);
  };

  useEffect(() => {
    if (!map) return;

    // Supprimer l'ancienne couche
    if (heatLayer) {
      map.removeLayer(heatLayer);
    }

    // Préparer les données
    const filteredData = filterData(data);
    
    if (filteredData.length === 0) return;

    // Créer la nouvelle couche heatmap
    const options = {
      radius: radius,
      blur: blur,
      maxZoom: maxZoom,
      gradient: gradient || defaultGradients[activityType] || defaultGradients.all
    };

    const newHeatLayer = L.heatLayer(filteredData, options);
    
    // Ajouter à la carte
    newHeatLayer.addTo(map);
    setHeatLayer(newHeatLayer);

    // Ajuster l'opacité
    newHeatLayer.setOptions({ opacity: intensity });

    return () => {
      if (newHeatLayer) {
        map.removeLayer(newHeatLayer);
      }
    };
  }, [map, data, intensity, radius, blur, maxZoom, gradient, activityType, timeRange]);

  // Mettre à jour l'intensité
  useEffect(() => {
    if (heatLayer) {
      heatLayer.setOptions({ opacity: intensity });
    }
  }, [intensity, heatLayer]);

  return null;
};

// Composant de contrôle pour la heatmap
export const HeatmapControls = ({ 
  intensity, 
  onIntensityChange,
  radius,
  onRadiusChange,
  activityType,
  onActivityTypeChange,
  timeRange,
  onTimeRangeChange,
  visible,
  onVisibilityChange
}) => {
  const activityTypes = [
    { value: 'all', label: 'Toutes activités', color: '#9900CC' },
    { value: 'running', label: 'Course', color: '#FF0000' },
    { value: 'cycling', label: 'Vélo', color: '#0080FF' },
    { value: 'hiking', label: 'Randonnée', color: '#00FF00' }
  ];

  const timeRanges = [
    { value: 'all', label: 'Toute période' },
    { value: 'week', label: 'Cette semaine' },
    { value: 'month', label: 'Ce mois' },
    { value: 'year', label: 'Cette année' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Carte de chaleur</h3>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={visible}
            onChange={(e) => onVisibilityChange(e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-600">Afficher</span>
        </label>
      </div>

      {visible && (
        <>
          {/* Intensité */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Intensité: {Math.round(intensity * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={intensity}
              onChange={(e) => onIntensityChange(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* Rayon */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rayon: {radius}px
            </label>
            <input
              type="range"
              min="10"
              max="50"
              step="5"
              value={radius}
              onChange={(e) => onRadiusChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* Type d'activité */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type d'activité
            </label>
            <div className="space-y-2">
              {activityTypes.map(type => (
                <label key={type.value} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="activityType"
                    value={type.value}
                    checked={activityType === type.value}
                    onChange={(e) => onActivityTypeChange(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: type.color }}
                  ></div>
                  <span className="text-sm text-gray-700">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Période */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Période
            </label>
            <select
              value={timeRange}
              onChange={(e) => onTimeRangeChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {timeRanges.map(range => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>
        </>
      )}
    </div>
  );
};

export default HeatmapLayer;
