import React, { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';
import L from 'leaflet';
import {
  FiLayers,
  FiMaximize2,
  FiMinimize2,
  FiNavigation,
  FiZoomIn,
  FiZoomOut,
  FiMap,
  FiGlobe,
  FiMoon,
  FiTriangle
} from 'react-icons/fi';

// Fix Leaflet default icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Modern Strava-like map styles with high quality tiles
const mapStyles = {
  strava: {
    url: "https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    name: "Strava Style",
    icon: FiMap
  },
  satellite: {
    url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
    attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
    name: "Satellite",
    icon: FiGlobe
  },
  dark: {
    url: "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    name: "Dark",
    icon: FiMoon
  },
  terrain: {
    url: "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png",
    attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a>',
    name: "Terrain",
    icon: FiTriangle
  }
};

// Strava-style colors
const stravaColors = {
  orange: '#FC4C02',
  darkOrange: '#E34402',
  blue: '#0073E6',
  green: '#00D924',
  red: '#FF0000',
  purple: '#8B5CF6',
  yellow: '#FFC107'
};

// Custom Strava-style icons
const createStravaIcon = (type, color = stravaColors.orange) => {
  const iconMap = {
    start: '🏁',
    finish: '🏆',
    segment: '⚡',
    poi: '📍',
    user: '👤'
  };

  return L.divIcon({
    html: `
      <div style="
        background: linear-gradient(135deg, ${color} 0%, ${color}dd 100%);
        color: white;
        border-radius: 12px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border: 2px solid white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);
        font-weight: 600;
        transform: translateZ(0);
        transition: all 0.2s ease;
      ">${iconMap[type] || '📍'}</div>
    `,
    className: 'strava-marker',
    iconSize: [36, 36],
    iconAnchor: [18, 18]
  });
};

// Strava-style map controls
const StravaMapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {
  const [showStyleSelector, setShowStyleSelector] = useState(false);

  return (
    <div className="absolute top-4 right-4 z-[1000] space-y-3">
      {/* Style Selector with Strava design */}
      <div className="relative">
        <button
          onClick={() => setShowStyleSelector(!showStyleSelector)}
          className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl"
          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
          title="Change map style"
        >
          <FiLayers className="h-5 w-5 text-gray-700" />
        </button>

        {showStyleSelector && (
          <div className="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0"
               style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}>
            {Object.entries(mapStyles).map(([key, style]) => {
              const IconComponent = style.icon;
              return (
                <button
                  key={key}
                  onClick={() => {
                    onStyleChange(key);
                    setShowStyleSelector(false);
                  }}
                  className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${
                    currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'
                  }`}
                  style={currentStyle === key ? { backgroundColor: '#FFF7ED', color: stravaColors.orange } : {}}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{style.name}</span>
                </button>
              );
            })}
          </div>
        )}
      </div>

      {/* Locate button with Strava style */}
      <button
        onClick={onLocate}
        className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block"
        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
        title="Center on your location"
      >
        <FiNavigation className="h-5 w-5 text-gray-700" />
      </button>

      {/* Fullscreen button with Strava style */}
      <button
        onClick={onFullscreen}
        className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block"
        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
        title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
      >
        {isFullscreen ? (
          <FiMinimize2 className="h-5 w-5 text-gray-700" />
        ) : (
          <FiMaximize2 className="h-5 w-5 text-gray-700" />
        )}
      </button>
    </div>
  );
};

// Map event handler
const MapEventHandler = ({ onMapClick, onMapReady }) => {
  const map = useMap();

  useMapEvents({
    click: onMapClick,
    ready: () => {
      onMapReady && onMapReady(map);
    }
  });

  return null;
};

// Strava-style zoom controls
const StravaZoomControl = () => {
  const map = useMap();

  return (
    <div className="absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg"
         style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>
      <button
        onClick={() => map.zoomIn()}
        className="p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100"
        title="Zoom in"
      >
        <FiZoomIn className="h-5 w-5 text-gray-700" />
      </button>
      <button
        onClick={() => map.zoomOut()}
        className="p-3 hover:bg-gray-50 transition-all duration-150"
        title="Zoom out"
      >
        <FiZoomOut className="h-5 w-5 text-gray-700" />
      </button>
    </div>
  );
};

const ModernMap = ({
  center,
  zoom = 13,
  userPosition,
  routes = [],
  segments = [],
  pois = [],
  selectedRoute,
  selectedSegment,
  selectedPOIs = [],
  optimizedRoute,
  planningPoints = [],
  showPOIs = true,
  showSegments = false,
  onMapClick,
  onRouteSelect,
  onSegmentSelect,
  onPOISelect,
  className = "",
  height = "h-96 lg:h-[600px]"
}) => {
  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mapInstance, setMapInstance] = useState(null);
  const mapContainerRef = useRef(null);

  const handleLocate = () => {
    if (mapInstance && userPosition) {
      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {
        animate: true,
        duration: 1
      });
    }
  };

  const handleFullscreen = () => {
    if (mapContainerRef.current) {
      if (!isFullscreen) {
        mapContainerRef.current.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
      setIsFullscreen(!isFullscreen);
    }
  };

  const currentMapStyle = mapStyles[mapStyle];

  return (
    <div
      ref={mapContainerRef}
      className={`relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`}
      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}
    >
      <div className={`${height} relative`}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false}
          attributionControl={false}
          className="rounded-xl"
        >
          <TileLayer
            url={currentMapStyle.url}
            attribution={currentMapStyle.attribution}
            maxZoom={18}
          />

          <MapEventHandler
            onMapClick={onMapClick}
            onMapReady={setMapInstance}
          />

          <StravaZoomControl />
          
          {/* User position marker with Strava style */}
          {userPosition && (
            <Marker
              position={[userPosition.lat, userPosition.lng]}
              icon={createStravaIcon('user', stravaColors.blue)}
            >
              <Popup className="strava-popup">
                <div className="text-center p-2">
                  <strong className="text-gray-900">Your Location</strong>
                  <div className="text-sm text-gray-600 mt-1">Current position</div>
                </div>
              </Popup>
            </Marker>
          )}

          {/* Routes with Strava colors */}
          {routes.map(route => {
            const isSelected = selectedRoute?.id === route.id;
            const routeColor = isSelected ? stravaColors.orange : stravaColors.blue;

            return (
              <Polyline
                key={route.id}
                positions={route.points.map(p => [p.lat, p.lng])}
                color={routeColor}
                weight={isSelected ? 6 : 4}
                opacity={isSelected ? 1 : 0.7}
                eventHandlers={{
                  click: () => onRouteSelect && onRouteSelect(route)
                }}
              >
                <Popup className="strava-popup">
                  <div className="p-3">
                    <h3 className="font-bold text-gray-900 mb-2" style={{ color: stravaColors.orange }}>
                      {route.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">{route.description}</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Distance</div>
                        <div className="text-gray-900">{route.distance}km</div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Difficulty</div>
                        <div className="text-gray-900">{route.difficulty}</div>
                      </div>
                    </div>
                  </div>
                </Popup>
              </Polyline>
            );
          })}

          {/* Selected route highlight with Strava orange */}
          {selectedRoute && (
            <Polyline
              positions={selectedRoute.points.map(p => [p.lat, p.lng])}
              color={stravaColors.orange}
              weight={6}
              opacity={1}
            />
          )}

          {/* Optimized route with Strava green */}
          {optimizedRoute && (
            <Polyline
              positions={optimizedRoute.map(p => [p.lat, p.lng])}
              color={stravaColors.green}
              weight={5}
              opacity={0.9}
              dashArray="8, 8"
            />
          )}

          {/* Planning points with Strava style */}
          {planningPoints.map((point, index) => (
            <Marker
              key={index}
              position={[point.lat, point.lng]}
              icon={createStravaIcon('poi', stravaColors.yellow)}
            >
              <Popup className="strava-popup">
                <div className="text-center p-2">
                  <strong className="text-gray-900">Point {index + 1}</strong>
                  <div className="text-sm text-gray-600 mt-1">Planning waypoint</div>
                </div>
              </Popup>
            </Marker>
          ))}

          {/* POIs with Strava style */}
          {showPOIs && pois.map(poi => (
            <Marker
              key={poi.id}
              position={[poi.lat, poi.lng]}
              icon={createStravaIcon('poi', stravaColors.green)}
              eventHandlers={{
                click: () => onPOISelect && onPOISelect(poi)
              }}
            >
              <Popup className="strava-popup">
                <div className="p-3">
                  <h3 className="font-bold text-gray-900 mb-2" style={{ color: stravaColors.green }}>
                    {poi.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">{poi.type}</p>
                  {poi.rating && (
                    <div className="flex items-center bg-yellow-50 p-2 rounded">
                      <span className="text-yellow-500">⭐</span>
                      <span className="text-sm ml-1 font-medium">{poi.rating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          ))}

          {/* Selected POIs highlight */}
          {selectedPOIs.map(poi => (
            <Marker
              key={`selected-${poi.id}`}
              position={[poi.lat, poi.lng]}
              icon={createStravaIcon('poi', stravaColors.red)}
            />
          ))}

          {/* Segments with Strava colors */}
          {(showSegments || selectedSegment) && segments.map(segment => {
            const isSelected = selectedSegment?.id === segment.id;
            const segmentColor = isSelected ? stravaColors.orange :
                               segment.type === 'climb' ? stravaColors.red :
                               segment.type === 'sprint' ? stravaColors.green :
                               segment.type === 'descent' ? stravaColors.blue :
                               stravaColors.purple;

            return (
              <Polyline
                key={segment.id}
                positions={segment.points.map(point => [point.lat, point.lng])}
                color={segmentColor}
                weight={isSelected ? 7 : 5}
                opacity={isSelected ? 1 : 0.8}
                eventHandlers={{
                  click: () => onSegmentSelect && onSegmentSelect(segment)
                }}
              >
                <Popup className="strava-popup">
                  <div className="p-3">
                    <h3 className="font-bold text-gray-900 mb-2" style={{ color: segmentColor }}>
                      {segment.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">{segment.description}</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Distance</div>
                        <div className="text-gray-900">
                          {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}
                        </div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Elevation</div>
                        <div className="text-gray-900">
                          {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}
                        </div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Record</div>
                        <div className="text-gray-900">{segment.recordTime}</div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Attempts</div>
                        <div className="text-gray-900">{segment.attempts}</div>
                      </div>
                    </div>
                  </div>
                </Popup>
              </Polyline>
            );
          })}
        </MapContainer>

        {/* Strava-style controls overlay */}
        <StravaMapControls
          onStyleChange={setMapStyle}
          currentStyle={mapStyle}
          onLocate={handleLocate}
          onFullscreen={handleFullscreen}
          isFullscreen={isFullscreen}
        />
      </div>
    </div>
  );
};

export default ModernMap;
