import React, { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';
import L from 'leaflet';
import {
  FiLayers,
  FiMaximize2,
  FiMinimize2,
  FiNavigation,
  FiZoomIn,
  FiZoomOut,
  FiMap,
  FiGlobe,
  FiMoon,
  FiTriangle
} from 'react-icons/fi';

// Fix Leaflet default icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Modern Strava-like map styles with high quality tiles
const mapStyles = {
  strava: {
    url: "https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    name: "Strava Style",
    icon: FiMap
  },
  satellite: {
    url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
    attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
    name: "Satellite",
    icon: FiGlobe
  },
  dark: {
    url: "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    name: "Dark",
    icon: FiMoon
  },
  terrain: {
    url: "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png",
    attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a>',
    name: "Terrain",
    icon: FiTriangle
  }
};

// Modern Strava-style colors with gradients
const stravaColors = {
  orange: '#FC4C02',
  darkOrange: '#E34402',
  blue: '#0073E6',
  darkBlue: '#005BB5',
  green: '#00D924',
  darkGreen: '#00A01C',
  red: '#FF0000',
  darkRed: '#CC0000',
  purple: '#8B5CF6',
  darkPurple: '#7C3AED',
  yellow: '#FFC107',
  darkYellow: '#F59E0B',
  // Activity type colors
  running: '#FC4C02',
  cycling: '#0073E6',
  hiking: '#00D924',
  swimming: '#06B6D4',
  // Segment type colors
  climb: '#EF4444',
  sprint: '#10B981',
  descent: '#3B82F6',
  flat: '#8B5CF6'
};

// Route difficulty styling
const routeDifficultyStyles = {
  easy: { weight: 4, opacity: 0.8, color: stravaColors.green },
  moderate: { weight: 5, opacity: 0.85, color: stravaColors.yellow },
  hard: { weight: 6, opacity: 0.9, color: stravaColors.orange },
  extreme: { weight: 7, opacity: 0.95, color: stravaColors.red }
};

// Activity type styling
const activityTypeStyles = {
  running: {
    weight: 5,
    opacity: 0.9,
    color: stravaColors.running,
    dashArray: null
  },
  cycling: {
    weight: 6,
    opacity: 0.9,
    color: stravaColors.cycling,
    dashArray: null
  },
  hiking: {
    weight: 4,
    opacity: 0.85,
    color: stravaColors.hiking,
    dashArray: '8, 4'
  },
  swimming: {
    weight: 5,
    opacity: 0.9,
    color: stravaColors.swimming,
    dashArray: '12, 8'
  }
};

// Modern POI icons with detailed styling
const createModernPOIIcon = (poiType, isSelected = false) => {
  const iconMap = {
    restaurant: '🍽️',
    cafe: '☕',
    hotel: '🏨',
    gas_station: '⛽',
    hospital: '🏥',
    pharmacy: '💊',
    bank: '🏦',
    atm: '💳',
    parking: '🅿️',
    toilet: '🚻',
    water: '💧',
    viewpoint: '👁️',
    monument: '🏛️',
    park: '🌳',
    beach: '🏖️',
    mountain: '⛰️',
    default: '📍'
  };

  const size = isSelected ? 44 : 36;
  const shadowSize = isSelected ? 16 : 12;

  return L.divIcon({
    html: `
      <div class="modern-poi-marker ${isSelected ? 'selected' : ''}" style="
        background: linear-gradient(135deg, ${stravaColors.blue} 0%, ${stravaColors.darkBlue} 100%);
        color: white;
        border-radius: 50%;
        width: ${size}px;
        height: ${size}px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${size * 0.4}px;
        border: 3px solid white;
        box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0,0,0,0.2),
                    0 ${shadowSize/2}px ${shadowSize}px rgba(0,0,0,0.1),
                    inset 0 1px 0 rgba(255,255,255,0.3);
        font-weight: 600;
        transform: translateZ(0);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
      ">
        <span style="filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));">
          ${iconMap[poiType] || iconMap.default}
        </span>
      </div>
    `,
    className: 'modern-poi-icon',
    iconSize: [size, size],
    iconAnchor: [size/2, size/2]
  });
};

// Modern segment markers (start/finish)
const createSegmentMarker = (type, segmentType = 'general') => {
  const colors = {
    climb: stravaColors.red,
    sprint: stravaColors.green,
    descent: stravaColors.blue,
    general: stravaColors.purple
  };

  const icons = {
    start: '🚀',
    finish: '🎯'
  };

  return L.divIcon({
    html: `
      <div class="segment-marker ${type}" style="
        background: linear-gradient(135deg, ${colors[segmentType]} 0%, ${colors[segmentType]}dd 100%);
        color: white;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border: 2px solid white;
        box-shadow: 0 6px 20px rgba(0,0,0,0.15),
                    0 2px 6px rgba(0,0,0,0.1),
                    inset 0 1px 0 rgba(255,255,255,0.2);
        font-weight: bold;
        transform: translateZ(0);
        transition: all 0.2s ease;
        position: relative;
      ">
        <span style="filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));">
          ${icons[type]}
        </span>
      </div>
    `,
    className: 'segment-marker-icon',
    iconSize: [32, 32],
    iconAnchor: [16, 16]
  });
};

// User location icon
const createUserLocationIcon = () => {
  return L.divIcon({
    html: `
      <div class="user-location-marker" style="
        background: linear-gradient(135deg, ${stravaColors.orange} 0%, ${stravaColors.darkOrange} 100%);
        color: white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        border: 4px solid white;
        box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),
                    0 3px 10px rgba(0,0,0,0.2),
                    inset 0 1px 0 rgba(255,255,255,0.3);
        font-weight: bold;
        transform: translateZ(0);
        animation: pulse 2s infinite;
        position: relative;
      ">
        <span style="filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));">📍</span>
      </div>
    `,
    className: 'user-location-icon',
    iconSize: [40, 40],
    iconAnchor: [20, 20]
  });
};

// Create cluster icon for grouped POIs
const createClusterIcon = (count) => {
  const size = count < 10 ? 30 : count < 50 ? 40 : 50;
  const bgColor = count < 10 ? '#0073E6' : count < 50 ? '#FC4C02' : '#E53E3E';

  return L.divIcon({
    html: `
      <div style="
        width: ${size}px;
        height: ${size}px;
        background: linear-gradient(135deg, ${bgColor} 0%, ${bgColor}CC 100%);
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: ${size < 40 ? '12px' : '14px'};
        box-shadow: 0 8px 25px rgba(0,0,0,0.2),
                    0 3px 10px rgba(0,0,0,0.1);
        transform: translate(-50%, -50%);
        cursor: pointer;
        transition: all 0.2s ease;
      " class="poi-cluster-marker">
        ${count}
      </div>
    `,
    className: 'strava-marker',
    iconSize: [size, size],
    iconAnchor: [size/2, size/2]
  });
};

// Modern route styling functions
const getRouteStyle = (route, isSelected = false, isHovered = false) => {
  const baseStyle = activityTypeStyles[route.activityType] || activityTypeStyles.running;
  const difficultyStyle = routeDifficultyStyles[route.difficulty] || routeDifficultyStyles.moderate;

  return {
    color: isSelected ? stravaColors.orange : baseStyle.color,
    weight: isSelected ? difficultyStyle.weight + 2 : difficultyStyle.weight,
    opacity: isSelected ? 1 : (isHovered ? 0.9 : baseStyle.opacity),
    dashArray: baseStyle.dashArray,
    lineCap: 'round',
    lineJoin: 'round'
  };
};

const getSegmentStyle = (segment, isSelected = false) => {
  const colors = {
    climb: stravaColors.red,
    sprint: stravaColors.green,
    descent: stravaColors.blue,
    flat: stravaColors.purple
  };

  const color = colors[segment.type] || colors.flat;

  return {
    color: isSelected ? stravaColors.orange : color,
    weight: isSelected ? 8 : 6,
    opacity: isSelected ? 1 : 0.85,
    dashArray: segment.type === 'sprint' ? '10, 5' : null,
    lineCap: 'round',
    lineJoin: 'round'
  };
};

// Modern popup content generator
const createModernPopup = (title, content, type = 'default') => {
  const typeColors = {
    route: stravaColors.blue,
    segment: stravaColors.red,
    poi: stravaColors.green,
    default: stravaColors.orange
  };

  return `
    <div class="modern-popup" style="
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      min-width: 250px;
      max-width: 350px;
    ">
      <div style="
        background: linear-gradient(135deg, ${typeColors[type]} 0%, ${typeColors[type]}dd 100%);
        color: white;
        padding: 12px 16px;
        margin: -12px -16px 12px -16px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
      ">
        ${title}
      </div>
      <div style="
        color: #374151;
        line-height: 1.5;
      ">
        ${content}
      </div>
    </div>
  `;
};

// Strava-style map controls
const StravaMapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {
  const [showStyleSelector, setShowStyleSelector] = useState(false);

  return (
    <div className="absolute top-4 right-4 z-[1000] space-y-3">
      {/* Style Selector with Strava design */}
      <div className="relative">
        <button
          onClick={() => setShowStyleSelector(!showStyleSelector)}
          className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl"
          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
          title="Change map style"
        >
          <FiLayers className="h-5 w-5 text-gray-700" />
        </button>

        {showStyleSelector && (
          <div className="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-2xl min-w-[160px] overflow-hidden border-0"
               style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}>
            {Object.entries(mapStyles).map(([key, style]) => {
              const IconComponent = style.icon;
              return (
                <button
                  key={key}
                  onClick={() => {
                    onStyleChange(key);
                    setShowStyleSelector(false);
                  }}
                  className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-150 flex items-center space-x-3 ${
                    currentStyle === key ? 'bg-orange-50 text-orange-600 font-medium' : 'text-gray-700'
                  }`}
                  style={currentStyle === key ? { backgroundColor: '#FFF7ED', color: stravaColors.orange } : {}}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{style.name}</span>
                </button>
              );
            })}
          </div>
        )}
      </div>

      {/* Locate button with Strava style */}
      <button
        onClick={onLocate}
        className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block"
        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
        title="Center on your location"
      >
        <FiNavigation className="h-5 w-5 text-gray-700" />
      </button>

      {/* Fullscreen button with Strava style */}
      <button
        onClick={onFullscreen}
        className="bg-white hover:bg-gray-50 border-0 rounded-xl p-3 shadow-lg transition-all duration-200 hover:shadow-xl block"
        style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
        title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
      >
        {isFullscreen ? (
          <FiMinimize2 className="h-5 w-5 text-gray-700" />
        ) : (
          <FiMaximize2 className="h-5 w-5 text-gray-700" />
        )}
      </button>
    </div>
  );
};

// Map event handler
const MapEventHandler = ({ onMapClick, onMapReady }) => {
  const map = useMap();

  useMapEvents({
    click: onMapClick,
    ready: () => {
      onMapReady && onMapReady(map);
    },
    zoomend: () => {
      if (onMapReady) {
        // Notify parent component of zoom change
        const currentZoom = map.getZoom();
        map.fire('zoomchange', { zoom: currentZoom });
      }
    }
  });

  return null;
};

// Strava-style zoom controls
const StravaZoomControl = () => {
  const map = useMap();

  return (
    <div className="absolute bottom-6 right-4 z-[1000] flex flex-col bg-white rounded-xl overflow-hidden shadow-lg"
         style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>
      <button
        onClick={() => map.zoomIn()}
        className="p-3 hover:bg-gray-50 transition-all duration-150 border-b border-gray-100"
        title="Zoom in"
      >
        <FiZoomIn className="h-5 w-5 text-gray-700" />
      </button>
      <button
        onClick={() => map.zoomOut()}
        className="p-3 hover:bg-gray-50 transition-all duration-150"
        title="Zoom out"
      >
        <FiZoomOut className="h-5 w-5 text-gray-700" />
      </button>
    </div>
  );
};

// Layer control component
const LayerControl = ({ visibleLayers, onLayerToggle, currentZoom }) => {
  return (
    <div className="absolute top-4 left-4 z-[1000] bg-white rounded-lg shadow-lg border border-gray-200 p-3 min-w-[200px]">
      <div className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
        <FiLayers className="h-4 w-4 mr-2" />
        Map Layers
      </div>
      <div className="space-y-2">
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={visibleLayers.routes}
            onChange={(e) => onLayerToggle('routes', e.target.checked)}
            className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
          />
          <span className="text-sm text-gray-600">Routes</span>
          <span className="text-xs text-gray-400">({currentZoom >= 10 ? 'visible' : 'zoom in'})</span>
        </label>

        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={visibleLayers.segments}
            onChange={(e) => onLayerToggle('segments', e.target.checked)}
            className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
          />
          <span className="text-sm text-gray-600">Segments</span>
          <span className="text-xs text-gray-400">({currentZoom >= 14 ? 'visible' : 'zoom in'})</span>
        </label>

        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={visibleLayers.pois}
            onChange={(e) => onLayerToggle('pois', e.target.checked)}
            className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
          />
          <span className="text-sm text-gray-600">Points of Interest</span>
          <span className="text-xs text-gray-400">({currentZoom >= 12 ? 'visible' : 'zoom in'})</span>
        </label>
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          Zoom: {currentZoom.toFixed(1)}
        </div>
      </div>
    </div>
  );
};

const ModernMap = ({
  center,
  zoom = 13,
  userPosition,
  routes = [],
  segments = [],
  pois = [],
  selectedRoute,
  selectedSegment,
  selectedPOIs = [],
  optimizedRoute,
  planningPoints = [],
  showPOIs = true,
  showSegments = false,
  onMapClick,
  onRouteSelect,
  onSegmentSelect,
  onPOISelect,
  className = "",
  height = "h-96 lg:h-[600px]"
}) => {
  const [mapStyle, setMapStyle] = useState('strava'); // Default to Strava style
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mapInstance, setMapInstance] = useState(null);
  const [currentZoom, setCurrentZoom] = useState(zoom);
  const [visibleLayers, setVisibleLayers] = useState({
    routes: true,
    segments: false,
    pois: true,
    planning: true
  });
  const mapContainerRef = useRef(null);

  // Smart layer visibility based on zoom level
  const getLayerVisibility = (zoomLevel) => {
    return {
      routes: zoomLevel >= 10, // Show routes at medium zoom
      segments: zoomLevel >= 14, // Show segments only at high zoom
      pois: zoomLevel >= 12, // Show POIs at medium-high zoom
      planning: true, // Always show planning points
      routeLabels: zoomLevel >= 15, // Show route labels only at very high zoom
      detailedMarkers: zoomLevel >= 13 // Show detailed markers at high zoom
    };
  };

  // Update layer visibility when zoom changes
  const handleZoomChange = (newZoom) => {
    setCurrentZoom(newZoom);
    const newVisibility = getLayerVisibility(newZoom);
    setVisibleLayers(prev => ({
      ...prev,
      ...newVisibility
    }));
  };

  // Handle manual layer toggle
  const handleLayerToggle = (layerName, isVisible) => {
    setVisibleLayers(prev => ({
      ...prev,
      [layerName]: isVisible
    }));
  };

  // Filter routes to reduce clutter
  const getVisibleRoutes = () => {
    if (!visibleLayers.routes) return [];

    // If a route is selected, show it prominently and reduce others
    if (selectedRoute) {
      return routes.filter(route =>
        route.id === selectedRoute.id ||
        (currentZoom >= 13 && route.difficulty !== 'easy') // Show only non-easy routes when zoomed in
      );
    }

    // At lower zoom levels, show fewer routes to reduce clutter
    if (currentZoom < 12) {
      return routes.filter(route => route.difficulty === 'hard' || route.difficulty === 'extreme');
    }

    return routes;
  };

  // Filter segments based on zoom and selection
  const getVisibleSegments = () => {
    if (!visibleLayers.segments || !showSegments) return [];

    // Always show selected segment
    if (selectedSegment) {
      return segments.filter(segment => segment.id === selectedSegment.id);
    }

    // At high zoom, show all segments
    if (currentZoom >= 15) return segments;

    // At medium zoom, show only important segments
    return segments.filter(segment =>
      segment.type === 'climb' || segment.type === 'sprint'
    );
  };

  // Simple POI clustering based on distance
  const getClusteredPOIs = () => {
    if (!visibleLayers.pois || !showPOIs) return [];

    const clusteredPOIs = [];
    const processed = new Set();
    const clusterDistance = currentZoom < 13 ? 0.01 : 0.005; // Adjust cluster distance based on zoom

    pois.forEach((poi, index) => {
      if (processed.has(index)) return;

      const cluster = [poi];
      processed.add(index);

      // Find nearby POIs to cluster
      if (currentZoom < 14) {
        pois.forEach((otherPoi, otherIndex) => {
          if (processed.has(otherIndex) || index === otherIndex) return;

          const distance = Math.sqrt(
            Math.pow(poi.lat - otherPoi.lat, 2) +
            Math.pow(poi.lng - otherPoi.lng, 2)
          );

          if (distance < clusterDistance) {
            cluster.push(otherPoi);
            processed.add(otherIndex);
          }
        });
      }

      clusteredPOIs.push({
        ...poi,
        isCluster: cluster.length > 1,
        clusterSize: cluster.length,
        clusteredPOIs: cluster
      });
    });

    return clusteredPOIs;
  };

  const handleLocate = () => {
    if (mapInstance && userPosition) {
      mapInstance.setView([userPosition.lat, userPosition.lng], 15, {
        animate: true,
        duration: 1
      });
    }
  };

  const handleFullscreen = () => {
    if (mapContainerRef.current) {
      if (!isFullscreen) {
        mapContainerRef.current.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
      setIsFullscreen(!isFullscreen);
    }
  };

  const currentMapStyle = mapStyles[mapStyle];

  return (
    <div
      ref={mapContainerRef}
      className={`relative bg-white rounded-xl shadow-xl overflow-hidden ${className}`}
      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}
    >
      <div className={`${height} relative`}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false}
          attributionControl={false}
          className="rounded-xl"
        >
          <TileLayer
            url={currentMapStyle.url}
            attribution={currentMapStyle.attribution}
            maxZoom={18}
          />

          <MapEventHandler
            onMapClick={onMapClick}
            onMapReady={(map) => {
              setMapInstance(map);
              // Listen for zoom changes
              map.on('zoomchange', (e) => {
                handleZoomChange(e.zoom);
              });
            }}
          />

          <StravaZoomControl />
          <LayerControl
            visibleLayers={visibleLayers}
            onLayerToggle={handleLayerToggle}
            currentZoom={currentZoom}
          />
          
          {/* Modern user position marker */}
          {userPosition && (
            <Marker
              position={[userPosition.lat, userPosition.lng]}
              icon={createUserLocationIcon()}
            >
              <Popup className="strava-popup">
                <div dangerouslySetInnerHTML={{
                  __html: createModernPopup(
                    "Your Location",
                    `
                      <div class="text-center">
                        <div class="text-sm text-gray-600 mb-2">Current position</div>
                        <div class="text-xs text-gray-500">
                          Lat: ${userPosition.lat.toFixed(6)}<br>
                          Lng: ${userPosition.lng.toFixed(6)}
                        </div>
                      </div>
                    `,
                    'default'
                  )
                }} />
              </Popup>
            </Marker>
          )}

          {/* Smart routes rendering with clutter reduction */}
          {getVisibleRoutes().map(route => {
            const isSelected = selectedRoute?.id === route.id;
            const routeStyle = getRouteStyle(route, isSelected);

            return (
              <Polyline
                key={route.id}
                positions={route.points.map(p => [p.lat, p.lng])}
                {...routeStyle}
                eventHandlers={{
                  click: () => onRouteSelect && onRouteSelect(route),
                  mouseover: (e) => {
                    if (!isSelected) {
                      e.target.setStyle({ opacity: 0.9, weight: routeStyle.weight + 1 });
                    }
                  },
                  mouseout: (e) => {
                    if (!isSelected) {
                      e.target.setStyle({ opacity: routeStyle.opacity, weight: routeStyle.weight });
                    }
                  }
                }}
              >
                <Popup className="strava-popup">
                  <div dangerouslySetInnerHTML={{
                    __html: createModernPopup(
                      route.name,
                      `
                        <div class="space-y-3">
                          <p class="text-sm text-gray-600">${route.description || 'No description available'}</p>

                          <div class="grid grid-cols-2 gap-3">
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg">
                              <div class="text-xs font-medium text-blue-700 uppercase tracking-wide">Distance</div>
                              <div class="text-lg font-bold text-blue-900">${route.distance}km</div>
                            </div>
                            <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg">
                              <div class="text-xs font-medium text-orange-700 uppercase tracking-wide">Difficulty</div>
                              <div class="text-lg font-bold text-orange-900 capitalize">${route.difficulty || 'moderate'}</div>
                            </div>
                          </div>

                          <div class="grid grid-cols-2 gap-3">
                            <div class="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg">
                              <div class="text-xs font-medium text-green-700 uppercase tracking-wide">Activity</div>
                              <div class="text-sm font-semibold text-green-900 capitalize">${route.activityType || 'running'}</div>
                            </div>
                            <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg">
                              <div class="text-xs font-medium text-purple-700 uppercase tracking-wide">Elevation</div>
                              <div class="text-sm font-semibold text-purple-900">${route.elevation || 0}m</div>
                            </div>
                          </div>
                        </div>
                      `,
                      'route'
                    )
                  }} />
                </Popup>
              </Polyline>
            );
          })}

          {/* Modern optimized route with animated dash */}
          {optimizedRoute && (
            <Polyline
              positions={optimizedRoute.map(p => [p.lat, p.lng])}
              color={stravaColors.green}
              weight={6}
              opacity={0.95}
              dashArray="12, 8"
              lineCap="round"
              lineJoin="round"
              className="animated-route"
            />
          )}

          {/* Modern planning points */}
          {planningPoints.map((point, index) => (
            <Marker
              key={index}
              position={[point.lat, point.lng]}
              icon={createModernPOIIcon('default', false)}
            >
              <Popup className="strava-popup">
                <div dangerouslySetInnerHTML={{
                  __html: createModernPopup(
                    `Waypoint ${index + 1}`,
                    `
                      <div class="text-center">
                        <div class="text-sm text-gray-600 mb-2">Planning waypoint</div>
                        <div class="text-xs text-gray-500">
                          Lat: ${point.lat.toFixed(6)}<br>
                          Lng: ${point.lng.toFixed(6)}
                        </div>
                      </div>
                    `,
                    'poi'
                  )
                }} />
              </Popup>
            </Marker>
          ))}

          {/* Smart POIs with clustering */}
          {getClusteredPOIs().map(poi => {
            const isSelected = selectedPOIs.some(selected => selected.id === poi.id);

            // Render cluster marker if this is a cluster
            if (poi.isCluster && poi.clusterSize > 1) {
              return (
                <Marker
                  key={`cluster-${poi.id}`}
                  position={[poi.lat, poi.lng]}
                  icon={createClusterIcon(poi.clusterSize)}
                  eventHandlers={{
                    click: () => {
                      // Zoom in to show individual POIs
                      if (mapInstance) {
                        mapInstance.setView([poi.lat, poi.lng], Math.min(currentZoom + 2, 18), {
                          animate: true,
                          duration: 0.5
                        });
                      }
                    }
                  }}
                >
                  <Popup className="strava-popup">
                    <div dangerouslySetInnerHTML={{
                      __html: createModernPopup(
                        `${poi.clusterSize} Points of Interest`,
                        `
                          <div class="space-y-2">
                            <p class="text-sm text-gray-600">Click to zoom in and see individual locations</p>
                            <div class="text-xs text-gray-500">
                              ${poi.clusteredPOIs.map(p => `• ${p.name || p.type}`).join('<br>')}
                            </div>
                          </div>
                        `,
                        'poi'
                      )
                    }} />
                  </Popup>
                </Marker>
              );
            }

            // Render individual POI
            return (
              <Marker
                key={poi.id}
                position={[poi.lat, poi.lng]}
                icon={createModernPOIIcon(poi.type || 'default', isSelected)}
                eventHandlers={{
                  click: () => onPOISelect && onPOISelect(poi)
                }}
              >
                <Popup className="strava-popup">
                  <div dangerouslySetInnerHTML={{
                    __html: createModernPopup(
                      poi.name,
                      `
                        <div class="space-y-3">
                          <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              ${poi.type || 'POI'}
                            </span>
                          </div>

                          ${poi.description ? `<p class="text-sm text-gray-600">${poi.description}</p>` : ''}

                          ${poi.rating ? `
                            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg">
                              <div class="flex items-center justify-center space-x-2">
                                <span class="text-yellow-500 text-lg">⭐</span>
                                <span class="text-lg font-bold text-yellow-700">${poi.rating.toFixed(1)}</span>
                                <span class="text-sm text-gray-500">/ 5.0</span>
                              </div>
                            </div>
                          ` : ''}

                          ${poi.amenities ? `
                            <div class="space-y-1">
                              <div class="text-xs font-medium text-gray-700 uppercase tracking-wide">Amenities</div>
                              <div class="flex flex-wrap gap-1">
                                ${poi.amenities.map(amenity => `
                                  <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                                    ${amenity}
                                  </span>
                                `).join('')}
                              </div>
                            </div>
                          ` : ''}
                        </div>
                      `,
                      'poi'
                    )
                  }} />
                </Popup>
              </Marker>
            );
          })}

          {/* Selected POIs with enhanced highlight */}
          {selectedPOIs.map(poi => (
            <Marker
              key={`selected-${poi.id}`}
              position={[poi.lat, poi.lng]}
              icon={createModernPOIIcon(poi.type || 'default', true)}
            />
          ))}

          {/* Smart segments rendering with zoom-based visibility */}
          {getVisibleSegments().map(segment => {
            const isSelected = selectedSegment?.id === segment.id;
            const segmentStyle = getSegmentStyle(segment, isSelected);

            return (
              <React.Fragment key={segment.id}>
                {/* Segment start marker */}
                {segment.points && segment.points.length > 0 && (
                  <Marker
                    position={[segment.points[0].lat, segment.points[0].lng]}
                    icon={createSegmentMarker('start', segment.type)}
                  >
                    <Popup className="strava-popup">
                      <div dangerouslySetInnerHTML={{
                        __html: createModernPopup(
                          `${segment.name} - Start`,
                          `
                            <div class="text-center">
                              <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-2">
                                🚀 Segment Start
                              </div>
                              <div class="text-sm text-gray-600">
                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}
                              </div>
                            </div>
                          `,
                          'segment'
                        )
                      }} />
                    </Popup>
                  </Marker>
                )}

                {/* Segment polyline */}
                <Polyline
                  positions={segment.points.map(point => [point.lat, point.lng])}
                  {...segmentStyle}
                  eventHandlers={{
                    click: () => onSegmentSelect && onSegmentSelect(segment),
                    mouseover: (e) => {
                      if (!isSelected) {
                        e.target.setStyle({
                          opacity: 1,
                          weight: segmentStyle.weight + 1
                        });
                      }
                    },
                    mouseout: (e) => {
                      if (!isSelected) {
                        e.target.setStyle({
                          opacity: segmentStyle.opacity,
                          weight: segmentStyle.weight
                        });
                      }
                    }
                  }}
                >
                  <Popup className="strava-popup">
                    <div dangerouslySetInnerHTML={{
                      __html: createModernPopup(
                        segment.name,
                        `
                          <div class="space-y-4">
                            <div class="flex items-center justify-center">
                              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                segment.type === 'climb' ? 'bg-red-100 text-red-800' :
                                segment.type === 'sprint' ? 'bg-green-100 text-green-800' :
                                segment.type === 'descent' ? 'bg-blue-100 text-blue-800' :
                                'bg-purple-100 text-purple-800'
                              }">
                                ${segment.type === 'climb' ? '⛰️' :
                                  segment.type === 'sprint' ? '💨' :
                                  segment.type === 'descent' ? '⬇️' : '📏'}
                                ${segment.type ? segment.type.charAt(0).toUpperCase() + segment.type.slice(1) : 'Segment'}
                              </span>
                            </div>

                            <p class="text-sm text-gray-600 text-center">${segment.description || 'No description available'}</p>

                            <div class="grid grid-cols-2 gap-3">
                              <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center">
                                <div class="text-xs font-medium text-blue-700 uppercase tracking-wide">Distance</div>
                                <div class="text-lg font-bold text-blue-900">
                                  ${segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}
                                </div>
                              </div>
                              <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg text-center">
                                <div class="text-xs font-medium text-orange-700 uppercase tracking-wide">Elevation</div>
                                <div class="text-lg font-bold text-orange-900">
                                  ${segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation || 0}m`}
                                </div>
                              </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                              <div class="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg text-center">
                                <div class="text-xs font-medium text-green-700 uppercase tracking-wide">Record</div>
                                <div class="text-sm font-bold text-green-900">${segment.recordTime || 'No record'}</div>
                              </div>
                              <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center">
                                <div class="text-xs font-medium text-purple-700 uppercase tracking-wide">Attempts</div>
                                <div class="text-sm font-bold text-purple-900">${segment.attempts || 0}</div>
                              </div>
                            </div>
                          </div>
                        `,
                        'segment'
                      )
                    }} />
                  </Popup>
                </Polyline>

                {/* Segment finish marker */}
                {segment.points && segment.points.length > 1 && (
                  <Marker
                    position={[segment.points[segment.points.length - 1].lat, segment.points[segment.points.length - 1].lng]}
                    icon={createSegmentMarker('finish', segment.type)}
                  >
                    <Popup className="strava-popup">
                      <div dangerouslySetInnerHTML={{
                        __html: createModernPopup(
                          `${segment.name} - Finish`,
                          `
                            <div class="text-center">
                              <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 mb-2">
                                🎯 Segment Finish
                              </div>
                              <div class="text-sm text-gray-600">
                                ${segment.type ? `Type: ${segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}` : ''}
                              </div>
                            </div>
                          `,
                          'segment'
                        )
                      }} />
                    </Popup>
                  </Marker>
                )}
              </React.Fragment>
            );
          })}
        </MapContainer>

        {/* Strava-style controls overlay */}
        <StravaMapControls
          onStyleChange={setMapStyle}
          currentStyle={mapStyle}
          onLocate={handleLocate}
          onFullscreen={handleFullscreen}
          isFullscreen={isFullscreen}
        />
      </div>
    </div>
  );
};

export default ModernMap;
