import React, { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';
import L from 'leaflet';
import {
  FiLayers,
  FiMaximize2,
  FiMinimize2,
  FiNavigation,
  FiZoomIn,
  FiZoomOut,
  FiMapPin,
  FiTarget
} from 'react-icons/fi';

// Fix Leaflet default icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom map styles
const mapStyles = {
  default: {
    url: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    name: "Standard"
  },
  satellite: {
    url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
    attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
    name: "Satellite"
  },
  dark: {
    url: "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    name: "Dark"
  },
  terrain: {
    url: "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png",
    attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a>',
    name: "Terrain"
  }
};

// Custom icons
const createCustomIcon = (color, icon) => {
  return L.divIcon({
    html: `
      <div style="
        background: ${color};
        color: white;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        font-weight: bold;
      ">${icon}</div>
    `,
    className: 'custom-marker',
    iconSize: [32, 32],
    iconAnchor: [16, 16]
  });
};

// Map controls component
const MapControls = ({ onStyleChange, currentStyle, onLocate, onFullscreen, isFullscreen }) => {
  const [showStyleSelector, setShowStyleSelector] = useState(false);

  return (
    <div className="absolute top-4 right-4 z-[1000] space-y-2">
      {/* Style Selector */}
      <div className="relative">
        <button
          onClick={() => setShowStyleSelector(!showStyleSelector)}
          className="bg-white hover:bg-gray-50 border border-gray-300 rounded-lg p-2 shadow-lg transition-colors"
          title="Change map style"
        >
          <FiLayers className="h-5 w-5 text-gray-700" />
        </button>
        
        {showStyleSelector && (
          <div className="absolute top-full right-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-xl min-w-[120px] overflow-hidden">
            {Object.entries(mapStyles).map(([key, style]) => (
              <button
                key={key}
                onClick={() => {
                  onStyleChange(key);
                  setShowStyleSelector(false);
                }}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                  currentStyle === key ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                }`}
              >
                {style.name}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Locate button */}
      <button
        onClick={onLocate}
        className="bg-white hover:bg-gray-50 border border-gray-300 rounded-lg p-2 shadow-lg transition-colors block"
        title="Center on your location"
      >
        <FiNavigation className="h-5 w-5 text-gray-700" />
      </button>

      {/* Fullscreen button */}
      <button
        onClick={onFullscreen}
        className="bg-white hover:bg-gray-50 border border-gray-300 rounded-lg p-2 shadow-lg transition-colors block"
        title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
      >
        {isFullscreen ? (
          <FiMinimize2 className="h-5 w-5 text-gray-700" />
        ) : (
          <FiMaximize2 className="h-5 w-5 text-gray-700" />
        )}
      </button>
    </div>
  );
};

// Map event handler
const MapEventHandler = ({ onMapClick, onMapReady }) => {
  const map = useMap();

  useMapEvents({
    click: onMapClick,
    ready: () => {
      onMapReady && onMapReady(map);
    }
  });

  return null;
};

// Zoom controls component
const CustomZoomControl = () => {
  const map = useMap();

  return (
    <div className="absolute bottom-4 right-4 z-[1000] flex flex-col space-y-1">
      <button
        onClick={() => map.zoomIn()}
        className="bg-white hover:bg-gray-50 border border-gray-300 rounded-t-lg p-2 shadow-lg transition-colors"
        title="Zoom in"
      >
        <FiZoomIn className="h-5 w-5 text-gray-700" />
      </button>
      <button
        onClick={() => map.zoomOut()}
        className="bg-white hover:bg-gray-50 border border-gray-300 rounded-b-lg p-2 shadow-lg transition-colors"
        title="Zoom out"
      >
        <FiZoomOut className="h-5 w-5 text-gray-700" />
      </button>
    </div>
  );
};

const ModernMap = ({
  center,
  zoom = 13,
  userPosition,
  routes = [],
  segments = [],
  pois = [],
  selectedRoute,
  selectedSegment,
  selectedPOIs = [],
  optimizedRoute,
  planningPoints = [],
  showPOIs = true,
  showSegments = false,
  onMapClick,
  onRouteSelect,
  onSegmentSelect,
  onPOISelect,
  className = "",
  height = "h-96 lg:h-[600px]"
}) => {
  const [mapStyle, setMapStyle] = useState('default');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mapInstance, setMapInstance] = useState(null);
  const mapContainerRef = useRef(null);

  const handleLocate = () => {
    if (mapInstance && userPosition) {
      mapInstance.setView([userPosition.lat, userPosition.lng], 15);
    }
  };

  const handleFullscreen = () => {
    if (mapContainerRef.current) {
      if (!isFullscreen) {
        mapContainerRef.current.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
      setIsFullscreen(!isFullscreen);
    }
  };

  const currentMapStyle = mapStyles[mapStyle];

  return (
    <div 
      ref={mapContainerRef}
      className={`relative bg-white rounded-lg shadow-lg overflow-hidden ${className}`}
    >
      <div className={`${height} relative`}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false}
          attributionControl={false}
        >
          <TileLayer
            url={currentMapStyle.url}
            attribution={currentMapStyle.attribution}
            maxZoom={18}
          />
          
          <MapEventHandler 
            onMapClick={onMapClick}
            onMapReady={setMapInstance}
          />
          
          <CustomZoomControl />
          
          {/* User position marker */}
          {userPosition && (
            <Marker 
              position={[userPosition.lat, userPosition.lng]}
              icon={createCustomIcon('#3B82F6', '📍')}
            >
              <Popup>
                <div className="text-center">
                  <strong>Your Location</strong>
                </div>
              </Popup>
            </Marker>
          )}
          
          {/* Routes */}
          {routes.map(route => (
            <Polyline
              key={route.id}
              positions={route.points.map(p => [p.lat, p.lng])}
              color={selectedRoute?.id === route.id ? '#3B82F6' : '#6B7280'}
              weight={selectedRoute?.id === route.id ? 5 : 3}
              opacity={0.8}
              eventHandlers={{
                click: () => onRouteSelect && onRouteSelect(route)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-semibold text-gray-900 mb-1">{route.name}</h3>
                  <p className="text-sm text-gray-600">{route.description}</p>
                  <div className="mt-2 text-xs text-gray-500">
                    <div>Distance: {route.distance}km</div>
                    <div>Difficulty: {route.difficulty}</div>
                  </div>
                </div>
              </Popup>
            </Polyline>
          ))}
          
          {/* Selected route highlight */}
          {selectedRoute && (
            <Polyline
              positions={selectedRoute.points.map(p => [p.lat, p.lng])}
              color="#3B82F6"
              weight={5}
              opacity={0.9}
            />
          )}
          
          {/* Optimized route */}
          {optimizedRoute && (
            <Polyline
              positions={optimizedRoute.map(p => [p.lat, p.lng])}
              color="#EF4444"
              weight={4}
              opacity={0.8}
              dashArray="10, 10"
            />
          )}
          
          {/* Planning points */}
          {planningPoints.map((point, index) => (
            <Marker
              key={index}
              position={[point.lat, point.lng]}
              icon={createCustomIcon('#F59E0B', index + 1)}
            >
              <Popup>
                <div className="text-center">
                  <strong>Point {index + 1}</strong>
                </div>
              </Popup>
            </Marker>
          ))}
          
          {/* POIs */}
          {showPOIs && pois.map(poi => (
            <Marker
              key={poi.id}
              position={[poi.lat, poi.lng]}
              icon={createCustomIcon('#10B981', poi.icon)}
              eventHandlers={{
                click: () => onPOISelect && onPOISelect(poi)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-semibold text-gray-900 mb-1">{poi.name}</h3>
                  <p className="text-sm text-gray-600">{poi.type}</p>
                  {poi.rating && (
                    <div className="flex items-center mt-1">
                      <span className="text-yellow-400">⭐</span>
                      <span className="text-sm ml-1">{poi.rating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          ))}
          
          {/* Selected POIs highlight */}
          {selectedPOIs.map(poi => (
            <Marker
              key={`selected-${poi.id}`}
              position={[poi.lat, poi.lng]}
              icon={createCustomIcon('#EF4444', poi.icon)}
            />
          ))}
          
          {/* Segments */}
          {(showSegments || selectedSegment) && segments.map(segment => (
            <Polyline
              key={segment.id}
              positions={segment.points.map(point => [point.lat, point.lng])}
              color={selectedSegment?.id === segment.id ? '#3B82F6' : 
                     segment.type === 'climb' ? '#EF4444' :
                     segment.type === 'sprint' ? '#10B981' :
                     segment.type === 'descent' ? '#F59E0B' :
                     '#8B5CF6'}
              weight={selectedSegment?.id === segment.id ? 6 : 4}
              opacity={0.8}
              eventHandlers={{
                click: () => onSegmentSelect && onSegmentSelect(segment)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-semibold text-gray-900 mb-1">{segment.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{segment.description}</p>
                  <div className="space-y-1 text-xs text-gray-500">
                    <div>Distance: {segment.distance < 1000 ? `${segment.distance}m` : `${(segment.distance / 1000).toFixed(1)}km`}</div>
                    <div>Elevation: {segment.elevation > 0 ? `+${segment.elevation}m` : `${segment.elevation}m`}</div>
                    <div>Record: {segment.recordTime}</div>
                    <div>Attempts: {segment.attempts}</div>
                  </div>
                </div>
              </Popup>
            </Polyline>
          ))}
        </MapContainer>
        
        {/* Custom controls overlay */}
        <MapControls
          onStyleChange={setMapStyle}
          currentStyle={mapStyle}
          onLocate={handleLocate}
          onFullscreen={handleFullscreen}
          isFullscreen={isFullscreen}
        />
      </div>
    </div>
  );
};

export default ModernMap;
