import React, { useState, useEffect } from 'react';
import {
  FiTarget,
  FiTrendingUp,
  FiClock,
  FiActivity,
  FiAward,
  FiUsers,
  FiMapPin,
  FiChevronRight,
  FiStar,
  FiFlag
} from 'react-icons/fi';

const SegmentPanel = ({ segments, selectedSegment, onSegmentSelect, userPosition }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [userStats, setUserStats] = useState(null);

  // Générer un classement fictif pour le segment sélectionné
  useEffect(() => {
    if (selectedSegment) {
      generateLeaderboard(selectedSegment);
      generateUserStats(selectedSegment);
    }
  }, [selectedSegment]);

  const generateLeaderboard = (segment) => {
    const mockLeaderboard = [
      {
        rank: 1,
        name: '<PERSON>',
        time: '12:34',
        speed: '18.5 km/h',
        date: '2024-01-15',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        isPR: true
      },
      {
        rank: 2,
        name: '<PERSON>',
        time: '12:45',
        speed: '18.2 km/h',
        date: '2024-01-12',
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        isPR: false
      },
      {
        rank: 3,
        name: 'Thomas Leroy',
        time: '12:58',
        speed: '17.8 km/h',
        date: '2024-01-10',
        avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
        isPR: false
      },
      {
        rank: 4,
        name: 'Marie Dubois',
        time: '13:15',
        speed: '17.2 km/h',
        date: '2024-01-08',
        avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
        isPR: true,
        isCurrentUser: true
      },
      {
        rank: 5,
        name: 'Pierre Moreau',
        time: '13:22',
        speed: '17.0 km/h',
        date: '2024-01-05',
        avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
        isPR: false
      }
    ];
    setLeaderboard(mockLeaderboard);
  };

  const generateUserStats = (segment) => {
    setUserStats({
      personalRecord: '13:15',
      attempts: 8,
      lastAttempt: '2024-01-08',
      averageTime: '13:45',
      improvement: '+12s'
    });
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <span className="text-yellow-500">🥇</span>;
      case 2:
        return <span className="text-gray-400">🥈</span>;
      case 3:
        return <span className="text-orange-600">🥉</span>;
      default:
        return <span className="text-gray-500 font-bold">#{rank}</span>;
    }
  };

  const formatDistance = (distance) => {
    return distance < 1000 ? `${distance}m` : `${(distance / 1000).toFixed(1)}km`;
  };

  if (!selectedSegment) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center text-gray-500">
          <FiTarget className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">Sélectionnez un segment</h3>
          <p className="text-sm">Cliquez sur un segment sur la carte pour voir les classements</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Segment Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold mb-2">{selectedSegment.name}</h2>
            <div className="flex items-center space-x-4 text-blue-100">
              <span className="flex items-center">
                <FiMapPin className="h-4 w-4 mr-1" />
                {formatDistance(selectedSegment.distance)}
              </span>
              <span className="flex items-center">
                <FiTrendingUp className="h-4 w-4 mr-1" />
                {selectedSegment.elevation}m D+
              </span>
              <span className="flex items-center">
                <FiUsers className="h-4 w-4 mr-1" />
                {selectedSegment.attempts || 156} tentatives
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{selectedSegment.difficulty}</div>
            <div className="text-sm text-blue-100">Difficulté</div>
          </div>
        </div>

        {/* User Stats */}
        {userStats && (
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="font-semibold mb-3 flex items-center">
              <FiStar className="h-4 w-4 mr-2" />
              Vos statistiques
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-blue-100">Record personnel</div>
                <div className="font-bold text-lg">{userStats.personalRecord}</div>
              </div>
              <div>
                <div className="text-blue-100">Tentatives</div>
                <div className="font-bold text-lg">{userStats.attempts}</div>
              </div>
              <div>
                <div className="text-blue-100">Dernière fois</div>
                <div className="font-medium">{userStats.lastAttempt}</div>
              </div>
              <div>
                <div className="text-blue-100">Progression</div>
                <div className="font-medium text-green-300">{userStats.improvement}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Leaderboard */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <FiAward className="h-5 w-5 mr-2 text-yellow-500" />
            Classement
          </h3>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            Voir tout
          </button>
        </div>

        <div className="space-y-3">
          {leaderboard.map((entry) => (
            <div
              key={entry.rank}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                entry.isCurrentUser
                  ? 'bg-blue-50 border border-blue-200'
                  : 'hover:bg-gray-50'
              }`}
            >
              {/* Rank */}
              <div className="flex-shrink-0 w-8 text-center">
                {getRankIcon(entry.rank)}
              </div>

              {/* Avatar */}
              <img
                src={entry.avatar}
                alt={entry.name}
                className="w-8 h-8 rounded-full"
              />

              {/* User Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className={`font-medium truncate ${
                    entry.isCurrentUser ? 'text-blue-700' : 'text-gray-900'
                  }`}>
                    {entry.name}
                  </span>
                  {entry.isPR && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                      PR
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-500">{entry.date}</div>
              </div>

              {/* Stats */}
              <div className="text-right">
                <div className="font-bold text-gray-900">{entry.time}</div>
                <div className="text-sm text-gray-500">{entry.speed}</div>
              </div>

              {/* Arrow */}
              <FiChevronRight className="h-4 w-4 text-gray-400" />
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="mt-6 space-y-3">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
            <FiFlag className="h-4 w-4 mr-2" />
            Tenter ce segment
          </button>
          <button className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors">
            Ajouter aux favoris
          </button>
        </div>
      </div>
    </div>
  );
};

export default SegmentPanel;
