import React, { useState } from 'react';
import {
  FiActivity,
  FiMap,
  FiTrendingUp,
  FiUsers,
  FiSearch,
  FiBell,
  FiPlus,
  FiFilter,
  FiGrid,
  FiList
} from 'react-icons/fi';

const StravaHeader = ({ 
  activeView, 
  onViewChange, 
  searchTerm, 
  onSearchChange,
  showFilters,
  onToggleFilters,
  viewMode,
  onViewModeChange 
}) => {
  const [notifications] = useState(3);

  const views = [
    { id: 'feed', label: 'Flux', icon: FiActivity, color: 'text-orange-500' },
    { id: 'explore', label: 'Explorer', icon: FiMap, color: 'text-blue-500' },
    { id: 'segments', label: 'Segments', icon: FiTrendingUp, color: 'text-red-500' },
    { id: 'athletes', label: 'Athlètes', icon: FiUsers, color: 'text-green-500' }
  ];

  return (
    <div className="bg-white border-b border-gray-200 sticky top-0 z-50">
      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo & Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <FiActivity className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">FitTracker</span>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher des parcours, athlètes, segments..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-3">
            <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
              <FiBell className="h-5 w-5" />
              {notifications > 0 && (
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notifications}
                </span>
              )}
            </button>
            
            <button className="bg-orange-500 text-white px-4 py-2 rounded-full hover:bg-orange-600 transition-colors flex items-center space-x-2">
              <FiPlus className="h-4 w-4" />
              <span className="hidden sm:inline">Enregistrer</span>
            </button>

            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex items-center justify-between border-t border-gray-100 pt-4 pb-4">
          <div className="flex space-x-1">
            {views.map((view) => (
              <button
                key={view.id}
                onClick={() => onViewChange(view.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all ${
                  activeView === view.id
                    ? 'bg-orange-50 text-orange-600 border border-orange-200'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <view.icon className={`h-4 w-4 ${activeView === view.id ? view.color : ''}`} />
                <span>{view.label}</span>
              </button>
            ))}
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onToggleFilters}
              className={`p-2 rounded-lg transition-colors ${
                showFilters 
                  ? 'bg-blue-50 text-blue-600' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <FiFilter className="h-4 w-4" />
            </button>

            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => onViewModeChange('grid')}
                className={`p-1 rounded transition-colors ${
                  viewMode === 'grid' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiGrid className="h-4 w-4" />
              </button>
              <button
                onClick={() => onViewModeChange('list')}
                className={`p-1 rounded transition-colors ${
                  viewMode === 'list' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiList className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StravaHeader;
