import React, { createContext, useContext, useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock user data for demonstration
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'demo123',
    firstName: 'Marie',
    lastName: 'Dubois',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    dateOfBirth: '1990-05-15',
    gender: 'female',
    height: 165,
    weight: 60,
    fitnessLevel: 'intermediate',
    goals: ['weight_loss', 'endurance'],
    joinDate: '2023-01-15',
    preferences: {
      units: 'metric',
      language: 'fr',
      notifications: true,
      privacy: 'friends'
    },
    stats: {
      totalWorkouts: 156,
      totalDistance: 1250.5,
      totalTime: 8940, // minutes
      currentStreak: 12,
      longestStreak: 28,
      achievements: ['first_5k', 'month_streak', 'distance_100k']
    }
  }
];

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = localStorage.getItem('fittracker_user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        localStorage.removeItem('fittracker_user');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (email, password) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user in mock data
      const foundUser = mockUsers.find(u => u.email === email && u.password === password);
      
      if (!foundUser) {
        throw new Error('Email ou mot de passe incorrect');
      }

      // Remove password from user object
      const { password: _, ...userWithoutPassword } = foundUser;
      
      setUser(userWithoutPassword);
      localStorage.setItem('fittracker_user', JSON.stringify(userWithoutPassword));
      
      return userWithoutPassword;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('Un compte avec cet email existe déjà');
      }

      // Create new user
      const newUser = {
        id: uuidv4(),
        ...userData,
        joinDate: new Date().toISOString().split('T')[0],
        avatar: `https://randomuser.me/api/portraits/${userData.gender === 'female' ? 'women' : 'men'}/${Math.floor(Math.random() * 50)}.jpg`,
        preferences: {
          units: 'metric',
          language: 'fr',
          notifications: true,
          privacy: 'friends'
        },
        stats: {
          totalWorkouts: 0,
          totalDistance: 0,
          totalTime: 0,
          currentStreak: 0,
          longestStreak: 0,
          achievements: []
        }
      };

      // Add to mock users (in real app, this would be an API call)
      mockUsers.push(newUser);

      // Remove password from user object
      const { password: _, ...userWithoutPassword } = newUser;
      
      setUser(userWithoutPassword);
      localStorage.setItem('fittracker_user', JSON.stringify(userWithoutPassword));
      
      return userWithoutPassword;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    setUser(null);
    localStorage.removeItem('fittracker_user');
  };

  // Update user profile
  const updateProfile = async (updates) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('fittracker_user', JSON.stringify(updatedUser));
      
      return updatedUser;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user stats
  const updateStats = async (statUpdates) => {
    if (!user) return;

    try {
      const updatedStats = { ...user.stats, ...statUpdates };
      const updatedUser = { ...user, stats: updatedStats };
      
      setUser(updatedUser);
      localStorage.setItem('fittracker_user', JSON.stringify(updatedUser));
      
      return updatedUser;
    } catch (error) {
      console.error('Error updating stats:', error);
    }
  };

  // Check if user is authenticated
  const isAuthenticated = !!user;

  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    updateStats,
    isAuthenticated,
    setError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
