@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

/* Modern Map Styles */
.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #f8fafc;
}

.leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 0;
}

.leaflet-popup-content {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

.leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  font-size: 11px;
  padding: 4px 8px;
  margin: 8px;
}

.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* Smooth animations for map interactions */
.leaflet-zoom-anim .leaflet-zoom-animated {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern polyline styles */
.leaflet-interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.leaflet-interactive:hover {
  filter: brightness(1.1);
}

/* Custom control styles */
.leaflet-control {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Hide default zoom controls */
.leaflet-control-zoom {
  display: none;
}

/* Fullscreen styles */
.leaflet-container:-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
}

.leaflet-container:-moz-full-screen {
  width: 100vw !important;
  height: 100vh !important;
}

.leaflet-container:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
  h1 {
    @apply text-3xl font-bold;
  }
  h2 {
    @apply text-2xl font-bold;
  }
  h3 {
    @apply text-xl font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  .btn-primary {
    @apply bg-primary text-white hover:bg-blue-600;
  }
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-orange-600;
  }
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  .input {
    @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary;
  }
}
