@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

/* Modern Map Styles */
.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #f8fafc;
}

.leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 0;
}

.leaflet-popup-content {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

.leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  font-size: 11px;
  padding: 4px 8px;
  margin: 8px;
}

.custom-marker, .strava-marker {
  background: transparent !important;
  border: none !important;
}

/* Strava-style marker animations */
.strava-marker:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Strava-style popup */
.strava-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  border: none;
  padding: 0;
  overflow: hidden;
}

.strava-popup .leaflet-popup-content {
  margin: 0;
  border-radius: 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.strava-popup .leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.strava-popup .leaflet-popup-close-button {
  color: #6B7280;
  font-size: 18px;
  font-weight: bold;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.strava-popup .leaflet-popup-close-button:hover {
  background: #F3F4F6;
  color: #374151;
}

/* Smooth animations for map interactions */
.leaflet-zoom-anim .leaflet-zoom-animated {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern polyline styles */
.leaflet-interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.leaflet-interactive:hover {
  filter: brightness(1.1);
}

/* Custom control styles */
.leaflet-control {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Hide default zoom controls */
.leaflet-control-zoom {
  display: none;
}

/* Fullscreen styles */
.leaflet-container:-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
}

.leaflet-container:-moz-full-screen {
  width: 100vw !important;
  height: 100vh !important;
}

.leaflet-container:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
  h1 {
    @apply text-3xl font-bold;
  }
  h2 {
    @apply text-2xl font-bold;
  }
  h3 {
    @apply text-xl font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  .btn-primary {
    @apply bg-primary text-white hover:bg-blue-600;
  }
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-orange-600;
  }
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  .input {
    @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary;
  }
}

/* Modern Strava-style Enhancements */

/* Advanced marker animations and effects */
.modern-poi-marker,
.segment-marker,
.user-location-marker {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-poi-marker:hover,
.segment-marker:hover,
.user-location-marker:hover {
  transform: scale(1.15) translateZ(0);
}

.modern-poi-marker.selected {
  animation: selectedPulse 2s infinite;
}

/* User location pulse animation */
@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),
                0 3px 10px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.3);
  }
  50% {
    box-shadow: 0 8px 25px rgba(252, 76, 2, 0.5),
                0 3px 10px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.3),
                0 0 0 10px rgba(252, 76, 2, 0.1);
  }
  100% {
    box-shadow: 0 8px 25px rgba(252, 76, 2, 0.3),
                0 3px 10px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.3);
  }
}

/* Selected POI pulse animation */
@keyframes selectedPulse {
  0% {
    box-shadow: 0 16px 32px rgba(0,0,0,0.2),
                0 8px 16px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.3);
  }
  50% {
    box-shadow: 0 16px 32px rgba(0,0,0,0.2),
                0 8px 16px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.3),
                0 0 0 8px rgba(0, 115, 230, 0.2);
  }
  100% {
    box-shadow: 0 16px 32px rgba(0,0,0,0.2),
                0 8px 16px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.3);
  }
}

/* Animated route effect */
.animated-route {
  animation: dashMove 2s linear infinite;
}

@keyframes dashMove {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20;
  }
}

/* Enhanced popup styling */
.strava-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.15),
              0 8px 25px rgba(0,0,0,0.1),
              0 0 0 1px rgba(0,0,0,0.05);
  border: none;
  padding: 0;
  overflow: hidden;
}

.strava-popup .leaflet-popup-close-button {
  color: #6B7280;
  font-size: 20px;
  font-weight: bold;
  right: 12px;
  top: 12px;
  width: 28px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  border-radius: 50%;
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0,0,0,0.1);
}

.strava-popup .leaflet-popup-close-button:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #EF4444;
  transform: scale(1.1);
  border-color: rgba(239, 68, 68, 0.2);
}

/* Modern popup content styling */
.modern-popup {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.5;
}

/* Enhanced zoom controls */
.leaflet-control-zoom a {
  background: white !important;
  border: none !important;
  color: #374151 !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  border-radius: 8px !important;
}

.leaflet-control-zoom a:hover {
  background: #FC4C02 !important;
  color: white !important;
  transform: scale(1.05);
}

/* POI cluster markers */
.poi-cluster-marker:hover {
  transform: translate(-50%, -50%) scale(1.1) !important;
  box-shadow: 0 12px 35px rgba(0,0,0,0.3),
              0 6px 15px rgba(0,0,0,0.15) !important;
}

/* Layer control styling */
.layer-control {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.layer-control input[type="checkbox"]:checked {
  background-color: #FC4C02;
  border-color: #FC4C02;
}

.layer-control input[type="checkbox"]:focus {
  ring-color: rgba(252, 76, 2, 0.5);
}

/* Smooth transitions for all map elements */
.leaflet-marker-icon {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduce visual noise at different zoom levels */
.zoom-fade-out {
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.zoom-fade-in {
  opacity: 1;
  transition: opacity 0.3s ease;
}
