import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, FiUsers, FiTrendingUp, FiAward } from 'react-icons/fi';

const About = () => {
  const stats = [
    {
      icon: <FiUsers className="h-8 w-8" />,
      number: '50,000+',
      label: 'Utilisateurs actifs',
      description: 'Rejoignez notre communauté grandissante'
    },
    {
      icon: <FiTrendingUp className="h-8 w-8" />,
      number: '2M+',
      label: 'Entraînements complétés',
      description: 'Des millions de séances réussies'
    },
    {
      icon: <FiAward className="h-8 w-8" />,
      number: '500+',
      label: 'Exercices disponibles',
      description: 'Une bibliothèque complète et variée'
    },
    {
      icon: <FiHeart className="h-8 w-8" />,
      number: '98%',
      label: 'Satisfaction utilisateur',
      description: 'Des utilisateurs qui nous recommandent'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: '<PERSON>onda<PERSON><PERSON> & CEO',
      description: 'Ancienne athlète olympique, passionnée par la démocratisation du fitness.',
      image: 'https://randomuser.me/api/portraits/women/3.jpg'
    },
    {
      name: 'Thomas Martin',
      role: 'CTO',
      description: 'Expert en développement d\'applications mobiles et intelligence artificielle.',
      image: 'https://randomuser.me/api/portraits/men/2.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Coach en chef',
      description: 'Certifiée NASM, spécialisée dans la création de programmes personnalisés.',
      image: 'https://randomuser.me/api/portraits/women/4.jpg'
    },
    {
      name: 'Alexandre Petit',
      role: 'Designer UX/UI',
      description: 'Créateur d\'expériences utilisateur intuitives et motivantes.',
      image: 'https://randomuser.me/api/portraits/men/3.jpg'
    }
  ];

  const values = [
    {
      title: 'Accessibilité',
      description: 'Le fitness doit être accessible à tous, quel que soit le niveau ou l\'équipement disponible.',
      icon: '🌟'
    },
    {
      title: 'Innovation',
      description: 'Nous utilisons les dernières technologies pour créer des expériences d\'entraînement uniques.',
      icon: '🚀'
    },
    {
      title: 'Communauté',
      description: 'Nous croyons en la force de la communauté pour maintenir la motivation et atteindre les objectifs.',
      icon: '🤝'
    },
    {
      title: 'Bien-être',
      description: 'Notre approche holistique considère le bien-être physique et mental comme indissociables.',
      icon: '💚'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              À propos de FitTracker
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              Notre mission est de rendre le fitness accessible, motivant et efficace pour tous, 
              grâce à la technologie et à une communauté bienveillante.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Notre histoire
              </h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  FitTracker est né en 2023 de la frustration de notre fondatrice, Marie Dubois, 
                  ancienne athlète olympique, face au manque d'outils fitness vraiment adaptés 
                  aux besoins des utilisateurs modernes.
                </p>
                <p>
                  Après avoir testé des dizaines d'applications existantes, elle a réalisé qu'aucune 
                  ne combinait efficacement la simplicité d'utilisation, la personnalisation avancée 
                  et l'aspect communautaire nécessaires pour maintenir la motivation à long terme.
                </p>
                <p>
                  Avec une équipe passionnée de développeurs, designers et coachs sportifs, 
                  nous avons créé FitTracker : une plateforme qui s'adapte à votre vie, 
                  pas l'inverse.
                </p>
              </div>
            </div>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Équipe FitTracker"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              FitTracker en chiffres
            </h2>
            <p className="text-xl text-gray-600">
              Des résultats qui parlent d'eux-mêmes
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
                <div className="text-primary mb-4 flex justify-center">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-lg font-semibold text-gray-700 mb-2">
                  {stat.label}
                </div>
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Nos valeurs
            </h2>
            <p className="text-xl text-gray-600">
              Les principes qui guident notre développement
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="text-4xl">{value.icon}</div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {value.title}
                  </h3>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Notre équipe
            </h2>
            <p className="text-xl text-gray-600">
              Les experts passionnés derrière FitTracker
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
                <img 
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4"
                />
                <h3 className="text-xl font-semibold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-primary font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-600 text-sm">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-secondary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Rejoignez l'aventure FitTracker
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Que vous soyez débutant ou athlète confirmé, FitTracker s'adapte à vos besoins 
            pour vous accompagner vers vos objectifs fitness.
          </p>
          <button className="bg-white text-secondary font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors">
            Commencer gratuitement
          </button>
        </div>
      </section>
    </div>
  );
};

export default About;
