import React from 'react';
import { Link } from 'react-router-dom';
import {
  FiActivity,
  FiClock,
  FiTrendingUp,
  FiTarget,
  FiAward,
  FiMapPin,
  FiUsers
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();

  if (!user) return null;

  const quickStats = [
    {
      label: 'Entraînements totaux',
      value: user.stats.totalWorkouts,
      icon: <FiActivity className="h-6 w-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      label: 'Distance totale',
      value: `${user.stats.totalDistance.toFixed(1)} km`,
      icon: <FiMapPin className="h-6 w-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      label: 'Temps total',
      value: `${Math.floor(user.stats.totalTime / 60)}h ${user.stats.totalTime % 60}m`,
      icon: <FiClock className="h-6 w-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      label: 'Série actuelle',
      value: `${user.stats.currentStreak} jours`,
      icon: <FiTrendingUp className="h-6 w-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  const quickActions = [
    {
      title: 'Bibliothèque d\'exercices',
      description: 'Explorez notre collection complète',
      icon: <FiActivity className="h-8 w-8" />,
      link: '/exercises',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: 'Parcours et cartes',
      description: 'Découvrez de nouveaux itinéraires',
      icon: <FiMapPin className="h-8 w-8" />,
      link: '/routes',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: 'Communauté sociale',
      description: 'Connectez-vous avec d\'autres athlètes',
      icon: <FiUsers className="h-8 w-8" />,
      link: '/social',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: 'Démarrer un entraînement',
      description: 'Utilisez le minuteur pour votre session',
      icon: <FiClock className="h-8 w-8" />,
      link: '/timer',
      color: 'bg-orange-600 hover:bg-orange-700'
    },
    {
      title: 'Parcourir les programmes',
      description: 'Découvrez de nouveaux programmes',
      icon: <FiTarget className="h-8 w-8" />,
      link: '/programs',
      color: 'bg-teal-600 hover:bg-teal-700'
    },
    {
      title: 'Prédiction de performance',
      description: 'Analysez vos temps prédits',
      icon: <FiAward className="h-8 w-8" />,
      link: '/performance',
      color: 'bg-indigo-600 hover:bg-indigo-700'
    }
  ];

  const recentAchievements = user.stats.achievements.slice(-3).map(achievement => {
    const achievementData = {
      first_5k: { name: 'Premier 5K', description: 'Félicitations pour votre premier 5K !', icon: '🏃‍♀️' },
      month_streak: { name: 'Série d\'un mois', description: '30 jours consécutifs d\'entraînement', icon: '🔥' },
      distance_100k: { name: '100km parcourus', description: 'Vous avez parcouru 100km au total', icon: '🎯' }
    };
    return achievementData[achievement] || { name: achievement, description: '', icon: '🏆' };
  });

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <img
              src={user.avatar}
              alt={`${user.firstName} ${user.lastName}`}
              className="h-16 w-16 rounded-full object-cover"
            />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Bonjour, {user.firstName} !
              </h1>
              <p className="text-gray-600">
                Prêt pour votre entraînement d'aujourd'hui ?
              </p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor} ${stat.color}`}>
                  {stat.icon}
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Actions rapides</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className={`${action.color} text-white rounded-lg p-6 transition-colors duration-200`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{action.title}</h3>
                      <p className="text-sm opacity-90">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Achievements & Activity */}
          <div className="space-y-8">
            {/* Recent Achievements */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiAward className="h-5 w-5 mr-2 text-yellow-500" />
                Derniers succès
              </h3>
              <div className="space-y-3">
                {recentAchievements.length > 0 ? (
                  recentAchievements.map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <span className="text-2xl">{achievement.icon}</span>
                      <div>
                        <p className="font-medium text-gray-900">{achievement.name}</p>
                        <p className="text-sm text-gray-600">{achievement.description}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">
                    Commencez à vous entraîner pour débloquer des succès !
                  </p>
                )}
              </div>
            </div>

            {/* Weekly Goal */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiTarget className="h-5 w-5 mr-2 text-green-500" />
                Objectif hebdomadaire
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Entraînements cette semaine</span>
                  <span className="font-medium">3/5</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                </div>
                <p className="text-sm text-gray-600">
                  Plus que 2 entraînements pour atteindre votre objectif !
                </p>
              </div>
            </div>

            {/* Social Activity */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiUsers className="h-5 w-5 mr-2 text-blue-500" />
                Activité sociale
              </h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <img
                    src="https://randomuser.me/api/portraits/men/1.jpg"
                    alt="Ami"
                    className="h-8 w-8 rounded-full"
                  />
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">Thomas</span> a terminé un 10K
                    </p>
                    <p className="text-xs text-gray-500">Il y a 2 heures</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <img
                    src="https://randomuser.me/api/portraits/women/2.jpg"
                    alt="Ami"
                    className="h-8 w-8 rounded-full"
                  />
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">Sophie</span> a battu son record personnel
                    </p>
                    <p className="text-xs text-gray-500">Il y a 5 heures</p>
                  </div>
                </div>
                <Link
                  to="/social"
                  className="block text-center text-sm text-primary hover:text-blue-700 font-medium mt-4"
                >
                  Voir toute l'activité
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
