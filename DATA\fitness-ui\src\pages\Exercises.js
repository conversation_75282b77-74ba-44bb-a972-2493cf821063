import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>lay,
  FiClock,
  FiZap,
  FiFilter,
  FiSearch,
  FiTarget,
  FiHeart,
  FiStar,
  FiInfo,
  FiPlus,
  FiTrendingUp,
  FiBookmark,
  FiVideo,
  FiBarChart2,
  FiCalendar,
  FiX,

} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import {
  exercises,
  exerciseCategories,
  muscleGroups,
  equipmentTypes,
  getDifficultyColor,
  getRecommendedExercises,
  searchExercises,
  createWorkoutPlan,
  toggleFavorite,
  loadFavorites,
  addToHistory,
  getExerciseHistory,
  calculateProgressStats,
  createCustomProgram,
  getExerciseVideo
} from '../utils/exerciseUtils';

const Exercises = () => {
  const { user } = useAuth();

  const [selectedExercise, setSelectedExercise] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: 'all',
    muscleGroup: 'all',
    equipment: 'all',
    difficulty: 'all',
    maxDuration: null
  });
  const [activeTab, setActiveTab] = useState('all');
  const [recommendedExercises, setRecommendedExercises] = useState([]);
  const [favoriteExercises, setFavoriteExercises] = useState([]);
  const [filteredExercises, setFilteredExercises] = useState(exercises);

  // Nouveaux états pour les fonctionnalités avancées
  const [exerciseHistory, setExerciseHistory] = useState([]);
  const [showCreateProgram, setShowCreateProgram] = useState(false);
  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [progressData, setProgressData] = useState(null);
  const [showPerformanceModal, setShowPerformanceModal] = useState(false);
  const [performanceData, setPerformanceData] = useState({
    sets: 3,
    reps: 12,
    weight: '',
    duration: '',
    notes: ''
  });
  const [exerciseRatings, setExerciseRatings] = useState({});
  const [exerciseComments, setExerciseComments] = useState({});
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [ratingExercise, setRatingExercise] = useState(null);

  useEffect(() => {
    // Générer des recommandations basées sur le profil utilisateur
    if (user) {
      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');
      setRecommendedExercises(recommendations);
    }

    // Charger les favoris depuis localStorage
    const savedFavorites = loadFavorites();
    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));
    setFavoriteExercises(favoriteExerciseObjects);

    // Charger l'historique des exercices
    const history = getExerciseHistory();
    setExerciseHistory(history);

    // Charger les notes et commentaires
    try {
      const savedRatings = JSON.parse(localStorage.getItem('exerciseRatings') || '{}');
      const savedComments = JSON.parse(localStorage.getItem('exerciseComments') || '{}');
      setExerciseRatings(savedRatings);
      setExerciseComments(savedComments);
    } catch (error) {
      console.error('Erreur lors du chargement des notes:', error);
    }
  }, [user]);

  useEffect(() => {
    // Appliquer les filtres et la recherche
    let results = exercises;
    
    if (activeTab === 'recommended') {
      results = recommendedExercises;
    } else if (activeTab === 'favorites') {
      results = favoriteExercises;
    }
    
    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {
      results = searchExercises(searchQuery, filters);
    }
    
    setFilteredExercises(results);
  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      muscleGroup: 'all',
      equipment: 'all',
      difficulty: 'all',
      maxDuration: null
    });
    setSearchQuery('');
  };

  const generateQuickWorkout = () => {
    const selectedExercises = filteredExercises.slice(0, 4);
    const workout = createWorkoutPlan(selectedExercises, 30);
    
    // Ici on pourrait ouvrir une modal avec le programme généré
    alert(`Programme généré !\nDurée: ${workout.totalDuration} min\nCalories estimées: ${workout.estimatedCalories}\nExercices: ${workout.exercises.length}`);
  };

  const handleToggleFavorite = (exerciseId) => {
    const savedFavorites = loadFavorites();
    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});

    // Mettre à jour l'état local
    if (isNowFavorite) {
      const exercise = exercises.find(ex => ex.id === exerciseId);
      setFavoriteExercises(prev => [...prev, exercise]);
    } else {
      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));
    }
  };

  // Ajouter un exercice à l'historique avec performance
  const handleAddToHistory = (exercise) => {
    const entry = addToHistory(exercise, performanceData);
    if (entry) {
      setExerciseHistory(prev => [entry, ...prev]);
      setShowPerformanceModal(false);
      setPerformanceData({
        sets: 3,
        reps: 12,
        weight: '',
        duration: '',
        notes: ''
      });
      alert('Exercice ajouté à votre historique !');
    }
  };

  // Afficher les statistiques de progression
  const handleShowProgress = (exerciseId) => {
    const stats = calculateProgressStats(exerciseId);
    setProgressData(stats);
    setShowProgressModal(true);
  };

  // Ajouter/retirer un exercice de la sélection pour programme
  const handleToggleExerciseSelection = (exercise) => {
    setSelectedExercisesForProgram(prev => {
      const isSelected = prev.some(ex => ex.id === exercise.id);
      if (isSelected) {
        return prev.filter(ex => ex.id !== exercise.id);
      } else {
        return [...prev, exercise];
      }
    });
  };

  // Créer un programme personnalisé
  const handleCreateProgram = (programName) => {
    if (selectedExercisesForProgram.length === 0) {
      alert('Veuillez sélectionner au moins un exercice');
      return;
    }

    const program = createCustomProgram(programName, selectedExercisesForProgram);
    setSelectedExercisesForProgram([]);
    setShowCreateProgram(false);
    alert(`Programme "${program.name}" créé avec succès !`);
  };

  const rateExercise = (exerciseId, rating, comment = '') => {
    const updatedRatings = { ...exerciseRatings, [exerciseId]: rating };
    const updatedComments = { ...exerciseComments, [exerciseId]: comment };

    setExerciseRatings(updatedRatings);
    setExerciseComments(updatedComments);

    localStorage.setItem('exerciseRatings', JSON.stringify(updatedRatings));
    localStorage.setItem('exerciseComments', JSON.stringify(updatedComments));

    setShowRatingModal(false);
    setRatingExercise(null);
  };

  const openRatingModal = (exercise) => {
    setRatingExercise(exercise);
    setShowRatingModal(true);
  };

  const getAverageRating = (exerciseId) => {
    return exerciseRatings[exerciseId] || 0;
  };

  const renderStars = (rating, interactive = false, onRate = null) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            onClick={() => interactive && onRate && onRate(star)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
            disabled={!interactive}
          >
            <FiStar
              className={`h-4 w-4 ${
                star <= rating
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Bibliothèque d'Exercices
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Découvrez notre collection complète d'exercices avec instructions détaillées, 
            recommandations personnalisées et programmes adaptés à vos objectifs.
          </p>
        </div>

        {/* Search and Quick Actions */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un exercice, groupe musculaire..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${
                  showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <FiFilter className="h-4 w-4 mr-2" />
                Filtres
              </button>
              <button
                onClick={generateQuickWorkout}
                className="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
              >
                <FiTarget className="h-4 w-4 mr-2" />
                Programme rapide
              </button>
              <button
                onClick={() => setShowCreateProgram(true)}
                className="px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Créer programme
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className="px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center"
              >
                <FiCalendar className="h-4 w-4 mr-2" />
                Historique
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Catégorie</label>
                  <select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Toutes les catégories</option>
                    {Object.entries(exerciseCategories).map(([key, category]) => (
                      <option key={key} value={key}>{category.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Groupe musculaire</label>
                  <select
                    value={filters.muscleGroup}
                    onChange={(e) => handleFilterChange('muscleGroup', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Tous les groupes</option>
                    {Object.entries(muscleGroups).map(([key, group]) => (
                      <option key={key} value={key}>{group.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Équipement</label>
                  <select
                    value={filters.equipment}
                    onChange={(e) => handleFilterChange('equipment', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Tout équipement</option>
                    {Object.entries(equipmentTypes).map(([key, equipment]) => (
                      <option key={key} value={key}>{equipment.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Difficulté</label>
                  <select
                    value={filters.difficulty}
                    onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Toutes difficultés</option>
                    <option value="Facile">Facile</option>
                    <option value="Modéré">Modéré</option>
                    <option value="Difficile">Difficile</option>
                  </select>
                </div>
              </div>
              
              <div className="mt-4 flex justify-between items-center">
                <div className="flex items-center space-x-4">
                  <label className="block text-sm font-medium text-gray-700">Durée max (min):</label>
                  <input
                    type="number"
                    value={filters.maxDuration || ''}
                    onChange={(e) => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null)}
                    placeholder="60"
                    className="w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Effacer les filtres
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8">
          <button
            onClick={() => setActiveTab('all')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'all'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Tous ({exercises.length})
          </button>
          {user && (
            <>
              <button
                onClick={() => setActiveTab('recommended')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'recommended'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiStar className="inline mr-2" />
                Recommandés ({recommendedExercises.length})
              </button>
              <button
                onClick={() => setActiveTab('favorites')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'favorites'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiHeart className="inline mr-2" />
                Favoris ({favoriteExercises.length})
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'history'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiCalendar className="inline mr-2" />
                Historique ({exerciseHistory.length})
              </button>
            </>
          )}
        </div>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredExercises.length} exercice{filteredExercises.length > 1 ? 's' : ''} trouvé{filteredExercises.length > 1 ? 's' : ''}
            {searchQuery && ` pour "${searchQuery}"`}
          </p>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'history' ? (
          /* Exercise History */
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Historique des exercices</h2>
            {exerciseHistory.length === 0 ? (
              <div className="text-center py-12">
                <FiCalendar className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun exercice dans l'historique</h3>
                <p className="text-gray-600">Commencez un exercice pour voir votre progression ici.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {exerciseHistory.slice(0, 10).map(entry => (
                  <div key={entry.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{entry.exerciseName}</h3>
                      <span className="text-sm text-gray-500">
                        {new Date(entry.date).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Séries:</span>
                        <span className="ml-2 font-medium">{entry.performance.sets}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Répétitions:</span>
                        <span className="ml-2 font-medium">{entry.performance.reps}</span>
                      </div>
                      {entry.performance.weight && (
                        <div>
                          <span className="text-gray-600">Poids:</span>
                          <span className="ml-2 font-medium">{entry.performance.weight} kg</span>
                        </div>
                      )}
                      {entry.performance.duration && (
                        <div>
                          <span className="text-gray-600">Durée:</span>
                          <span className="ml-2 font-medium">{entry.performance.duration} min</span>
                        </div>
                      )}
                    </div>
                    {entry.performance.notes && (
                      <div className="mt-2 text-sm text-gray-600">
                        <span className="font-medium">Notes:</span> {entry.performance.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          /* Exercise Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {(activeTab === 'all' ? filteredExercises :
              activeTab === 'recommended' ? recommendedExercises :
              favoriteExercises).map(exercise => {
            const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);

            return (
              <div
                key={exercise.id}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {exercise.name}
                      </h3>
                      <div className="flex items-center space-x-2 mb-3">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                          {exercise.difficulty}
                        </span>
                        <span className="text-xs text-gray-500">
                          {exerciseCategories[exercise.category]?.icon} {exerciseCategories[exercise.category]?.name}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShowProgress(exercise.id);
                        }}
                        className="p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors"
                        title="Voir les statistiques"
                      >
                        <FiBarChart2 className="h-5 w-5" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleFavorite(exercise.id);
                        }}
                        className={`p-2 rounded-full transition-colors ${
                          isFavorite
                            ? 'text-red-500 hover:text-red-600'
                            : 'text-gray-400 hover:text-red-500'
                        }`}
                        title={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                      >
                        <FiHeart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
                      </button>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {exercise.description}
                  </p>

                  {/* Rating and Comments */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {renderStars(getAverageRating(exercise.id))}
                      <span className="text-sm text-gray-500">
                        ({getAverageRating(exercise.id) > 0 ? getAverageRating(exercise.id).toFixed(1) : 'Non noté'})
                      </span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        openRatingModal(exercise);
                      }}
                      className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      Noter
                    </button>
                  </div>

                  {/* User Comment Preview */}
                  {exerciseComments[exercise.id] && (
                    <div className="mb-3 p-2 bg-gray-50 rounded text-sm text-gray-700">
                      <span className="font-medium">Votre note: </span>
                      {exerciseComments[exercise.id].length > 50
                        ? `${exerciseComments[exercise.id].substring(0, 50)}...`
                        : exerciseComments[exercise.id]
                      }
                    </div>
                  )}

                  {/* Muscle Groups */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {exercise.muscleGroups.map(mg => (
                      <span key={mg} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        {muscleGroups[mg]?.name}
                      </span>
                    ))}
                  </div>

                  {/* Equipment */}
                  <div className="flex items-center mb-4 text-sm text-gray-600">
                    <span className="mr-2">{equipmentTypes[exercise.equipment]?.icon}</span>
                    <span>{equipmentTypes[exercise.equipment]?.name}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <FiClock className="h-4 w-4 mr-1" />
                      {exercise.duration} min
                    </div>
                    <div className="flex items-center">
                      <FiZap className="h-4 w-4 mr-1" />
                      {exercise.calories} cal
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => setSelectedExercise(exercise)}
                        className="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm"
                      >
                        <FiInfo className="h-4 w-4 mr-1" />
                        Détails
                      </button>
                      <button
                        onClick={() => {
                          setSelectedExercise(exercise);
                          setShowPerformanceModal(true);
                        }}
                        className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm"
                      >
                        <FiPlay className="h-4 w-4 mr-1" />
                        Commencer
                      </button>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleExerciseSelection(exercise);
                        }}
                        className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center text-sm ${
                          selectedExercisesForProgram.some(ex => ex.id === exercise.id)
                            ? 'bg-purple-100 text-purple-700 border border-purple-300'
                            : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        <FiBookmark className="h-4 w-4 mr-1" />
                        {selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'Sélectionné' : 'Sélectionner'}
                      </button>
                      {getExerciseVideo(exercise.id) && (
                        <button
                          onClick={() => window.open(getExerciseVideo(exercise.id), '_blank')}
                          className="flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center text-sm"
                        >
                          <FiVideo className="h-4 w-4 mr-1" />
                          Vidéo
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          </div>
        )}

        {/* Empty State */}
        {filteredExercises.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <FiSearch className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucun exercice trouvé
            </h3>
            <p className="text-gray-600 mb-4">
              Essayez de modifier vos critères de recherche ou vos filtres.
            </p>
            <button
              onClick={clearFilters}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Effacer les filtres
            </button>
          </div>
        )}

        {/* Performance Modal */}
        {showPerformanceModal && selectedExercise && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    Enregistrer la performance
                  </h2>
                  <button
                    onClick={() => setShowPerformanceModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nombre de séries
                    </label>
                    <input
                      type="number"
                      value={performanceData.sets}
                      onChange={(e) => setPerformanceData(prev => ({...prev, sets: parseInt(e.target.value) || 0}))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Répétitions par série
                    </label>
                    <input
                      type="number"
                      value={performanceData.reps}
                      onChange={(e) => setPerformanceData(prev => ({...prev, reps: parseInt(e.target.value) || 0}))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Poids (kg) - optionnel
                    </label>
                    <input
                      type="number"
                      value={performanceData.weight}
                      onChange={(e) => setPerformanceData(prev => ({...prev, weight: e.target.value}))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: 20"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Durée (min) - optionnel
                    </label>
                    <input
                      type="number"
                      value={performanceData.duration}
                      onChange={(e) => setPerformanceData(prev => ({...prev, duration: e.target.value}))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: 15"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notes - optionnel
                    </label>
                    <textarea
                      value={performanceData.notes}
                      onChange={(e) => setPerformanceData(prev => ({...prev, notes: e.target.value}))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows="3"
                      placeholder="Comment vous êtes-vous senti ?"
                    />
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => setShowPerformanceModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={() => handleAddToHistory(selectedExercise)}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Enregistrer
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Modal */}
        {showProgressModal && progressData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-lg w-full">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    Statistiques de progression
                  </h2>
                  <button
                    onClick={() => setShowProgressModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{progressData.totalSessions}</div>
                      <div className="text-sm text-gray-600">Sessions totales</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {progressData.lastPerformed ?
                          new Date(progressData.lastPerformed).toLocaleDateString('fr-FR') :
                          'Jamais'
                        }
                      </div>
                      <div className="text-sm text-gray-600">Dernière fois</div>
                    </div>
                  </div>

                  {progressData.bestPerformance && (
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">Meilleure performance</h3>
                      <div className="text-sm text-gray-600">
                        {progressData.bestPerformance.performance.sets} séries × {progressData.bestPerformance.performance.reps} reps
                        {progressData.bestPerformance.performance.weight &&
                          ` à ${progressData.bestPerformance.performance.weight} kg`
                        }
                      </div>
                    </div>
                  )}

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 mb-2">Tendance</h3>
                    <div className={`flex items-center ${
                      progressData.progressTrend === 'improving' ? 'text-green-600' :
                      progressData.progressTrend === 'declining' ? 'text-red-600' :
                      'text-gray-600'
                    }`}>
                      <FiTrendingUp className="h-4 w-4 mr-2" />
                      {progressData.progressTrend === 'improving' ? 'En progression' :
                       progressData.progressTrend === 'declining' ? 'En baisse' :
                       'Stable'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Create Program Modal */}
        {showCreateProgram && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    Créer un programme
                  </h2>
                  <button
                    onClick={() => setShowCreateProgram(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>

                <div className="mb-4">
                  <p className="text-gray-600 mb-4">
                    {selectedExercisesForProgram.length} exercice(s) sélectionné(s)
                  </p>

                  {selectedExercisesForProgram.length > 0 && (
                    <div className="space-y-2 mb-4 max-h-40 overflow-y-auto">
                      {selectedExercisesForProgram.map(exercise => (
                        <div key={exercise.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <span className="text-sm">{exercise.name}</span>
                          <button
                            onClick={() => handleToggleExerciseSelection(exercise)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <FiX className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <input
                    type="text"
                    placeholder="Nom du programme"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateProgram(e.target.value);
                      }
                    }}
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={() => setShowCreateProgram(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={(e) => {
                      const input = e.target.parentElement.parentElement.querySelector('input');
                      handleCreateProgram(input.value);
                    }}
                    className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    Créer
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Exercise Detail Modal */}
        {selectedExercise && !showPerformanceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">
                      {selectedExercise.name}
                    </h2>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`}>
                        {selectedExercise.difficulty}
                      </span>
                      <span className="text-gray-600">
                        {exerciseCategories[selectedExercise.category]?.icon} {exerciseCategories[selectedExercise.category]?.name}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedExercise(null)}
                    className="text-gray-400 hover:text-gray-600 text-2xl"
                  >
                    ✕
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Content */}
                  <div className="lg:col-span-2">
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">Description</h3>
                      <p className="text-gray-600 leading-relaxed">{selectedExercise.description}</p>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Instructions détaillées</h3>
                      <ol className="list-decimal list-inside space-y-3">
                        {selectedExercise.instructions.map((instruction, index) => (
                          <li key={index} className="text-gray-600 leading-relaxed pl-2">
                            {instruction}
                          </li>
                        ))}
                      </ol>
                    </div>

                    {selectedExercise.tips && (
                      <div className="mb-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Conseils</h3>
                        <ul className="space-y-2">
                          {selectedExercise.tips.map((tip, index) => (
                            <li key={index} className="flex items-start text-gray-600">
                              <span className="text-blue-500 mr-2 mt-1">•</span>
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {selectedExercise.variations && (
                      <div className="mb-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Variations</h3>
                        <ul className="space-y-2">
                          {selectedExercise.variations.map((variation, index) => (
                            <li key={index} className="flex items-start text-gray-600">
                              <span className="text-green-500 mr-2 mt-1">•</span>
                              {variation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Sidebar */}
                  <div className="lg:col-span-1">
                    <div className="bg-gray-50 rounded-lg p-6 mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations</h3>

                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Durée</span>
                          <span className="font-medium">{selectedExercise.duration} min</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Calories</span>
                          <span className="font-medium">{selectedExercise.calories} cal</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Équipement</span>
                          <span className="font-medium">{equipmentTypes[selectedExercise.equipment]?.name}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-6 mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Groupes musculaires</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedExercise.muscleGroups.map(mg => (
                          <span key={mg} className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
                            {muscleGroups[mg]?.icon} {muscleGroups[mg]?.name}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <button
                        onClick={() => toggleFavorite(selectedExercise.id)}
                        className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${
                          favoriteExercises.some(fav => fav.id === selectedExercise.id)
                            ? 'bg-red-100 text-red-700 hover:bg-red-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        <FiHeart className="h-5 w-5 mr-2" />
                        {favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                      </button>

                      <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium">
                        <FiPlay className="h-5 w-5 mr-2" />
                        Commencer l'exercice
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal de notation */}
        {showRatingModal && ratingExercise && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Noter l'exercice : {ratingExercise.name}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Votre note
                  </label>
                  <div className="flex items-center justify-center space-x-2">
                    {renderStars(0, true, (rating) => {
                      const comment = exerciseComments[ratingExercise.id] || '';
                      rateExercise(ratingExercise.id, rating, comment);
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Commentaire (optionnel)
                  </label>
                  <textarea
                    defaultValue={exerciseComments[ratingExercise.id] || ''}
                    onChange={(e) => {
                      const updatedComments = { ...exerciseComments, [ratingExercise.id]: e.target.value };
                      setExerciseComments(updatedComments);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Partagez votre expérience avec cet exercice..."
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => {
                    const rating = exerciseRatings[ratingExercise.id] || 0;
                    const comment = exerciseComments[ratingExercise.id] || '';
                    if (rating > 0) {
                      rateExercise(ratingExercise.id, rating, comment);
                    }
                  }}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Sauvegarder
                </button>
                <button
                  onClick={() => {
                    setShowRatingModal(false);
                    setRatingExercise(null);
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Annuler
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Exercises;
