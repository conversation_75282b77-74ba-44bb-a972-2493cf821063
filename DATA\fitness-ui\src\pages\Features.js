import React from 'react';
import { 
  FiClock, 
  FiActivity, 
  FiBarChart2, 
  FiUsers, 
  FiTarget, 
  FiSmartphone,
  FiHeart,
  FiTrendingUp,
  FiCalendar,
  FiAward,
  FiShare2,
  FiSettings
} from 'react-icons/fi';

const Features = () => {
  const features = [
    {
      icon: <FiClock className="h-8 w-8" />,
      title: 'Minuteur intelligent',
      description: 'Minuteurs personnalisables pour HIIT, Tabata, EMOM et plus encore avec alertes sonores et visuelles.',
      details: [
        'Préréglages pour différents types d\'entraînement',
        'Minuteur personnalisable avec intervalles',
        'Alertes sonores et notifications visuelles',
        'Historique des sessions de minuterie'
      ]
    },
    {
      icon: <FiActivity className="h-8 w-8" />,
      title: 'Bibliothèque d\'exercices',
      description: 'Plus de 500 exercices avec instructions détaillées, vidéos et variations pour tous les niveaux.',
      details: [
        'Exercices catégorisés par groupe musculaire',
        'Instructions étape par étape',
        'Vidéos de démonstration',
        'Variations selon le niveau de difficulté'
      ]
    },
    {
      icon: <FiBarChart2 className="h-8 w-8" />,
      title: 'Suivi de progression',
      description: 'Analysez vos performances avec des graphiques détaillés et des statistiques personnalisées.',
      details: [
        'Graphiques de progression en temps réel',
        'Statistiques détaillées par période',
        'Comparaison des performances',
        'Objectifs personnalisables'
      ]
    },
    {
      icon: <FiUsers className="h-8 w-8" />,
      title: 'Communauté sociale',
      description: 'Partagez vos entraînements, défiez vos amis et rejoignez des groupes de motivation.',
      details: [
        'Partage d\'activités et de résultats',
        'Défis entre amis',
        'Groupes d\'entraînement',
        'Système de récompenses'
      ]
    },
    {
      icon: <FiTarget className="h-8 w-8" />,
      title: 'Objectifs personnalisés',
      description: 'Définissez et suivez vos objectifs fitness avec des plans d\'action adaptés.',
      details: [
        'Objectifs SMART personnalisables',
        'Plans d\'action automatiques',
        'Rappels et notifications',
        'Suivi de progression vers les objectifs'
      ]
    },
    {
      icon: <FiSmartphone className="h-8 w-8" />,
      title: 'Application mobile',
      description: 'Accédez à tous vos entraînements depuis votre smartphone, même hors ligne.',
      details: [
        'Interface optimisée mobile',
        'Synchronisation multi-appareils',
        'Mode hors ligne',
        'Notifications push intelligentes'
      ]
    },
    {
      icon: <FiHeart className="h-8 w-8" />,
      title: 'Suivi santé',
      description: 'Moniteur de fréquence cardiaque, calories brûlées et métriques de récupération.',
      details: [
        'Zones de fréquence cardiaque',
        'Calcul précis des calories',
        'Métriques de récupération',
        'Intégration avec appareils fitness'
      ]
    },
    {
      icon: <FiTrendingUp className="h-8 w-8" />,
      title: 'Analyses avancées',
      description: 'Intelligence artificielle pour optimiser vos entraînements et prédire vos performances.',
      details: [
        'Recommandations IA personnalisées',
        'Prédiction de performances',
        'Optimisation automatique des programmes',
        'Détection de surmenage'
      ]
    },
    {
      icon: <FiCalendar className="h-8 w-8" />,
      title: 'Planification intelligente',
      description: 'Planifiez vos entraînements avec des suggestions basées sur votre emploi du temps.',
      details: [
        'Calendrier d\'entraînement intégré',
        'Suggestions basées sur la disponibilité',
        'Rappels automatiques',
        'Adaptation selon la météo'
      ]
    },
    {
      icon: <FiAward className="h-8 w-8" />,
      title: 'Système de récompenses',
      description: 'Gagnez des badges, débloquez des achievements et restez motivé.',
      details: [
        'Badges de progression',
        'Achievements déblocables',
        'Classements et ligues',
        'Récompenses personnalisées'
      ]
    },
    {
      icon: <FiShare2 className="h-8 w-8" />,
      title: 'Intégrations',
      description: 'Connectez-vous avec Strava, MyFitnessPal, Apple Health et Google Fit.',
      details: [
        'Synchronisation Strava',
        'Import/export de données',
        'Compatibilité Apple Health',
        'Intégration Google Fit'
      ]
    },
    {
      icon: <FiSettings className="h-8 w-8" />,
      title: 'Personnalisation',
      description: 'Interface entièrement personnalisable selon vos préférences et besoins.',
      details: [
        'Thèmes personnalisables',
        'Widgets configurables',
        'Raccourcis personnalisés',
        'Préférences d\'entraînement'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Fonctionnalités complètes
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez toutes les fonctionnalités qui font de FitTracker l'application 
            de fitness la plus complète pour atteindre vos objectifs.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-primary mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-4">
                {feature.description}
              </p>
              <ul className="space-y-2">
                {feature.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-sm text-gray-700">{detail}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-2xl p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            Prêt à transformer vos entraînements ?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Rejoignez des milliers d'utilisateurs qui ont déjà amélioré leur condition physique avec FitTracker.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button className="bg-white text-primary font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors">
              Commencer gratuitement
            </button>
            <button className="border-2 border-white text-white font-bold py-3 px-8 rounded-lg hover:bg-white hover:text-primary transition-colors">
              Voir la démo
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Features;
