import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>lock, FiTarget, <PERSON><PERSON>lay, Fi<PERSON>ilter, FiX } from 'react-icons/fi';
import programsData from '../data/programsData.json';

const Programs = () => {
  const [programs, setPrograms] = useState([]);
  const [filteredPrograms, setFilteredPrograms] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('Tous');
  const [selectedLevel, setSelectedLevel] = useState('Tous');
  const [selectedProgram, setSelectedProgram] = useState(null);
  const [showFilters, setShowFilters] = useState(false);

  // Charger les programmes au montage du composant
  useEffect(() => {
    setPrograms(programsData);
    setFilteredPrograms(programsData);
  }, []);

  // Filtrer les programmes selon les critères sélectionnés
  useEffect(() => {
    let filtered = programs;

    if (selectedCategory !== 'Tous') {
      filtered = filtered.filter(program => program.category === selectedCategory);
    }

    if (selectedLevel !== 'Tous') {
      filtered = filtered.filter(program => program.level === selectedLevel);
    }

    setFilteredPrograms(filtered);
  }, [programs, selectedCategory, selectedLevel]);

  // Obtenir les catégories uniques
  const categories = ['Tous', ...new Set(programs.map(program => program.category))];
  
  // Obtenir les niveaux uniques
  const levels = ['Tous', ...new Set(programs.map(program => program.level))];

  // Obtenir la couleur selon la catégorie
  const getCategoryColor = (category) => {
    const colors = {
      'Musculation': 'bg-blue-100 text-blue-800',
      'Cardio': 'bg-red-100 text-red-800',
      'HIIT': 'bg-orange-100 text-orange-800',
      'Mobilité': 'bg-green-100 text-green-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  // Obtenir la couleur selon le niveau
  const getLevelColor = (level) => {
    const colors = {
      'Débutant': 'bg-green-500',
      'Intermédiaire': 'bg-yellow-500',
      'Avancé': 'bg-red-500'
    };
    return colors[level] || 'bg-gray-500';
  };

  // Réinitialiser les filtres
  const resetFilters = () => {
    setSelectedCategory('Tous');
    setSelectedLevel('Tous');
  };

  return (
    <div className="py-12 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      {/* En-tête */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Programmes d'entraînement
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Découvrez notre collection de programmes d'entraînement adaptés à tous les niveaux. 
          Du débutant à l'expert, trouvez le programme qui vous correspond.
        </p>
      </div>

      {/* Filtres */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FiFilter className="h-4 w-4" />
              Filtres
            </button>
            {(selectedCategory !== 'Tous' || selectedLevel !== 'Tous') && (
              <button
                onClick={resetFilters}
                className="text-sm text-primary hover:text-blue-600 underline"
              >
                Réinitialiser
              </button>
            )}
          </div>
          <div className="text-sm text-gray-600">
            {filteredPrograms.length} programme{filteredPrograms.length > 1 ? 's' : ''} trouvé{filteredPrograms.length > 1 ? 's' : ''}
          </div>
        </div>

        {/* Panneau de filtres */}
        {showFilters && (
          <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Catégorie
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full input"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Niveau
                </label>
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="w-full input"
                >
                  {levels.map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Grille des programmes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {filteredPrograms.map((program) => (
          <div
            key={program.id}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => setSelectedProgram(program)}
          >
            <div className="p-6">
              <div className="flex justify-between items-start mb-3">
                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(program.category)}`}>
                  {program.category}
                </span>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${getLevelColor(program.level)}`}></div>
                  <span className="text-xs text-gray-600">{program.level}</span>
                </div>
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {program.title}
              </h3>
              
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {program.description}
              </p>
              
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center gap-1">
                  <FiClock className="h-4 w-4" />
                  {program.duration}
                </div>
                <div className="flex items-center gap-1">
                  <FiTarget className="h-4 w-4" />
                  {program.exercises.length} exercices
                </div>
              </div>
              
              <div className="text-xs text-gray-500 mb-4">
                <strong>Équipement :</strong> {program.equipment}
              </div>
              
              <button className="w-full btn btn-primary flex items-center justify-center gap-2">
                <FiPlay className="h-4 w-4" />
                Voir le programme
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Message si aucun programme trouvé */}
      {filteredPrograms.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <FiTarget className="h-16 w-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucun programme trouvé
          </h3>
          <p className="text-gray-600 mb-4">
            Essayez de modifier vos critères de recherche.
          </p>
          <button
            onClick={resetFilters}
            className="btn btn-primary"
          >
            Réinitialiser les filtres
          </button>
        </div>
      )}

      {/* Modal de détail du programme */}
      {selectedProgram && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* En-tête de la modal */}
              <div className="flex justify-between items-start mb-6">
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(selectedProgram.category)}`}>
                      {selectedProgram.category}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${getLevelColor(selectedProgram.level)}`}></div>
                      <span className="text-sm font-medium text-gray-700">{selectedProgram.level}</span>
                    </div>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-2">
                    {selectedProgram.title}
                  </h2>
                  <p className="text-gray-600 mb-4">
                    {selectedProgram.description}
                  </p>
                  <div className="flex items-center gap-6 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <FiClock className="h-4 w-4" />
                      {selectedProgram.duration}
                    </div>
                    <div className="flex items-center gap-1">
                      <FiTarget className="h-4 w-4" />
                      {selectedProgram.exercises.length} exercices
                    </div>
                    <div>
                      <strong>Équipement :</strong> {selectedProgram.equipment}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedProgram(null)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <FiX className="h-6 w-6" />
                </button>
              </div>

              {/* Liste des exercices */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Exercices du programme
                </h3>
                <div className="space-y-4">
                  {selectedProgram.exercises.map((exercise, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">
                          {index + 1}. {exercise.name}
                        </h4>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Séries :</span> {exercise.sets}
                        </div>
                        <div>
                          <span className="font-medium">Répétitions :</span> {exercise.reps}
                        </div>
                        <div>
                          <span className="font-medium">Repos :</span> {exercise.rest}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <button className="flex-1 btn btn-primary flex items-center justify-center gap-2">
                  <FiPlay className="h-4 w-4" />
                  Commencer l'entraînement
                </button>
                <button className="flex-1 btn bg-gray-200 text-gray-700 hover:bg-gray-300 flex items-center justify-center gap-2">
                  <FiTarget className="h-4 w-4" />
                  Ajouter aux favoris
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Programs;
