import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import {
  FiMap,
  FiNavigation,
  FiMapPin,
  FiSearch,
  FiPlus,
  FiStar,
  FiUsers,
  FiTarget,
  FiBookmark,
  FiTrash2,
  FiPlay,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import {
  generatePopularRoutes,
  generatePOIs,
  calculateDistance,
  optimizeRouteWithPOIs,
  DEFAULT_POSITION
} from '../utils/mapUtils';

// Configuration des icônes Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const Routes = () => {
  const { user } = useAuth();
  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);
  const [routes, setRoutes] = useState([]);
  const [pois, setPois] = useState([]);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [selectedPOIs, setSelectedPOIs] = useState([]);
  const [optimizedRoute, setOptimizedRoute] = useState(null);
  const [isPlanning, setIsPlanning] = useState(false);
  const [planningPoints, setPlanningPoints] = useState([]);
  const [filters, setFilters] = useState({
    type: 'all',
    difficulty: 'all',
    distance: 'all'
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [showPOIs, setShowPOIs] = useState(true);
  const [activeTab, setActiveTab] = useState('discover');
  const [savedRoutes, setSavedRoutes] = useState([]);
  const [routeHistory, setRouteHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Charger les routes sauvegardées et l'historique depuis localStorage
    try {
      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');
      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');
      setSavedRoutes(saved);
      setRouteHistory(history);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  }, []);

  useEffect(() => {
    setIsLoading(true);
    setError(null);

    // Obtenir la position de l'utilisateur
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const pos = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserPosition(pos);
          loadRoutesAndPOIs(pos);
        },
        (error) => {
          console.warn('Géolocalisation échouée:', error);
          setError('Impossible d\'obtenir votre position. Utilisation de la position par défaut.');
          loadRoutesAndPOIs(DEFAULT_POSITION);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    } else {
      setError('Géolocalisation non supportée par votre navigateur.');
      loadRoutesAndPOIs(DEFAULT_POSITION);
    }
  }, []);

  const loadRoutesAndPOIs = (position) => {
    try {
      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);
      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);

      // Combiner avec les routes sauvegardées
      const allRoutes = [...savedRoutes, ...popularRoutes];
      setRoutes(allRoutes);
      setPois(nearbyPOIs);
      setIsLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des routes:', error);
      setError('Erreur lors du chargement des données de carte.');
      setIsLoading(false);
    }
  };

  const filteredRoutes = routes.filter(route => {
    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filters.type === 'all' || route.type === filters.type;
    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;
    const matchesDistance = filters.distance === 'all' || 
                           (filters.distance === 'short' && route.distance <= 5) ||
                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||
                           (filters.distance === 'long' && route.distance > 15);
    
    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;
  });

  const handleRouteSelect = (route) => {
    setSelectedRoute(route);
    setOptimizedRoute(null);
    setSelectedPOIs([]);
  };

  const handlePOIToggle = (poi) => {
    setSelectedPOIs(prev => {
      const isSelected = prev.find(p => p.id === poi.id);
      if (isSelected) {
        return prev.filter(p => p.id !== poi.id);
      } else {
        return [...prev, poi];
      }
    });
  };

  const optimizeRoute = () => {
    if (selectedRoute && selectedPOIs.length > 0) {
      const startPoint = selectedRoute.points[0];
      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];
      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);
      setOptimizedRoute(optimized);
    }
  };

  const startRoutePlanning = () => {
    setIsPlanning(true);
    setPlanningPoints([]);
    setSelectedRoute(null);
    setOptimizedRoute(null);
  };

  const MapClickHandler = () => {
    useMapEvents({
      click: (e) => {
        if (isPlanning) {
          const newPoint = {
            lat: e.latlng.lat,
            lng: e.latlng.lng,
            elevation: 100 // Valeur par défaut
          };
          setPlanningPoints(prev => [...prev, newPoint]);
        }
      }
    });
    return null;
  };

  const finishPlanning = () => {
    if (planningPoints.length >= 2) {
      const newRoute = {
        id: `custom_${Date.now()}`,
        name: 'Mon parcours personnalisé',
        type: 'running',
        distance: calculateTotalDistance(planningPoints),
        points: planningPoints,
        difficulty: 'modéré',
        rating: 0,
        completions: 0,
        createdBy: user?.firstName || 'Moi',
        tags: ['custom'],
        description: 'Parcours créé par l\'utilisateur',
        createdAt: new Date().toISOString(),
        isCustom: true
      };

      // Sauvegarder dans localStorage
      const updatedSavedRoutes = [newRoute, ...savedRoutes];
      setSavedRoutes(updatedSavedRoutes);
      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));

      setRoutes(prev => [newRoute, ...prev]);
      setSelectedRoute(newRoute);
      setIsPlanning(false);
      setPlanningPoints([]);
    }
  };

  const calculateTotalDistance = (points) => {
    let total = 0;
    for (let i = 1; i < points.length; i++) {
      total += calculateDistance(
        points[i-1].lat, points[i-1].lng,
        points[i].lat, points[i].lng
      );
    }
    return total;
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'facile': return 'text-green-600 bg-green-100';
      case 'modéré': return 'text-yellow-600 bg-yellow-100';
      case 'difficile': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'running': return '🏃‍♂️';
      case 'cycling': return '🚴‍♂️';
      case 'hiking': return '🥾';
      case 'walking': return '🚶‍♂️';
      default: return '📍';
    }
  };

  const saveRoute = (route) => {
    if (!savedRoutes.find(r => r.id === route.id)) {
      const routeToSave = { ...route, savedAt: new Date().toISOString() };
      const updatedSavedRoutes = [routeToSave, ...savedRoutes];
      setSavedRoutes(updatedSavedRoutes);
      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));
    }
  };

  const unsaveRoute = (routeId) => {
    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);
    setSavedRoutes(updatedSavedRoutes);
    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));
  };

  const deleteCustomRoute = (routeId) => {
    // Supprimer des routes sauvegardées
    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);
    setSavedRoutes(updatedSavedRoutes);
    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));

    // Supprimer de la liste des routes
    setRoutes(prev => prev.filter(r => r.id !== routeId));

    // Désélectionner si c'était la route sélectionnée
    if (selectedRoute?.id === routeId) {
      setSelectedRoute(null);
    }
  };

  const startRoute = (route) => {
    const historyEntry = {
      id: Date.now(),
      route: route,
      startedAt: new Date().toISOString(),
      status: 'started'
    };

    const updatedHistory = [historyEntry, ...routeHistory];
    setRouteHistory(updatedHistory);
    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Cartes et Itinéraires
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez
            les points d'intérêt autour de vous.
          </p>

          {/* Messages d'erreur */}
          {error && (
            <div className="mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                <p className="text-sm text-yellow-800">{error}</p>
              </div>
            </div>
          )}

          {/* Indicateur de chargement */}
          {isLoading && (
            <div className="mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                <p className="text-sm text-blue-800">Chargement des données...</p>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          <button
            onClick={() => setActiveTab('discover')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'discover'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiMap className="inline mr-2" />
            Découvrir
          </button>
          <button
            onClick={() => setActiveTab('saved')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'saved'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiBookmark className="inline mr-2" />
            Sauvegardées ({savedRoutes.length})
          </button>
          <button
            onClick={() => setActiveTab('plan')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'plan'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiNavigation className="inline mr-2" />
            Planifier
          </button>
          <button
            onClick={() => setActiveTab('pois')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'pois'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiMapPin className="inline mr-2" />
            Points d'intérêt
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            {activeTab === 'discover' && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Routes populaires
                  </h2>
                  <button
                    onClick={() => setShowPOIs(!showPOIs)}
                    className={`px-3 py-1 rounded-md text-sm ${
                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    POIs
                  </button>
                </div>

                {/* Search and Filters */}
                <div className="space-y-4 mb-6">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher un parcours..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <select
                      value={filters.type}
                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Tous types</option>
                      <option value="running">Course</option>
                      <option value="cycling">Vélo</option>
                      <option value="hiking">Randonnée</option>
                      <option value="walking">Marche</option>
                    </select>

                    <select
                      value={filters.difficulty}
                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Toutes difficultés</option>
                      <option value="facile">Facile</option>
                      <option value="modéré">Modéré</option>
                      <option value="difficile">Difficile</option>
                    </select>
                  </div>
                </div>

                {/* Routes List */}
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {filteredRoutes.map(route => (
                    <div
                      key={route.id}
                      onClick={() => handleRouteSelect(route)}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedRoute?.id === route.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getTypeIcon(route.type)}</span>
                          <h3 className="font-medium text-gray-900 text-sm">{route.name}</h3>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>
                          {route.difficulty}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-2">
                        {route.description}
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span>{route.distance.toFixed(1)} km</span>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center">
                            <FiStar className="h-3 w-3 text-yellow-400 mr-1" />
                            <span>{route.rating.toFixed(1)}</span>
                          </div>
                          <div className="flex items-center">
                            <FiUsers className="h-3 w-3 mr-1" />
                            <span>{route.completions}</span>
                          </div>
                        </div>
                      </div>

                      {/* Boutons d'action */}
                      <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                        <button
                          onClick={() => startRoute(route)}
                          className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center"
                        >
                          <FiPlay className="h-3 w-3 mr-1" />
                          Démarrer
                        </button>

                        {savedRoutes.find(r => r.id === route.id) ? (
                          <button
                            onClick={() => unsaveRoute(route.id)}
                            className="bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors"
                            title="Retirer des favoris"
                          >
                            <FiBookmark className="h-3 w-3" />
                          </button>
                        ) : (
                          <button
                            onClick={() => saveRoute(route)}
                            className="bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors"
                            title="Sauvegarder"
                          >
                            <FiBookmark className="h-3 w-3" />
                          </button>
                        )}

                        {route.isCustom && (
                          <button
                            onClick={() => deleteCustomRoute(route.id)}
                            className="bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors"
                            title="Supprimer"
                          >
                            <FiTrash2 className="h-3 w-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'saved' && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Routes sauvegardées
                  </h2>
                  <span className="text-sm text-gray-500">
                    {savedRoutes.length} route{savedRoutes.length !== 1 ? 's' : ''}
                  </span>
                </div>

                {savedRoutes.length === 0 ? (
                  <div className="text-center py-8">
                    <FiBookmark className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 mb-2">Aucune route sauvegardée</p>
                    <p className="text-sm text-gray-400">
                      Sauvegardez vos routes préférées pour les retrouver facilement
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {savedRoutes.map(route => (
                      <div
                        key={route.id}
                        onClick={() => handleRouteSelect(route)}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedRoute?.id === route.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getTypeIcon(route.type)}</span>
                            <h3 className="font-medium text-gray-900 text-sm">{route.name}</h3>
                            {route.isCustom && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                Personnalisé
                              </span>
                            )}
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>
                            {route.difficulty}
                          </span>
                        </div>

                        <div className="text-sm text-gray-600 mb-2">
                          {route.description}
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                          <span>{route.distance.toFixed(1)} km</span>
                          <div className="flex items-center space-x-2">
                            {route.savedAt && (
                              <span>Sauvegardé le {new Date(route.savedAt).toLocaleDateString()}</span>
                            )}
                          </div>
                        </div>

                        {/* Boutons d'action */}
                        <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                          <button
                            onClick={() => startRoute(route)}
                            className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center"
                          >
                            <FiPlay className="h-3 w-3 mr-1" />
                            Démarrer
                          </button>

                          <button
                            onClick={() => unsaveRoute(route.id)}
                            className="bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors"
                            title="Retirer des favoris"
                          >
                            <FiBookmark className="h-3 w-3" />
                          </button>

                          {route.isCustom && (
                            <button
                              onClick={() => deleteCustomRoute(route.id)}
                              className="bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors"
                              title="Supprimer"
                            >
                              <FiTrash2 className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'plan' && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Planificateur d'itinéraire
                </h2>
                
                {!isPlanning ? (
                  <div className="space-y-4">
                    <button
                      onClick={startRoutePlanning}
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <FiPlus className="mr-2" />
                      Créer un nouveau parcours
                    </button>
                    
                    <div className="text-sm text-gray-600">
                      <p className="mb-2">Instructions :</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Cliquez sur "Créer un nouveau parcours"</li>
                        <li>Cliquez sur la carte pour ajouter des points</li>
                        <li>Minimum 2 points requis</li>
                        <li>Cliquez sur "Terminer" pour sauvegarder</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-2">Mode planification actif</p>
                      <p>Points ajoutés : {planningPoints.length}</p>
                      {planningPoints.length >= 2 && (
                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={finishPlanning}
                        disabled={planningPoints.length < 2}
                        className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                      >
                        Terminer
                      </button>
                      <button
                        onClick={() => {
                          setIsPlanning(false);
                          setPlanningPoints([]);
                        }}
                        className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        Annuler
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'pois' && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Points d'intérêt
                </h2>
                
                {selectedRoute && (
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-700 mb-2">
                      Sélectionnez des POIs pour optimiser votre parcours
                    </p>
                    <button
                      onClick={optimizeRoute}
                      disabled={selectedPOIs.length === 0}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm"
                    >
                      <FiTarget className="inline mr-2" />
                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)
                    </button>
                  </div>
                )}
                
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {pois.map(poi => (
                    <div
                      key={poi.id}
                      onClick={() => handlePOIToggle(poi)}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPOIs.find(p => p.id === poi.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{poi.icon}</span>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 text-sm">{poi.name}</h4>
                          <p className="text-xs text-gray-600">{poi.description}</p>
                          <div className="flex items-center mt-1">
                            <FiStar className="h-3 w-3 text-yellow-400 mr-1" />
                            <span className="text-xs text-gray-500">{poi.rating.toFixed(1)}</span>
                            {poi.verified && (
                              <span className="ml-2 text-xs text-green-600">✓ Vérifié</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Map */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-96 lg:h-[600px]">
                <MapContainer
                  center={[userPosition.lat, userPosition.lng]}
                  zoom={13}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  />
                  
                  <MapClickHandler />
                  
                  {/* User position */}
                  <Marker position={[userPosition.lat, userPosition.lng]}>
                    <Popup>Votre position</Popup>
                  </Marker>
                  
                  {/* Selected route */}
                  {selectedRoute && (
                    <Polyline
                      positions={selectedRoute.points.map(p => [p.lat, p.lng])}
                      color="blue"
                      weight={4}
                      opacity={0.7}
                    />
                  )}
                  
                  {/* Optimized route */}
                  {optimizedRoute && (
                    <Polyline
                      positions={optimizedRoute.map(p => [p.lat, p.lng])}
                      color="red"
                      weight={4}
                      opacity={0.8}
                    />
                  )}
                  
                  {/* Planning points */}
                  {planningPoints.map((point, index) => (
                    <Marker key={index} position={[point.lat, point.lng]}>
                      <Popup>Point {index + 1}</Popup>
                    </Marker>
                  ))}
                  
                  {/* Planning route */}
                  {planningPoints.length > 1 && (
                    <Polyline
                      positions={planningPoints.map(p => [p.lat, p.lng])}
                      color="green"
                      weight={4}
                      opacity={0.7}
                    />
                  )}
                  
                  {/* POIs */}
                  {showPOIs && pois.map(poi => (
                    <Marker
                      key={poi.id}
                      position={[poi.lat, poi.lng]}
                      icon={L.divIcon({
                        html: `<div style="background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;">${poi.icon}</div>`,
                        className: 'custom-poi-marker',
                        iconSize: [30, 30]
                      })}
                    >
                      <Popup>
                        <div>
                          <h4 className="font-medium">{poi.name}</h4>
                          <p className="text-sm text-gray-600">{poi.description}</p>
                          <div className="flex items-center mt-1">
                            <FiStar className="h-3 w-3 text-yellow-400 mr-1" />
                            <span className="text-sm">{poi.rating.toFixed(1)}</span>
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}
                  
                  {/* Selected POIs highlight */}
                  {selectedPOIs.map(poi => (
                    <Marker
                      key={`selected-${poi.id}`}
                      position={[poi.lat, poi.lng]}
                      icon={L.divIcon({
                        html: `<div style="background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">${poi.icon}</div>`,
                        className: 'custom-selected-poi-marker',
                        iconSize: [35, 35]
                      })}
                    />
                  ))}
                </MapContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Routes;
