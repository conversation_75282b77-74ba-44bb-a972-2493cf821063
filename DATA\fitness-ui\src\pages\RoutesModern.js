import React, { useState, useEffect, useCallback } from 'react';
import {
  FiMap,
  FiActivity,
  FiTrendingUp,
  FiUsers,
  FiSearch,
  FiFilter,
  FiGrid,
  FiList,
  FiSettings,
  FiHeart,
  FiBookmark,
  FiShare2,
  FiMapPin,
  FiClock,
  FiTarget,
  FiZap,
  FiEye,
  FiPlay,
  FiStar
} from 'react-icons/fi';
import ModernMap from '../components/ModernMap';
import StravaHeader from '../components/StravaHeader';
import ActivityFeed from '../components/ActivityFeed';
import HeatmapLayer, { HeatmapControls } from '../components/HeatmapLayer';
import AdvancedFilters from '../components/AdvancedFilters';
import SegmentPanel from '../components/SegmentPanel';
import { generatePopularRoutes, generatePOIs, generateSegments, DEFAULT_POSITION } from '../utils/mapUtils';

const RoutesModern = () => {
  // États principaux
  const [activeView, setActiveView] = useState('explore');
  const [viewMode, setViewMode] = useState('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);
  
  // États pour les données
  const [routes, setRoutes] = useState([]);
  const [pois, setPois] = useState([]);
  const [segments, setSegments] = useState([]);
  const [activities, setActivities] = useState([]);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [selectedSegment, setSelectedSegment] = useState(null);
  
  // États pour les filtres
  const [filters, setFilters] = useState({
    type: 'all',
    difficulty: 'all',
    distance: 'all',
    sport: 'all',
    surface: 'all'
  });
  
  // États pour la heatmap
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [heatmapIntensity, setHeatmapIntensity] = useState(0.6);
  const [heatmapRadius, setHeatmapRadius] = useState(25);
  const [heatmapActivityType, setHeatmapActivityType] = useState('all');
  const [heatmapTimeRange, setHeatmapTimeRange] = useState('all');

  // Initialisation des données
  useEffect(() => {
    const initializeData = () => {
      const generatedRoutes = generatePopularRoutes(userPosition.lat, userPosition.lng, 20);
      const generatedPOIs = generatePOIs(userPosition.lat, userPosition.lng, 15);
      const generatedSegments = generateSegments(generatedRoutes, 10);
      
      setRoutes(generatedRoutes);
      setPois(generatedPOIs);
      setSegments(generatedSegments);
    };

    initializeData();
  }, [userPosition]);

  // Géolocalisation
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserPosition({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.log('Erreur de géolocalisation:', error);
        }
      );
    }
  }, []);

  // Filtrage des routes
  const filteredRoutes = routes.filter(route => {
    if (searchTerm && !route.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    if (filters.sport !== 'all' && route.type !== filters.sport) {
      return false;
    }
    if (filters.difficulty !== 'all' && route.difficulty !== filters.difficulty) {
      return false;
    }
    if (filters.distance !== 'all') {
      const [min, max] = filters.distance.split('-').map(Number);
      if (max && (route.distance < min || route.distance > max)) {
        return false;
      }
      if (!max && route.distance < min) {
        return false;
      }
    }
    return true;
  });

  const handleRouteSelect = (route) => {
    setSelectedRoute(route);
    setActiveView('explore');
  };

  const handleSegmentSelect = (segment) => {
    setSelectedSegment(segment);
  };

  const handleActivitySelect = (activity) => {
    // Logique pour afficher l'activité sur la carte
    setActiveView('explore');
  };

  const renderExploreView = () => (
    <div className="flex flex-col lg:flex-row h-full">
      {/* Sidebar - Responsive */}
      <div className="w-full lg:w-1/3 xl:w-1/4 bg-white border-b lg:border-b-0 lg:border-r border-gray-200 flex flex-col max-h-96 lg:max-h-none sidebar-mobile">
        {/* Search & Filters - Responsive */}
        <div className="p-3 lg:p-4 border-b border-gray-100">
          <div className="relative mb-4">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher des parcours..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
            />
          </div>

          <div className="flex flex-col sm:flex-row lg:flex-col xl:flex-row items-start sm:items-center lg:items-start xl:items-center justify-between gap-3">
            <div className="flex flex-wrap items-center gap-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`btn-interactive touch-button-small flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm ${
                  showFilters ? 'bg-orange-50 text-orange-600 focus:ring-orange-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
                }`}
              >
                <FiFilter className="h-4 w-4" />
                <span className="hidden sm:inline">Filtres</span>
              </button>

              <button
                onClick={() => setShowHeatmap(!showHeatmap)}
                className={`btn-interactive touch-button-small flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm ${
                  showHeatmap ? 'bg-blue-50 text-blue-600 focus:ring-blue-500' : 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
                }`}
              >
                <FiZap className="h-4 w-4" />
                <span className="hidden sm:inline">Heatmap</span>
              </button>
            </div>

            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`btn-interactive touch-button-small p-2 rounded transition-colors focus:ring-gray-500 ${
                  viewMode === 'grid' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Vue grille"
              >
                <FiGrid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`btn-interactive touch-button-small p-2 rounded transition-colors focus:ring-gray-500 ${
                  viewMode === 'list' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Vue liste"
              >
                <FiList className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="border-b border-gray-100">
            <AdvancedFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClose={() => setShowFilters(false)}
            />
          </div>
        )}

        {/* Heatmap Controls */}
        {showHeatmap && (
          <div className="p-3 lg:p-4 border-b border-gray-100">
            <HeatmapControls
              intensity={heatmapIntensity}
              onIntensityChange={setHeatmapIntensity}
              radius={heatmapRadius}
              onRadiusChange={setHeatmapRadius}
              activityType={heatmapActivityType}
              onActivityTypeChange={setHeatmapActivityType}
              timeRange={heatmapTimeRange}
              onTimeRangeChange={setHeatmapTimeRange}
              visible={showHeatmap}
              onVisibilityChange={setShowHeatmap}
            />
          </div>
        )}

        {/* Routes List */}
        <div className="flex-1 overflow-y-auto custom-scrollbar">
          <div className="p-3 lg:p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-base lg:text-lg font-semibold text-gray-900">
                Parcours populaires
              </h2>
              <span className="text-xs lg:text-sm text-gray-500">
                {filteredRoutes.length} résultats
              </span>
            </div>

            <div className={viewMode === 'grid' ? 'grid grid-cols-1 gap-3 lg:gap-4' : 'space-y-2 lg:space-y-3'}>
              {filteredRoutes.map((route) => (
                <RouteCard
                  key={route.id}
                  route={route}
                  viewMode={viewMode}
                  onSelect={handleRouteSelect}
                  isSelected={selectedRoute?.id === route.id}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Map - Responsive */}
      <div className="flex-1 relative min-h-96 lg:min-h-0">
        <ModernMap
          center={userPosition}
          routes={filteredRoutes}
          pois={pois}
          segments={segments}
          selectedRoute={selectedRoute}
          selectedSegment={selectedSegment}
          onRouteSelect={handleRouteSelect}
          onSegmentSelect={handleSegmentSelect}
          userPosition={userPosition}
        >
          {showHeatmap && (
            <HeatmapLayer
              intensity={heatmapIntensity}
              radius={heatmapRadius}
              activityType={heatmapActivityType}
              timeRange={heatmapTimeRange}
            />
          )}
        </ModernMap>

        {selectedSegment && (
          <div className="absolute top-4 right-4 w-72 lg:w-80 max-w-sm">
            <SegmentPanel
              segments={segments}
              selectedSegment={selectedSegment}
              onSegmentSelect={handleSegmentSelect}
              userPosition={userPosition}
            />
          </div>
        )}
      </div>
    </div>
  );

  const renderFeedView = () => (
    <div className="max-w-4xl mx-auto px-4 py-6">
      <ActivityFeed
        activities={activities}
        onActivitySelect={handleActivitySelect}
      />
    </div>
  );

  const renderSegmentsView = () => (
    <div className="flex flex-col lg:flex-row h-full">
      <div className="w-full lg:w-1/3 xl:w-1/4 bg-white border-b lg:border-b-0 lg:border-r border-gray-200 max-h-96 lg:max-h-none overflow-y-auto custom-scrollbar sidebar-mobile">
        <div className="p-3 lg:p-4">
          <h2 className="text-base lg:text-lg font-semibold text-gray-900 mb-4">
            Segments populaires
          </h2>
          <div className="space-y-2 lg:space-y-3">
            {segments.map((segment) => (
              <SegmentCard
                key={segment.id}
                segment={segment}
                onSelect={handleSegmentSelect}
                isSelected={selectedSegment?.id === segment.id}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="flex-1 min-h-96 lg:min-h-0">
        <ModernMap
          center={userPosition}
          routes={[]}
          pois={[]}
          segments={segments}
          selectedSegment={selectedSegment}
          onSegmentSelect={handleSegmentSelect}
          userPosition={userPosition}
        />
      </div>
    </div>
  );

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <StravaHeader
        activeView={activeView}
        onViewChange={setActiveView}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        showFilters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />
      
      <div className="flex-1 overflow-hidden">
        {activeView === 'feed' && renderFeedView()}
        {activeView === 'explore' && renderExploreView()}
        {activeView === 'segments' && renderSegmentsView()}
        {activeView === 'athletes' && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <FiUsers className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Communauté d'athlètes</h3>
              <p>Fonctionnalité en cours de développement</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Composant RouteCard
const RouteCard = ({ route, viewMode, onSelect, isSelected }) => {
  const getDifficultyColor = (difficulty) => {
    const colors = {
      'facile': 'text-green-600 bg-green-50',
      'modéré': 'text-yellow-600 bg-yellow-50',
      'difficile': 'text-red-600 bg-red-50'
    };
    return colors[difficulty] || 'text-gray-600 bg-gray-50';
  };

  const getActivityIcon = (type) => {
    const icons = {
      running: '🏃‍♂️',
      cycling: '🚴‍♂️',
      hiking: '🥾',
      walking: '🚶‍♂️'
    };
    return icons[type] || '🏃‍♂️';
  };

  if (viewMode === 'list') {
    return (
      <div
        onClick={() => onSelect(route)}
        className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-all ${
          isSelected
            ? 'border-orange-500 bg-orange-50'
            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }`}
      >
        <div className="text-2xl">{getActivityIcon(route.type)}</div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 truncate">{route.name}</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>{route.distance.toFixed(1)} km</span>
            <span>{route.elevation?.gain || 0}m D+</span>
            <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(route.difficulty)}`}>
              {route.difficulty}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-yellow-500">
            <FiStar className="h-4 w-4 fill-current" />
            <span className="text-sm ml-1">{route.rating?.toFixed(1) || '4.5'}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={() => onSelect(route)}
      className={`btn-interactive bg-white rounded-lg border cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-xl">{getActivityIcon(route.type)}</span>
            <h3 className="font-semibold text-gray-900 text-sm">{route.name}</h3>
          </div>
          <button className="btn-interactive touch-button-small text-gray-400 hover:text-red-500 transition-colors p-1 rounded focus:ring-red-500">
            <FiHeart className="h-4 w-4" />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div className="flex items-center text-gray-600">
            <FiMapPin className="h-3 w-3 mr-1" />
            <span>{route.distance.toFixed(1)} km</span>
          </div>
          <div className="flex items-center text-gray-600">
            <FiTrendingUp className="h-3 w-3 mr-1" />
            <span>{route.elevation?.gain || 0}m</span>
          </div>
          <div className="flex items-center text-gray-600">
            <FiClock className="h-3 w-3 mr-1" />
            <span>{Math.round(route.distance * 6)} min</span>
          </div>
          <div className="flex items-center text-gray-600">
            <FiUsers className="h-3 w-3 mr-1" />
            <span>{route.completions || 0}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>
            {route.difficulty}
          </span>
          <div className="flex items-center text-yellow-500">
            <FiStar className="h-3 w-3 fill-current" />
            <span className="text-xs ml-1">{route.rating?.toFixed(1) || '4.5'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Composant SegmentCard
const SegmentCard = ({ segment, onSelect, isSelected }) => {
  const getDifficultyColor = (difficulty) => {
    const colors = {
      'facile': 'text-green-600 bg-green-50',
      'modéré': 'text-yellow-600 bg-yellow-50',
      'difficile': 'text-red-600 bg-red-50'
    };
    return colors[difficulty] || 'text-gray-600 bg-gray-50';
  };

  return (
    <div
      onClick={() => onSelect(segment)}
      className={`btn-interactive bg-white rounded-lg border p-4 cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'border-orange-500 ring-2 ring-orange-100' : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-semibold text-gray-900 text-sm">{segment.name}</h3>
        <span className="text-orange-500">🏆</span>
      </div>

      <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
        <div className="flex items-center text-gray-600">
          <FiMapPin className="h-3 w-3 mr-1" />
          <span>{(segment.distance / 1000).toFixed(1)} km</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiTrendingUp className="h-3 w-3 mr-1" />
          <span>{segment.elevation}m D+</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiTarget className="h-3 w-3 mr-1" />
          <span>{segment.attempts || 156} tentatives</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiActivity className="h-3 w-3 mr-1" />
          <span>KOM: 12:34</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>
          {segment.difficulty}
        </span>
        <button className="btn-interactive touch-button-small text-orange-600 hover:text-orange-700 text-xs font-medium px-2 py-1 rounded focus:ring-orange-500">
          Tenter →
        </button>
      </div>
    </div>
  );
};

export default RoutesModern;
