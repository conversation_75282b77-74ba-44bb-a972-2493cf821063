import React, { useState, useEffect, useRef } from 'react';
import { FiPlay, FiPause, FiRefreshCw, FiSettings } from 'react-icons/fi';

const Timer = () => {
  // Timer presets
  const presets = [
    { name: 'HIIT', workTime: 45, restTime: 15, rounds: 8 },
    { name: '<PERSON><PERSON><PERSON>', workTime: 20, restTime: 10, rounds: 8 },
    { name: 'EMOM', workTime: 60, restTime: 0, rounds: 10 },
    { name: 'Pomodoro', workTime: 25 * 60, restTime: 5 * 60, rounds: 4 }
  ];

  // Timer state
  const [settings, setSettings] = useState(presets[0]);
  const [customSettings, setCustomSettings] = useState({
    workTime: 45,
    restTime: 15,
    rounds: 8
  });
  const [showSettings, setShowSettings] = useState(false);
  const [timeLeft, setTimeLeft] = useState(settings.workTime);
  const [isActive, setIsActive] = useState(false);
  const [isWorkPhase, setIsWorkPhase] = useState(true);
  const [currentRound, setCurrentRound] = useState(1);
  const [progress, setProgress] = useState(100);

  // Refs for interval and audio
  const timerInterval = useRef(null);
  const audioRef = useRef(null);

  // Initialize timer with selected preset
  useEffect(() => {
    resetTimer();
  }, [settings]); // eslint-disable-line react-hooks/exhaustive-deps

  // Timer logic
  useEffect(() => {
    if (isActive) {
      timerInterval.current = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime > 1) {
            // Update progress
            const totalTime = isWorkPhase ? settings.workTime : settings.restTime;
            const newProgress = ((prevTime - 1) / totalTime) * 100;
            setProgress(newProgress);
            return prevTime - 1;
          } else {
            // Time's up for current phase
            clearInterval(timerInterval.current);
            playSound();

            if (isWorkPhase) {
              // Work phase finished, start rest phase
              setIsWorkPhase(false);
              setTimeLeft(settings.restTime);
              setProgress(100);
              return 0;
            } else {
              // Rest phase finished
              if (currentRound < settings.rounds) {
                // Start next round
                setCurrentRound(prevRound => prevRound + 1);
                setIsWorkPhase(true);
                setTimeLeft(settings.workTime);
                setProgress(100);
                return 0;
              } else {
                // Workout completed
                setIsActive(false);
                return 0;
              }
            }
          }
        });
      }, 1000);
    }

    return () => clearInterval(timerInterval.current);
  }, [isActive, isWorkPhase, currentRound, settings]);

  // Play sound when phase changes
  const playSound = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(error => console.error('Error playing sound:', error));
    }
  };

  // Start or pause timer
  const toggleTimer = () => {
    setIsActive(!isActive);
  };

  // Reset timer to initial state
  const resetTimer = () => {
    clearInterval(timerInterval.current);
    setIsActive(false);
    setIsWorkPhase(true);
    setCurrentRound(1);
    setTimeLeft(settings.workTime);
    setProgress(100);
  };

  // Toggle settings panel
  const toggleSettings = () => {
    setShowSettings(!showSettings);
    if (isActive) {
      setIsActive(false);
    }
  };

  // Apply custom settings
  const applyCustomSettings = () => {
    setSettings({
      name: 'Custom',
      workTime: customSettings.workTime,
      restTime: customSettings.restTime,
      rounds: customSettings.rounds
    });
    setShowSettings(false);
    resetTimer();
  };

  // Apply preset
  const applyPreset = (preset) => {
    setSettings(preset);
    setShowSettings(false);
    resetTimer();
  };

  // Format time as mm:ss
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="py-12 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Minuteur d'entraînement</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Utilisez notre minuteur personnalisable pour vos entraînements HIIT, Tabata, EMOM ou créez votre propre configuration.
        </p>
      </div>

      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Timer display */}
        <div className="p-8 text-center">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-800">{settings.name}</h2>
            <p className="text-gray-600">
              {isWorkPhase ? 'Travail' : 'Repos'} • Round {currentRound}/{settings.rounds}
            </p>
          </div>

          <div className="mb-8">
            <div className="text-6xl font-bold mb-4 text-gray-900">
              {formatTime(timeLeft)}
            </div>

            {/* Progress bar */}
            <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full ${
                  isWorkPhase ? 'bg-primary' : 'bg-secondary'
                }`}
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex justify-center space-x-6">
            <button
              onClick={toggleTimer}
              className="p-4 rounded-full bg-primary text-white hover:bg-blue-600 transition-colors"
              aria-label={isActive ? 'Pause' : 'Play'}
            >
              {isActive ? <FiPause size={24} /> : <FiPlay size={24} />}
            </button>
            <button
              onClick={resetTimer}
              className="p-4 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors"
              aria-label="Reset"
            >
              <FiRefreshCw size={24} />
            </button>
            <button
              onClick={toggleSettings}
              className="p-4 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors"
              aria-label="Settings"
            >
              <FiSettings size={24} />
            </button>
          </div>
        </div>

        {/* Settings panel */}
        {showSettings && (
          <div className="bg-gray-50 p-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Préréglages</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
              {presets.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => applyPreset(preset)}
                  className={`py-2 px-4 rounded-md text-sm font-medium ${
                    settings.name === preset.name
                      ? 'bg-primary text-white'
                      : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {preset.name}
                </button>
              ))}
            </div>

            <h3 className="text-lg font-semibold mb-4">Configuration personnalisée</h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="workTime" className="block text-sm font-medium text-gray-700 mb-1">
                  Temps de travail (secondes)
                </label>
                <input
                  type="number"
                  id="workTime"
                  min="1"
                  max="3600"
                  value={customSettings.workTime}
                  onChange={(e) => setCustomSettings({ ...customSettings, workTime: parseInt(e.target.value) || 1 })}
                  className="input w-full"
                />
              </div>
              <div>
                <label htmlFor="restTime" className="block text-sm font-medium text-gray-700 mb-1">
                  Temps de repos (secondes)
                </label>
                <input
                  type="number"
                  id="restTime"
                  min="0"
                  max="3600"
                  value={customSettings.restTime}
                  onChange={(e) => setCustomSettings({ ...customSettings, restTime: parseInt(e.target.value) || 0 })}
                  className="input w-full"
                />
              </div>
              <div>
                <label htmlFor="rounds" className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre de rounds
                </label>
                <input
                  type="number"
                  id="rounds"
                  min="1"
                  max="20"
                  value={customSettings.rounds}
                  onChange={(e) => setCustomSettings({ ...customSettings, rounds: parseInt(e.target.value) || 1 })}
                  className="input w-full"
                />
              </div>
              <button
                onClick={applyCustomSettings}
                className="w-full btn btn-primary mt-4"
              >
                Appliquer
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Audio for timer alerts */}
      <audio ref={audioRef}>
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      {/* Timer instructions */}
      <div className="mt-12 max-w-3xl mx-auto">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Comment utiliser le minuteur</h2>
        <div className="bg-white rounded-lg shadow p-6">
          <ol className="list-decimal list-inside space-y-3 text-gray-700">
            <li>Sélectionnez un préréglage ou configurez votre propre minuteur</li>
            <li>Appuyez sur le bouton Play pour démarrer le minuteur</li>
            <li>Le minuteur alternera automatiquement entre les phases de travail et de repos</li>
            <li>Une alerte sonore retentira à la fin de chaque phase</li>
            <li>Utilisez le bouton Pause pour mettre le minuteur en pause à tout moment</li>
            <li>Utilisez le bouton Reset pour réinitialiser le minuteur</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default Timer;
