// Utilitaires pour les exercices
export const exerciseCategories = {
  cardio: {
    name: 'Cardio',
    icon: '❤️',
    color: 'red'
  },
  strength: {
    name: 'Musculation',
    icon: '💪',
    color: 'blue'
  },
  flexibility: {
    name: 'Flexibilité',
    icon: '🧘‍♀️',
    color: 'green'
  },
  balance: {
    name: 'Équilibre',
    icon: '⚖️',
    color: 'purple'
  },
  functional: {
    name: 'Fonctionnel',
    icon: '🏃‍♂️',
    color: 'orange'
  },
  core: {
    name: '<PERSON>ain<PERSON>',
    icon: '🔥',
    color: 'yellow'
  }
};

export const muscleGroups = {
  chest: { name: 'Pectoraux', icon: '💪' },
  back: { name: '<PERSON><PERSON>', icon: '🔙' },
  shoulders: { name: '<PERSON><PERSON><PERSON>', icon: '🤷‍♂️' },
  arms: { name: '<PERSON>ras', icon: '💪' },
  legs: { name: 'Jam<PERSON>', icon: '🦵' },
  glutes: { name: 'Fessiers', icon: '🍑' },
  core: { name: 'Abdomi<PERSON>ux', icon: '🔥' },
  fullBody: { name: 'Corps entier', icon: '🏃‍♂️' }
};

export const equipmentTypes = {
  none: { name: 'Aucun', icon: '🚫' },
  dumbbells: { name: 'Halt<PERSON>', icon: '🏋️‍♂️' },
  barbell: { name: '<PERSON>e', icon: '🏋️‍♀️' },
  resistance: { name: 'Élastiques', icon: '🔗' },
  machine: { name: 'Machine', icon: '⚙️' },
  cardio: { name: 'Cardio', icon: '🏃‍♂️' },
  mat: { name: 'Tapis', icon: '🧘‍♀️' },
  ball: { name: 'Ballon', icon: '⚽' }
};

export const exercises = [
  // Cardio
  {
    id: 1,
    name: 'Course à pied',
    category: 'cardio',
    muscleGroups: ['legs', 'core'],
    equipment: 'none',
    duration: 30,
    calories: 300,
    difficulty: 'Modéré',
    description: 'Course à rythme modéré pour améliorer l\'endurance cardiovasculaire.',
    instructions: [
      'Échauffez-vous pendant 5 minutes avec une marche rapide',
      'Maintenez un rythme constant et confortable',
      'Respirez régulièrement par le nez et la bouche',
      'Gardez une posture droite avec les épaules détendues',
      'Terminez par 5 minutes de marche et des étirements'
    ],
    tips: [
      'Commencez progressivement si vous êtes débutant',
      'Hydratez-vous avant, pendant et après l\'effort',
      'Portez des chaussures adaptées à la course'
    ],
    variations: [
      'Course en intervalles (alternance rapide/lent)',
      'Course en côte pour plus d\'intensité',
      'Course longue à rythme lent pour l\'endurance'
    ]
  },
  {
    id: 2,
    name: 'Vélo d\'appartement',
    category: 'cardio',
    muscleGroups: ['legs'],
    equipment: 'cardio',
    duration: 25,
    calories: 250,
    difficulty: 'Facile',
    description: 'Exercice cardiovasculaire à faible impact, idéal pour tous les niveaux.',
    instructions: [
      'Ajustez la hauteur de selle (jambe légèrement fléchie en bas)',
      'Commencez par un échauffement de 5 minutes à faible intensité',
      'Augmentez progressivement la résistance',
      'Maintenez une cadence de 60-80 tours par minute',
      'Terminez par 5 minutes de récupération active'
    ],
    tips: [
      'Gardez le dos droit et les épaules détendues',
      'Variez la résistance pour éviter la monotonie',
      'Utilisez un ventilateur pour rester au frais'
    ],
    variations: [
      'Intervalles haute intensité (HIIT)',
      'Simulation de montée avec résistance élevée',
      'Endurance longue à résistance modérée'
    ]
  },
  {
    id: 3,
    name: 'Burpees',
    category: 'cardio',
    muscleGroups: ['fullBody'],
    equipment: 'none',
    duration: 10,
    calories: 150,
    difficulty: 'Difficile',
    description: 'Exercice complet combinant cardio et renforcement musculaire.',
    instructions: [
      'Debout, pieds écartés largeur d\'épaules',
      'Accroupissez-vous et placez les mains au sol',
      'Sautez en arrière en position de planche',
      'Effectuez une pompe (optionnel)',
      'Ramenez les pieds vers les mains d\'un saut',
      'Sautez vers le haut avec les bras tendus'
    ],
    tips: [
      'Commencez par 3-5 répétitions si vous êtes débutant',
      'Concentrez-vous sur la technique avant la vitesse',
      'Respirez de manière contrôlée'
    ],
    variations: [
      'Burpees sans pompe pour débutants',
      'Burpees avec saut sur box',
      'Burpees avec haltères'
    ]
  },
  // Musculation - Haut du corps
  {
    id: 4,
    name: 'Pompes',
    category: 'strength',
    muscleGroups: ['chest', 'arms', 'core'],
    equipment: 'none',
    duration: 15,
    calories: 100,
    difficulty: 'Modéré',
    description: 'Exercice fondamental pour renforcer le haut du corps.',
    instructions: [
      'Position de planche, mains sous les épaules',
      'Corps aligné de la tête aux pieds',
      'Descendez lentement jusqu\'à ce que la poitrine touche presque le sol',
      'Poussez vers le haut en contractant les pectoraux',
      'Gardez les abdominaux contractés tout au long du mouvement'
    ],
    tips: [
      'Commencez sur les genoux si nécessaire',
      'Respirez en descendant, expirez en montant',
      'Gardez les coudes près du corps'
    ],
    variations: [
      'Pompes sur les genoux pour débutants',
      'Pompes inclinées (mains surélevées)',
      'Pompes diamant (mains en forme de diamant)',
      'Pompes déclinées (pieds surélevés)'
    ]
  },
  {
    id: 5,
    name: 'Tractions',
    category: 'strength',
    muscleGroups: ['back', 'arms'],
    equipment: 'machine',
    duration: 15,
    calories: 120,
    difficulty: 'Difficile',
    description: 'Exercice excellent pour développer la force du dos et des bras.',
    instructions: [
      'Suspendez-vous à une barre, mains en pronation',
      'Écartement des mains légèrement plus large que les épaules',
      'Tirez-vous vers le haut jusqu\'à ce que le menton dépasse la barre',
      'Descendez lentement en contrôlant le mouvement',
      'Gardez les jambes droites ou légèrement fléchies'
    ],
    tips: [
      'Utilisez une bande élastique pour assistance si nécessaire',
      'Concentrez-vous sur la contraction du dos',
      'Évitez les mouvements de balancier'
    ],
    variations: [
      'Tractions assistées avec élastique',
      'Tractions en supination (chin-ups)',
      'Tractions larges pour cibler le grand dorsal'
    ]
  },
  // Musculation - Bas du corps
  {
    id: 6,
    name: 'Squats',
    category: 'strength',
    muscleGroups: ['legs', 'glutes'],
    equipment: 'none',
    duration: 15,
    calories: 120,
    difficulty: 'Facile',
    description: 'Exercice roi pour renforcer les jambes et les fessiers.',
    instructions: [
      'Pieds écartés largeur d\'épaules, orteils légèrement vers l\'extérieur',
      'Descendez en poussant les hanches vers l\'arrière',
      'Gardez le dos droit et la poitrine sortie',
      'Descendez jusqu\'à ce que les cuisses soient parallèles au sol',
      'Remontez en poussant sur les talons'
    ],
    tips: [
      'Gardez les genoux alignés avec les orteils',
      'Ne laissez pas les genoux dépasser les orteils',
      'Contractez les fessiers en remontant'
    ],
    variations: [
      'Squats sumo (pieds très écartés)',
      'Squats sautés pour plus d\'intensité',
      'Squats avec haltères ou barre',
      'Squats sur une jambe (pistol squats)'
    ]
  },
  {
    id: 7,
    name: 'Fentes',
    category: 'strength',
    muscleGroups: ['legs', 'glutes'],
    equipment: 'none',
    duration: 15,
    calories: 110,
    difficulty: 'Modéré',
    description: 'Exercice unilatéral excellent pour l\'équilibre et la force des jambes.',
    instructions: [
      'Debout, faites un grand pas en avant',
      'Descendez en fléchissant les deux genoux à 90°',
      'Le genou arrière ne doit pas toucher le sol',
      'Poussez sur le talon avant pour revenir à la position initiale',
      'Alternez les jambes ou faites toutes les répétitions d\'un côté'
    ],
    tips: [
      'Gardez le buste droit tout au long du mouvement',
      'Le poids doit être réparti entre les deux jambes',
      'Commencez sans poids pour maîtriser le mouvement'
    ],
    variations: [
      'Fentes arrière (pas en arrière)',
      'Fentes latérales',
      'Fentes marchées',
      'Fentes avec haltères'
    ]
  },
  // Gainage et Core
  {
    id: 8,
    name: 'Planche',
    category: 'core',
    muscleGroups: ['core', 'shoulders'],
    equipment: 'mat',
    duration: 10,
    calories: 50,
    difficulty: 'Modéré',
    description: 'Exercice isométrique fondamental pour renforcer le core.',
    instructions: [
      'Position de pompe, appui sur les avant-bras',
      'Corps aligné de la tête aux pieds',
      'Contractez les abdominaux et les fessiers',
      'Respirez normalement',
      'Maintenez la position sans bouger'
    ],
    tips: [
      'Commencez par tenir 20-30 secondes',
      'Regardez vers le sol pour garder la nuque neutre',
      'Ne laissez pas les hanches monter ou descendre'
    ],
    variations: [
      'Planche sur les genoux pour débutants',
      'Planche latérale',
      'Planche avec levée de jambe',
      'Planche dynamique (mountain climbers)'
    ]
  },
  {
    id: 9,
    name: 'Crunchs',
    category: 'core',
    muscleGroups: ['core'],
    equipment: 'mat',
    duration: 10,
    calories: 60,
    difficulty: 'Facile',
    description: 'Exercice classique pour cibler les abdominaux supérieurs.',
    instructions: [
      'Allongé sur le dos, genoux fléchis',
      'Mains derrière la tête sans tirer sur la nuque',
      'Contractez les abdominaux pour soulever les épaules',
      'Montez jusqu\'à sentir la contraction maximale',
      'Redescendez lentement sans poser complètement'
    ],
    tips: [
      'Le mouvement vient des abdominaux, pas du cou',
      'Expirez en montant, inspirez en descendant',
      'Qualité plutôt que quantité'
    ],
    variations: [
      'Crunchs obliques (rotation)',
      'Crunchs vélo (bicycle crunches)',
      'Crunchs inversés (jambes vers la poitrine)'
    ]
  },
  // Flexibilité et Yoga
  {
    id: 10,
    name: 'Étirements complets',
    category: 'flexibility',
    muscleGroups: ['fullBody'],
    equipment: 'mat',
    duration: 20,
    calories: 50,
    difficulty: 'Facile',
    description: 'Séance complète d\'étirements pour améliorer la flexibilité.',
    instructions: [
      'Commencez par des étirements du cou et des épaules',
      'Étirez les bras et le dos',
      'Passez aux étirements des hanches et des jambes',
      'Maintenez chaque étirement 30 secondes minimum',
      'Respirez profondément et détendez-vous'
    ],
    tips: [
      'Ne forcez jamais un étirement',
      'La sensation doit être confortable',
      'Étirez-vous après l\'échauffement ou l\'entraînement'
    ],
    variations: [
      'Étirements dynamiques avant l\'entraînement',
      'Étirements statiques après l\'entraînement',
      'Étirements avec partenaire'
    ]
  }
];

export const getExercisesByCategory = (category) => {
  return exercises.filter(exercise => exercise.category === category);
};

export const getExercisesByMuscleGroup = (muscleGroup) => {
  return exercises.filter(exercise => exercise.muscleGroups.includes(muscleGroup));
};

export const getExercisesByEquipment = (equipment) => {
  return exercises.filter(exercise => exercise.equipment === equipment);
};

export const getExercisesByDifficulty = (difficulty) => {
  return exercises.filter(exercise => exercise.difficulty === difficulty);
};

export const getExerciseById = (id) => {
  return exercises.find(exercise => exercise.id === id);
};

export const getDifficultyColor = (difficulty) => {
  switch (difficulty.toLowerCase()) {
    case 'facile':
      return 'bg-green-100 text-green-800';
    case 'modéré':
      return 'bg-yellow-100 text-yellow-800';
    case 'difficile':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getRecommendedExercises = (userProfile, goal = 'general_fitness') => {
  let recommendedExercises = [];

  switch (goal) {
    case 'weight_loss':
      // Privilégier le cardio et les exercices complets
      recommendedExercises = exercises.filter(ex =>
        ex.category === 'cardio' ||
        ex.muscleGroups.includes('fullBody') ||
        ex.calories > 100
      );
      break;

    case 'muscle_gain':
      // Privilégier la musculation
      recommendedExercises = exercises.filter(ex =>
        ex.category === 'strength' || ex.category === 'core'
      );
      break;

    case 'endurance':
      // Privilégier le cardio et les exercices d'endurance
      recommendedExercises = exercises.filter(ex =>
        ex.category === 'cardio' ||
        ex.duration >= 20
      );
      break;

    case 'flexibility':
      // Privilégier la flexibilité et le yoga
      recommendedExercises = exercises.filter(ex =>
        ex.category === 'flexibility' || ex.category === 'balance'
      );
      break;

    default:
      // Programme équilibré
      recommendedExercises = exercises.filter(ex =>
        ex.difficulty !== 'Difficile' || Math.random() > 0.7
      );
  }

  // Adapter selon le niveau de l'utilisateur
  if (userProfile?.fitnessLevel === 'beginner') {
    recommendedExercises = recommendedExercises.filter(ex =>
      ex.difficulty === 'Facile' || (ex.difficulty === 'Modéré' && Math.random() > 0.5)
    );
  } else if (userProfile?.fitnessLevel === 'advanced') {
    recommendedExercises = recommendedExercises.filter(ex =>
      ex.difficulty !== 'Facile' || Math.random() > 0.3
    );
  }

  // Mélanger et retourner un nombre limité
  return recommendedExercises
    .sort(() => 0.5 - Math.random())
    .slice(0, 8);
};

export const createWorkoutPlan = (exercises, duration = 30) => {
  let totalDuration = 0;
  const selectedExercises = [];

  // Trier par priorité (cardio d'abord, puis force, puis flexibilité)
  const priorityOrder = ['cardio', 'strength', 'core', 'flexibility'];
  const sortedExercises = exercises.sort((a, b) => {
    return priorityOrder.indexOf(a.category) - priorityOrder.indexOf(b.category);
  });

  for (const exercise of sortedExercises) {
    if (totalDuration + exercise.duration <= duration) {
      selectedExercises.push(exercise);
      totalDuration += exercise.duration;
    }

    if (totalDuration >= duration * 0.9) break; // 90% du temps cible
  }

  return {
    exercises: selectedExercises,
    totalDuration,
    estimatedCalories: selectedExercises.reduce((sum, ex) => sum + ex.calories, 0)
  };
};

export const searchExercises = (query, filters = {}) => {
  let filteredExercises = exercises;

  // Filtrer par texte de recherche
  if (query) {
    const searchTerm = query.toLowerCase();
    filteredExercises = filteredExercises.filter(exercise =>
      exercise.name.toLowerCase().includes(searchTerm) ||
      exercise.description.toLowerCase().includes(searchTerm) ||
      exercise.muscleGroups.some(mg => muscleGroups[mg]?.name.toLowerCase().includes(searchTerm))
    );
  }

  // Filtrer par catégorie
  if (filters.category && filters.category !== 'all') {
    filteredExercises = filteredExercises.filter(ex => ex.category === filters.category);
  }

  // Filtrer par groupe musculaire
  if (filters.muscleGroup && filters.muscleGroup !== 'all') {
    filteredExercises = filteredExercises.filter(ex => ex.muscleGroups.includes(filters.muscleGroup));
  }

  // Filtrer par équipement
  if (filters.equipment && filters.equipment !== 'all') {
    filteredExercises = filteredExercises.filter(ex => ex.equipment === filters.equipment);
  }

  // Filtrer par difficulté
  if (filters.difficulty && filters.difficulty !== 'all') {
    filteredExercises = filteredExercises.filter(ex => ex.difficulty === filters.difficulty);
  }

  // Filtrer par durée
  if (filters.maxDuration) {
    filteredExercises = filteredExercises.filter(ex => ex.duration <= filters.maxDuration);
  }

  return filteredExercises;
};

// Gestion des favoris
export const toggleFavorite = (exerciseId, favorites, setFavorites) => {
  const isFavorite = favorites.includes(exerciseId);
  let newFavorites;

  if (isFavorite) {
    newFavorites = favorites.filter(id => id !== exerciseId);
  } else {
    newFavorites = [...favorites, exerciseId];
  }

  setFavorites(newFavorites);
  localStorage.setItem('exerciseFavorites', JSON.stringify(newFavorites));
  return !isFavorite;
};

// Charger les favoris depuis localStorage
export const loadFavorites = () => {
  try {
    const saved = localStorage.getItem('exerciseFavorites');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Erreur lors du chargement des favoris:', error);
    return [];
  }
};

// Gestion de l'historique des exercices
export const addToHistory = (exercise, performance = {}) => {
  try {
    const history = getExerciseHistory();
    const entry = {
      id: Date.now(),
      exerciseId: exercise.id,
      exerciseName: exercise.name,
      date: new Date().toISOString(),
      performance: {
        sets: performance.sets || 3,
        reps: performance.reps || 12,
        weight: performance.weight || null,
        duration: performance.duration || null,
        notes: performance.notes || ''
      }
    };

    const newHistory = [entry, ...history].slice(0, 100); // Garder seulement les 100 dernières entrées
    localStorage.setItem('exerciseHistory', JSON.stringify(newHistory));
    return entry;
  } catch (error) {
    console.error('Erreur lors de l\'ajout à l\'historique:', error);
    return null;
  }
};

// Charger l'historique des exercices
export const getExerciseHistory = () => {
  try {
    const saved = localStorage.getItem('exerciseHistory');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Erreur lors du chargement de l\'historique:', error);
    return [];
  }
};

// Obtenir l'historique d'un exercice spécifique
export const getExerciseHistoryById = (exerciseId) => {
  const history = getExerciseHistory();
  return history.filter(entry => entry.exerciseId === exerciseId);
};

// Calculer les statistiques de progression
export const calculateProgressStats = (exerciseId) => {
  const history = getExerciseHistoryById(exerciseId);

  if (history.length === 0) {
    return {
      totalSessions: 0,
      lastPerformed: null,
      bestPerformance: null,
      progressTrend: 'stable'
    };
  }

  const sortedHistory = history.sort((a, b) => new Date(a.date) - new Date(b.date));
  const recent = sortedHistory.slice(-5); // 5 dernières sessions

  // Calculer la tendance de progression
  let progressTrend = 'stable';
  if (recent.length >= 2) {
    const firstRecent = recent[0];
    const lastRecent = recent[recent.length - 1];

    if (firstRecent.performance.weight && lastRecent.performance.weight) {
      if (lastRecent.performance.weight > firstRecent.performance.weight) {
        progressTrend = 'improving';
      } else if (lastRecent.performance.weight < firstRecent.performance.weight) {
        progressTrend = 'declining';
      }
    }
  }

  return {
    totalSessions: history.length,
    lastPerformed: history[0].date,
    bestPerformance: history.reduce((best, current) => {
      if (!best) return current;
      if (current.performance.weight > (best.performance.weight || 0)) {
        return current;
      }
      return best;
    }, null),
    progressTrend,
    recentHistory: recent
  };
};

// Créer un programme d'entraînement personnalisé
export const createCustomProgram = (name, exercises, settings = {}) => {
  const program = {
    id: Date.now(),
    name: name || 'Programme personnalisé',
    type: 'custom',
    exercises: exercises.map(exercise => ({
      ...exercise,
      sets: settings.defaultSets || 3,
      reps: settings.defaultReps || 12,
      rest: settings.defaultRest || '60s'
    })),
    settings: {
      difficulty: settings.difficulty || 'intermediate',
      duration: settings.duration || '30 min',
      equipment: settings.equipment || 'mixed',
      ...settings
    },
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString()
  };

  // Sauvegarder le programme
  const programs = getCustomPrograms();
  const newPrograms = [program, ...programs];
  localStorage.setItem('customPrograms', JSON.stringify(newPrograms));

  return program;
};

// Charger les programmes personnalisés
export const getCustomPrograms = () => {
  try {
    const saved = localStorage.getItem('customPrograms');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Erreur lors du chargement des programmes:', error);
    return [];
  }
};

// Supprimer un programme personnalisé
export const deleteCustomProgram = (programId) => {
  try {
    const programs = getCustomPrograms();
    const filtered = programs.filter(program => program.id !== programId);
    localStorage.setItem('customPrograms', JSON.stringify(filtered));
    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression du programme:', error);
    return false;
  }
};

// Simuler des vidéos d'exercices (URLs d'exemple)
export const getExerciseVideo = (exerciseId) => {
  // En production, ceci serait connecté à une vraie base de données de vidéos
  const videoUrls = {
    1: 'https://example.com/videos/pompes.mp4',
    2: 'https://example.com/videos/squats.mp4',
    3: 'https://example.com/videos/planche.mp4',
    4: 'https://example.com/videos/burpees.mp4',
    5: 'https://example.com/videos/jumping-jacks.mp4',
    6: 'https://example.com/videos/mountain-climbers.mp4',
    7: 'https://example.com/videos/fentes.mp4',
    8: 'https://example.com/videos/dips.mp4',
    9: 'https://example.com/videos/pull-ups.mp4',
    10: 'https://example.com/videos/crunchs.mp4'
  };

  return videoUrls[exerciseId] || null;
};

// Obtenir une animation/GIF pour un exercice
export const getExerciseAnimation = (exerciseId) => {
  // En production, ceci serait connecté à une vraie base de données d'animations
  const animationUrls = {
    1: '/animations/pompes.gif',
    2: '/animations/squats.gif',
    3: '/animations/planche.gif',
    4: '/animations/burpees.gif',
    5: '/animations/jumping-jacks.gif',
    6: '/animations/mountain-climbers.gif',
    7: '/animations/fentes.gif',
    8: '/animations/dips.gif',
    9: '/animations/pull-ups.gif',
    10: '/animations/crunchs.gif'
  };

  return animationUrls[exerciseId] || '/animations/default-exercise.gif';
};
