// Utilitaires pour la gestion des cartes et des routes

/**
 * Calcule la distance entre deux points géographiques (formule de Haversine)
 */
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Calcule l'élévation simulée basée sur la position géographique
 */
export const calculateElevation = (lat, lon) => {
  // Simulation d'élévation basée sur des patterns géographiques
  const baseElevation = Math.sin(lat * 0.1) * Math.cos(lon * 0.1) * 500;
  const noise = (Math.sin(lat * 10) + Math.cos(lon * 10)) * 50;
  return Math.max(0, baseElevation + noise + 100);
};

/**
 * Calcule la pente entre deux points
 */
export const calculateSlope = (point1, point2) => {
  const distance = calculateDistance(point1.lat, point1.lng, point2.lat, point2.lng) * 1000; // en mètres
  const elevationDiff = point2.elevation - point1.elevation;
  return distance > 0 ? (elevationDiff / distance) * 100 : 0; // en pourcentage
};

/**
 * Détermine le niveau de difficulté basé sur la pente et la distance
 */
export const calculateDifficulty = (route) => {
  if (!route.points || route.points.length < 2) return 'facile';
  
  let totalSlope = 0;
  let maxSlope = 0;
  
  for (let i = 1; i < route.points.length; i++) {
    const slope = Math.abs(calculateSlope(route.points[i-1], route.points[i]));
    totalSlope += slope;
    maxSlope = Math.max(maxSlope, slope);
  }
  
  const avgSlope = totalSlope / (route.points.length - 1);
  const distance = route.distance || 0;
  
  // Calcul de difficulté basé sur pente moyenne, pente max et distance
  if (maxSlope > 15 || avgSlope > 8 || distance > 20) return 'difficile';
  if (maxSlope > 8 || avgSlope > 4 || distance > 10) return 'modéré';
  return 'facile';
};

/**
 * Génère des routes populaires simulées autour d'une position
 */
export const generatePopularRoutes = (centerLat, centerLng, count = 10) => {
  const routes = [];
  
  for (let i = 0; i < count; i++) {
    const angle = (i / count) * 2 * Math.PI;
    const distance = 2 + Math.random() * 15; // 2-17 km
    const points = generateRoutePoints(centerLat, centerLng, angle, distance);
    
    const route = {
      id: `route_${i + 1}`,
      name: `Parcours ${getRouteTypeName()} ${i + 1}`,
      type: getRandomRouteType(),
      distance: distance,
      points: points,
      elevation: {
        gain: calculateElevationGain(points),
        loss: calculateElevationLoss(points),
        max: Math.max(...points.map(p => p.elevation)),
        min: Math.min(...points.map(p => p.elevation))
      },
      difficulty: '',
      rating: 3 + Math.random() * 2, // 3-5 étoiles
      completions: Math.floor(Math.random() * 500) + 50,
      createdBy: getRandomCreator(),
      tags: getRandomTags(),
      description: generateRouteDescription()
    };
    
    route.difficulty = calculateDifficulty(route);
    routes.push(route);
  }
  
  return routes.sort((a, b) => b.completions - a.completions);
};

/**
 * Génère des points pour une route
 */
const generateRoutePoints = (startLat, startLng, angle, distance) => {
  const points = [];
  const numPoints = Math.floor(distance * 2) + 5; // Plus de points pour les routes longues
  
  for (let i = 0; i < numPoints; i++) {
    const progress = i / (numPoints - 1);
    const currentDistance = distance * progress;
    
    // Ajouter de la variation à l'angle pour créer des courbes
    const variation = Math.sin(progress * Math.PI * 4) * 0.3;
    const currentAngle = angle + variation;
    
    const lat = startLat + (currentDistance / 111) * Math.cos(currentAngle);
    const lng = startLng + (currentDistance / (111 * Math.cos(startLat * Math.PI / 180))) * Math.sin(currentAngle);
    
    points.push({
      lat: lat,
      lng: lng,
      elevation: calculateElevation(lat, lng)
    });
  }
  
  return points;
};

/**
 * Calcule le dénivelé positif
 */
const calculateElevationGain = (points) => {
  let gain = 0;
  for (let i = 1; i < points.length; i++) {
    const diff = points[i].elevation - points[i-1].elevation;
    if (diff > 0) gain += diff;
  }
  return Math.round(gain);
};

/**
 * Calcule le dénivelé négatif
 */
const calculateElevationLoss = (points) => {
  let loss = 0;
  for (let i = 1; i < points.length; i++) {
    const diff = points[i-1].elevation - points[i].elevation;
    if (diff > 0) loss += diff;
  }
  return Math.round(loss);
};

/**
 * Types de routes aléatoires
 */
const getRandomRouteType = () => {
  const types = ['running', 'cycling', 'hiking', 'walking'];
  return types[Math.floor(Math.random() * types.length)];
};

const getRouteTypeName = () => {
  const names = ['du Parc', 'de la Forêt', 'du Lac', 'de la Colline', 'Urbain', 'Panoramique', 'Historique'];
  return names[Math.floor(Math.random() * names.length)];
};

/**
 * Créateurs aléatoires
 */
const getRandomCreator = () => {
  const creators = [
    'Marie L.', 'Thomas B.', 'Sophie M.', 'Pierre D.', 'Julie R.',
    'Antoine C.', 'Camille P.', 'Nicolas F.', 'Emma T.', 'Lucas G.'
  ];
  return creators[Math.floor(Math.random() * creators.length)];
};

/**
 * Tags aléatoires
 */
const getRandomTags = () => {
  const allTags = [
    'scenic', 'challenging', 'beginner-friendly', 'family', 'nature',
    'urban', 'historic', 'waterfront', 'mountain', 'forest'
  ];
  const numTags = 2 + Math.floor(Math.random() * 3);
  const shuffled = allTags.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numTags);
};

/**
 * Descriptions aléatoires
 */
const generateRouteDescription = () => {
  const descriptions = [
    'Un magnifique parcours offrant des vues panoramiques sur la région.',
    'Idéal pour les débutants, ce tracé combine nature et facilité d\'accès.',
    'Parcours technique avec quelques montées, parfait pour se challenger.',
    'Route familiale sécurisée avec de nombreux points d\'intérêt.',
    'Tracé urbain dynamique traversant les quartiers historiques.',
    'Parcours nature au cœur de la forêt, très rafraîchissant.',
    'Route côtière avec vue sur l\'eau, particulièrement belle au coucher du soleil.'
  ];
  return descriptions[Math.floor(Math.random() * descriptions.length)];
};

/**
 * Génère des points d'intérêt (POI) autour d'une position
 */
export const generatePOIs = (centerLat, centerLng, radius = 5) => {
  const poiTypes = [
    { type: 'fountain', name: 'Fontaine', icon: '⛲', color: 'blue' },
    { type: 'viewpoint', name: 'Point de vue', icon: '🏔️', color: 'green' },
    { type: 'restroom', name: 'Toilettes', icon: '🚻', color: 'purple' },
    { type: 'parking', name: 'Parking', icon: '🅿️', color: 'gray' },
    { type: 'cafe', name: 'Café', icon: '☕', color: 'brown' },
    { type: 'bench', name: 'Banc', icon: '🪑', color: 'orange' },
    { type: 'shelter', name: 'Abri', icon: '🏠', color: 'red' }
  ];
  
  const pois = [];
  const numPOIs = 15 + Math.floor(Math.random() * 10);
  
  for (let i = 0; i < numPOIs; i++) {
    const angle = Math.random() * 2 * Math.PI;
    const distance = Math.random() * radius;
    const poiType = poiTypes[Math.floor(Math.random() * poiTypes.length)];
    
    const lat = centerLat + (distance / 111) * Math.cos(angle);
    const lng = centerLng + (distance / (111 * Math.cos(centerLat * Math.PI / 180))) * Math.sin(angle);
    
    pois.push({
      id: `poi_${i + 1}`,
      type: poiType.type,
      name: `${poiType.name} ${i + 1}`,
      icon: poiType.icon,
      color: poiType.color,
      lat: lat,
      lng: lng,
      elevation: calculateElevation(lat, lng),
      description: `${poiType.name} situé dans un endroit pratique`,
      addedBy: getRandomCreator(),
      rating: 3 + Math.random() * 2,
      verified: Math.random() > 0.3
    });
  }
  
  return pois;
};

/**
 * Optimise une route pour passer par des POIs sélectionnés
 */
export const optimizeRouteWithPOIs = (startPoint, endPoint, selectedPOIs) => {
  if (!selectedPOIs || selectedPOIs.length === 0) {
    return generateDirectRoute(startPoint, endPoint);
  }
  
  // Algorithme simple de plus proche voisin pour optimiser l'ordre des POIs
  const optimizedPoints = [startPoint];
  const remaining = [...selectedPOIs];
  
  let current = startPoint;
  
  while (remaining.length > 0) {
    let nearest = remaining[0];
    let nearestIndex = 0;
    let minDistance = calculateDistance(current.lat, current.lng, nearest.lat, nearest.lng);
    
    for (let i = 1; i < remaining.length; i++) {
      const distance = calculateDistance(current.lat, current.lng, remaining[i].lat, remaining[i].lng);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = remaining[i];
        nearestIndex = i;
      }
    }
    
    optimizedPoints.push(nearest);
    remaining.splice(nearestIndex, 1);
    current = nearest;
  }
  
  optimizedPoints.push(endPoint);
  
  // Générer les points de route entre chaque waypoint
  const routePoints = [];
  for (let i = 0; i < optimizedPoints.length - 1; i++) {
    const segmentPoints = generateDirectRoute(optimizedPoints[i], optimizedPoints[i + 1]);
    routePoints.push(...segmentPoints.slice(0, -1)); // Éviter les doublons
  }
  routePoints.push(optimizedPoints[optimizedPoints.length - 1]);
  
  return routePoints;
};

/**
 * Génère une route directe entre deux points
 */
const generateDirectRoute = (start, end) => {
  const points = [];
  const numPoints = 10;
  
  for (let i = 0; i <= numPoints; i++) {
    const progress = i / numPoints;
    const lat = start.lat + (end.lat - start.lat) * progress;
    const lng = start.lng + (end.lng - start.lng) * progress;
    
    points.push({
      lat: lat,
      lng: lng,
      elevation: calculateElevation(lat, lng)
    });
  }
  
  return points;
};

/**
 * Position par défaut (Paris)
 */
export const DEFAULT_POSITION = {
  lat: 48.8566,
  lng: 2.3522
};

/**
 * Génère des segments pour les classements
 */
export const generateSegments = (centerLat, centerLng, count = 10) => {
  const segments = [
    {
      id: 'seg_1',
      name: 'Montée du Sacré-Cœur',
      distance: 850,
      elevation: 65,
      difficulty: 'Difficile',
      type: 'climb',
      sport: 'running',
      surface: 'road',
      attempts: 234,
      averageTime: '4:12',
      recordTime: '3:45',
      points: generateRoutePoints(centerLat + 0.01, centerLng + 0.005, 0.85, 15),
      description: 'Segment emblématique avec une belle montée vers Montmartre'
    },
    {
      id: 'seg_2',
      name: 'Sprint des Champs',
      distance: 400,
      elevation: 5,
      difficulty: 'Modéré',
      type: 'sprint',
      sport: 'running',
      surface: 'road',
      attempts: 456,
      averageTime: '1:28',
      recordTime: '1:15',
      points: generateRoutePoints(centerLat - 0.008, centerLng + 0.01, 0.4, 8),
      description: 'Sprint plat idéal pour tester sa vitesse de pointe'
    },
    {
      id: 'seg_3',
      name: 'Boucle du Bois',
      distance: 2100,
      elevation: 25,
      difficulty: 'Facile',
      type: 'loop',
      sport: 'cycling',
      surface: 'mixed',
      attempts: 189,
      averageTime: '6:45',
      recordTime: '5:58',
      points: generateRoutePoints(centerLat + 0.015, centerLng - 0.01, 2.1, 25),
      description: 'Belle boucle dans un cadre verdoyant, parfaite pour le vélo'
    },
    {
      id: 'seg_4',
      name: 'Côte de Belleville',
      distance: 650,
      elevation: 45,
      difficulty: 'Difficile',
      type: 'climb',
      sport: 'running',
      surface: 'road',
      attempts: 167,
      averageTime: '3:22',
      recordTime: '2:58',
      points: generateRoutePoints(centerLat - 0.005, centerLng - 0.008, 0.65, 12),
      description: 'Montée technique dans le quartier de Belleville'
    },
    {
      id: 'seg_5',
      name: 'Descente des Martyrs',
      distance: 750,
      elevation: -35,
      difficulty: 'Modéré',
      type: 'descent',
      sport: 'running',
      surface: 'road',
      attempts: 298,
      averageTime: '2:45',
      recordTime: '2:18',
      points: generateRoutePoints(centerLat + 0.008, centerLng + 0.012, 0.75, 14),
      description: 'Descente rapide avec quelques virages techniques'
    },
    {
      id: 'seg_6',
      name: 'Trail de la Butte',
      distance: 1200,
      elevation: 80,
      difficulty: 'Expert',
      type: 'trail',
      sport: 'trail',
      surface: 'trail',
      attempts: 89,
      averageTime: '8:15',
      recordTime: '6:42',
      points: generateRoutePoints(centerLat + 0.02, centerLng + 0.008, 1.2, 20),
      description: 'Segment trail technique avec dénivelé important'
    },
    {
      id: 'seg_7',
      name: 'Ligne droite République',
      distance: 600,
      elevation: 8,
      difficulty: 'Facile',
      type: 'flat',
      sport: 'cycling',
      surface: 'road',
      attempts: 512,
      averageTime: '1:45',
      recordTime: '1:28',
      points: generateRoutePoints(centerLat - 0.01, centerLng + 0.015, 0.6, 10),
      description: 'Segment plat et rapide, idéal pour les cyclistes'
    },
    {
      id: 'seg_8',
      name: 'Escaliers de Montmartre',
      distance: 320,
      elevation: 55,
      difficulty: 'Expert',
      type: 'stairs',
      sport: 'running',
      surface: 'stairs',
      attempts: 145,
      averageTime: '2:58',
      recordTime: '2:12',
      points: generateRoutePoints(centerLat + 0.012, centerLng + 0.003, 0.32, 8),
      description: 'Défi ultime : montée des escaliers de Montmartre'
    }
  ];

  return segments;
};

const calculateTotalSegmentDistance = (points) => {
  let total = 0;
  for (let i = 1; i < points.length; i++) {
    total += calculateDistance(
      points[i-1].lat, points[i-1].lng,
      points[i].lat, points[i].lng
    );
  }
  return total;
};

const generateSegmentLeaderboard = () => {
  const athletes = [
    'Marie Dubois', 'Thomas Martin', 'Sophie Bernard', 'Pierre Durand',
    'Julie Moreau', 'Antoine Petit', 'Camille Roux', 'Nicolas Leroy',
    'Emma Fournier', 'Lucas Girard', 'Léa Bonnet', 'Hugo Dupont'
  ];

  const leaderboard = [];
  const numEntries = 5 + Math.floor(Math.random() * 10);

  for (let i = 0; i < numEntries; i++) {
    const baseTime = 300 + Math.random() * 1800; // 5-35 minutes
    const time = baseTime + (i * 10) + Math.random() * 30;

    leaderboard.push({
      rank: i + 1,
      athlete: athletes[Math.floor(Math.random() * athletes.length)],
      time: Math.round(time),
      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
      pace: time / 1000, // pace par km en secondes
      isLocalLegend: i === 0 && Math.random() > 0.7
    });
  }

  return leaderboard.sort((a, b) => a.time - b.time);
};
