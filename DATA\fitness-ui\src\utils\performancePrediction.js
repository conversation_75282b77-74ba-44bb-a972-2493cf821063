// Performance prediction algorithms for running distances

/**
 * Calculate race time predictions based on current performance
 * Uses <PERSON><PERSON><PERSON>'s formula and other established running prediction models
 */

// <PERSON><PERSON><PERSON>'s formula constants
const RIEGEL_EXPONENT = 1.06;

// Distance constants in meters
export const DISTANCES = {
  '5K': 5000,
  '10K': 10000,
  'HALF_MARATHON': 21097.5,
  'MARATHON': 42195
};

// Training zones based on heart rate percentage
export const TRAINING_ZONES = {
  RECOVERY: { min: 50, max: 60, name: 'R<PERSON>cup<PERSON>' },
  AEROBIC: { min: 60, max: 70, name: '<PERSON><PERSON><PERSON><PERSON>' },
  TEMPO: { min: 70, max: 80, name: 'Tempo' },
  THRESHOLD: { min: 80, max: 90, name: '<PERSON><PERSON>' },
  VO2_MAX: { min: 90, max: 95, name: 'VO2 Max' },
  NEUROMUSCULAR: { min: 95, max: 100, name: 'Neuromusculaire' }
};

/**
 * Convert time string (HH:MM:SS or MM:SS) to seconds
 */
export const timeToSeconds = (timeString) => {
  const parts = timeString.split(':').map(Number);
  if (parts.length === 2) {
    return parts[0] * 60 + parts[1]; // MM:SS
  } else if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2]; // HH:MM:SS
  }
  return 0;
};

/**
 * Convert seconds to time string (HH:MM:SS or MM:SS)
 */
export const secondsToTime = (seconds, includeHours = false) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (includeHours || hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Calculate pace in minutes per kilometer
 */
export const calculatePace = (timeInSeconds, distanceInMeters) => {
  const pacePerKm = (timeInSeconds / (distanceInMeters / 1000)) / 60;
  return pacePerKm;
};

/**
 * Predict race time using Riegel's formula
 */
export const predictRaceTime = (knownTime, knownDistance, targetDistance) => {
  const ratio = targetDistance / knownDistance;
  const predictedTime = knownTime * Math.pow(ratio, RIEGEL_EXPONENT);
  return predictedTime;
};

/**
 * Calculate all race predictions from a known performance
 */
export const calculateRacePredictions = (knownTime, knownDistance) => {
  const predictions = {};
  
  Object.entries(DISTANCES).forEach(([raceName, distance]) => {
    if (distance !== knownDistance) {
      const predictedTime = predictRaceTime(knownTime, knownDistance, distance);
      const pace = calculatePace(predictedTime, distance);
      
      predictions[raceName] = {
        time: predictedTime,
        timeFormatted: secondsToTime(predictedTime, predictedTime >= 3600),
        pace: pace,
        paceFormatted: secondsToTime(pace * 60),
        distance: distance
      };
    }
  });
  
  return predictions;
};

/**
 * Calculate VO2 Max estimate based on race performance
 * Uses Jack Daniels' formula
 */
export const estimateVO2Max = (timeInSeconds, distanceInMeters) => {
  const velocityMPerMin = distanceInMeters / (timeInSeconds / 60);
  const vo2Max = -4.6 + 0.182258 * velocityMPerMin + 0.000104 * Math.pow(velocityMPerMin, 2);
  return Math.max(vo2Max, 30); // Minimum reasonable VO2 Max
};

/**
 * Calculate training paces based on current fitness level
 */
export const calculateTrainingPaces = (vo2Max, userAge = 30) => {
  // Calculate VDOT (running fitness index)
  const vdot = vo2Max;
  
  // Training pace calculations based on Jack Daniels' formulas
  const easyPace = 15.3 * Math.pow(vdot, -0.515); // minutes per km
  const marathonPace = 15.3 * Math.pow(vdot * 0.8, -0.515);
  const thresholdPace = 15.3 * Math.pow(vdot * 0.88, -0.515);
  const intervalPace = 15.3 * Math.pow(vdot, -0.515) * 0.85;
  const repetitionPace = 15.3 * Math.pow(vdot * 1.1, -0.515);
  
  return {
    easy: {
      pace: easyPace,
      paceFormatted: secondsToTime(easyPace * 60),
      hrZone: TRAINING_ZONES.AEROBIC,
      description: 'Allure facile pour l\'endurance de base'
    },
    marathon: {
      pace: marathonPace,
      paceFormatted: secondsToTime(marathonPace * 60),
      hrZone: TRAINING_ZONES.TEMPO,
      description: 'Allure marathon pour les sorties longues'
    },
    threshold: {
      pace: thresholdPace,
      paceFormatted: secondsToTime(thresholdPace * 60),
      hrZone: TRAINING_ZONES.THRESHOLD,
      description: 'Allure seuil pour améliorer l\'endurance'
    },
    interval: {
      pace: intervalPace,
      paceFormatted: secondsToTime(intervalPace * 60),
      hrZone: TRAINING_ZONES.VO2_MAX,
      description: 'Allure intervalles pour développer VO2 Max'
    },
    repetition: {
      pace: repetitionPace,
      paceFormatted: secondsToTime(repetitionPace * 60),
      hrZone: TRAINING_ZONES.NEUROMUSCULAR,
      description: 'Allure répétitions pour la vitesse'
    }
  };
};

/**
 * Generate personalized training recommendations
 */
export const generateTrainingRecommendations = (userProfile, currentPerformance) => {
  const { fitnessLevel, goals } = userProfile;
  const recommendations = [];

  // Base recommendations by fitness level
  const baseRecommendations = {
    beginner: {
      weeklyDistance: 20,
      workoutsPerWeek: 3,
      longRunPercentage: 30,
      speedWorkPercentage: 10
    },
    intermediate: {
      weeklyDistance: 40,
      workoutsPerWeek: 4,
      longRunPercentage: 25,
      speedWorkPercentage: 20
    },
    advanced: {
      weeklyDistance: 60,
      workoutsPerWeek: 5,
      longRunPercentage: 20,
      speedWorkPercentage: 25
    }
  };

  const base = baseRecommendations[fitnessLevel] || baseRecommendations.intermediate;

  // Adjust based on goals
  if (goals.includes('weight_loss')) {
    recommendations.push({
      type: 'endurance',
      title: 'Entraînement pour la perte de poids',
      description: 'Privilégiez les sorties longues à allure modérée',
      frequency: '4-5 fois par semaine',
      duration: '45-60 minutes',
      intensity: 'Modérée (60-70% FCM)'
    });
  }

  if (goals.includes('endurance')) {
    recommendations.push({
      type: 'endurance',
      title: 'Développement de l\'endurance',
      description: 'Augmentez progressivement la distance de vos sorties longues',
      frequency: '1 fois par semaine',
      duration: '90-120 minutes',
      intensity: 'Facile (60-65% FCM)'
    });
  }

  if (goals.includes('speed')) {
    recommendations.push({
      type: 'speed',
      title: 'Amélioration de la vitesse',
      description: 'Intégrez des séances d\'intervalles courts',
      frequency: '2 fois par semaine',
      duration: '30-45 minutes',
      intensity: 'Élevée (85-95% FCM)'
    });
  }

  // Weekly structure recommendation
  recommendations.push({
    type: 'structure',
    title: 'Structure hebdomadaire recommandée',
    description: `${base.workoutsPerWeek} entraînements par semaine, ${base.weeklyDistance}km total`,
    details: [
      `Sortie longue: ${Math.round(base.weeklyDistance * base.longRunPercentage / 100)}km`,
      `Travail de vitesse: ${Math.round(base.weeklyDistance * base.speedWorkPercentage / 100)}km`,
      `Endurance facile: ${Math.round(base.weeklyDistance * (100 - base.longRunPercentage - base.speedWorkPercentage) / 100)}km`
    ]
  });

  return recommendations;
};

/**
 * Calculate performance trends and forecasting
 */
export const calculatePerformanceTrends = (performanceHistory) => {
  if (!performanceHistory || performanceHistory.length < 2) {
    return null;
  }

  // Sort by date
  const sortedHistory = [...performanceHistory].sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // Calculate trend for each distance
  const trends = {};
  
  Object.keys(DISTANCES).forEach(distance => {
    const distanceData = sortedHistory.filter(p => p.distance === distance);
    
    if (distanceData.length >= 2) {
      const firstTime = distanceData[0].time;
      const lastTime = distanceData[distanceData.length - 1].time;
      const improvement = firstTime - lastTime; // Positive = improvement
      const improvementPercentage = (improvement / firstTime) * 100;
      
      trends[distance] = {
        improvement: improvement,
        improvementPercentage: improvementPercentage,
        trend: improvement > 0 ? 'improving' : improvement < 0 ? 'declining' : 'stable',
        dataPoints: distanceData.length
      };
    }
  });
  
  return trends;
};
