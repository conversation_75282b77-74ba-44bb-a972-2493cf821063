// Utilitaires pour les fonctionnalités sociales et compétitives

/**
 * Génère des utilisateurs fictifs pour les fonctionnalités sociales
 */
export const generateMockUsers = (count = 50) => {
  const firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>h<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
  ];
  
  const lastNames = [
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ier'
  ];
  
  const cities = [
    'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nan<PERSON>', 'Strasbourg', 'Montpellier',
    'Bordeaux', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Le Havre', '<PERSON><PERSON><PERSON>', 'Toulon', 'Grenoble'
  ];
  
  const users = [];
  
  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const joinDate = new Date(Date.now() - Math.random() * 2 * 365 * 24 * 60 * 60 * 1000);
    
    users.push({
      id: `user_${i + 1}`,
      firstName,
      lastName,
      username: `${firstName.toLowerCase()}${lastName.toLowerCase()}${Math.floor(Math.random() * 100)}`,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      avatar: `https://ui-avatars.com/api/?name=${firstName}+${lastName}&background=random`,
      city: cities[Math.floor(Math.random() * cities.length)],
      joinDate,
      stats: generateUserStats(),
      preferences: generateUserPreferences(),
      achievements: generateUserAchievements(),
      isOnline: Math.random() > 0.7,
      lastActivity: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    });
  }
  
  return users;
};

/**
 * Génère des statistiques d'utilisateur
 */
const generateUserStats = () => {
  const totalActivities = Math.floor(Math.random() * 500) + 50;
  const totalDistance = Math.floor(Math.random() * 2000) + 100;
  const totalTime = Math.floor(Math.random() * 10000) + 1000; // en minutes
  
  return {
    totalActivities,
    totalDistance,
    totalTime,
    averagePace: 5 + Math.random() * 3, // min/km
    longestRun: Math.floor(Math.random() * 30) + 5,
    weeklyGoal: Math.floor(Math.random() * 50) + 20,
    currentStreak: Math.floor(Math.random() * 30),
    personalBests: {
      '5K': (18 + Math.random() * 10) * 60, // en secondes
      '10K': (35 + Math.random() * 20) * 60,
      'HALF_MARATHON': (80 + Math.random() * 40) * 60,
      'MARATHON': (180 + Math.random() * 80) * 60
    }
  };
};

/**
 * Génère des préférences d'utilisateur
 */
const generateUserPreferences = () => {
  const activities = ['running', 'cycling', 'hiking', 'walking'];
  const goals = ['weight_loss', 'endurance', 'speed', 'general_fitness'];
  
  return {
    favoriteActivity: activities[Math.floor(Math.random() * activities.length)],
    primaryGoal: goals[Math.floor(Math.random() * goals.length)],
    preferredDistance: Math.floor(Math.random() * 20) + 5,
    preferredTime: ['morning', 'afternoon', 'evening'][Math.floor(Math.random() * 3)],
    privacy: ['public', 'friends', 'private'][Math.floor(Math.random() * 3)]
  };
};

/**
 * Génère des achievements d'utilisateur
 */
const generateUserAchievements = () => {
  const allAchievements = [
    { id: 'first_run', name: 'Premier pas', description: 'Première course enregistrée', icon: '🏃‍♂️' },
    { id: 'week_streak', name: 'Régularité', description: '7 jours consécutifs d\'activité', icon: '🔥' },
    { id: 'distance_100', name: 'Centurion', description: '100 km parcourus au total', icon: '💯' },
    { id: 'early_bird', name: 'Lève-tôt', description: '10 courses avant 7h du matin', icon: '🌅' },
    { id: 'explorer', name: 'Explorateur', description: '25 parcours différents complétés', icon: '🗺️' },
    { id: 'social_butterfly', name: 'Papillon social', description: '10 amis ajoutés', icon: '🦋' },
    { id: 'challenger', name: 'Challenger', description: '5 défis complétés', icon: '🏆' },
    { id: 'local_legend', name: 'Légende locale', description: 'Statut de légende sur un segment', icon: '👑' }
  ];
  
  const numAchievements = Math.floor(Math.random() * 6) + 2;
  const shuffled = allAchievements.sort(() => 0.5 - Math.random());
  
  return shuffled.slice(0, numAchievements).map(achievement => ({
    ...achievement,
    unlockedAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
  }));
};

/**
 * Génère des défis
 */
export const generateChallenges = (users) => {
  const challengeTypes = [
    {
      type: 'distance',
      name: 'Défi Distance',
      description: 'Parcourir une certaine distance',
      unit: 'km',
      targets: [50, 100, 200, 300]
    },
    {
      type: 'activities',
      name: 'Défi Activités',
      description: 'Nombre d\'activités à compléter',
      unit: 'activités',
      targets: [10, 20, 30, 50]
    },
    {
      type: 'time',
      name: 'Défi Temps',
      description: 'Temps total d\'activité',
      unit: 'heures',
      targets: [10, 25, 50, 100]
    },
    {
      type: 'streak',
      name: 'Défi Régularité',
      description: 'Jours consécutifs d\'activité',
      unit: 'jours',
      targets: [7, 14, 30, 60]
    }
  ];
  
  const challenges = [];
  
  // Défis actifs
  for (let i = 0; i < 8; i++) {
    const challengeType = challengeTypes[Math.floor(Math.random() * challengeTypes.length)];
    const target = challengeType.targets[Math.floor(Math.random() * challengeType.targets.length)];
    const creator = users[Math.floor(Math.random() * users.length)];
    const startDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const duration = [7, 14, 30, 60][Math.floor(Math.random() * 4)];
    const endDate = new Date(startDate.getTime() + duration * 24 * 60 * 60 * 1000);
    
    const participants = generateChallengeParticipants(users, target, challengeType.type);
    
    challenges.push({
      id: `challenge_${i + 1}`,
      name: `${challengeType.name} - ${target} ${challengeType.unit}`,
      description: `${challengeType.description} en ${duration} jours`,
      type: challengeType.type,
      target,
      unit: challengeType.unit,
      startDate,
      endDate,
      duration,
      createdBy: creator,
      participants,
      isActive: endDate > new Date(),
      prize: generateChallengePrize(),
      rules: generateChallengeRules(challengeType.type),
      category: ['individual', 'team'][Math.floor(Math.random() * 2)]
    });
  }
  
  return challenges.sort((a, b) => b.participants.length - a.participants.length);
};

/**
 * Génère des participants pour un défi
 */
const generateChallengeParticipants = (users, target, type) => {
  const numParticipants = Math.floor(Math.random() * 20) + 5;
  const selectedUsers = users.sort(() => 0.5 - Math.random()).slice(0, numParticipants);
  
  return selectedUsers.map(user => {
    const progress = Math.random() * target * 1.2; // Certains peuvent dépasser l'objectif
    
    return {
      user,
      progress: Math.round(progress * 100) / 100,
      rank: 0, // Sera calculé après tri
      joinedAt: new Date(Date.now() - Math.random() * 20 * 24 * 60 * 60 * 1000),
      isCompleted: progress >= target,
      lastActivity: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000)
    };
  }).sort((a, b) => b.progress - a.progress).map((participant, index) => ({
    ...participant,
    rank: index + 1
  }));
};

/**
 * Génère des prix pour les défis
 */
const generateChallengePrize = () => {
  const prizes = [
    'Badge exclusif "Champion"',
    'Titre spécial sur le profil',
    'Points bonus (500 pts)',
    'Accès anticipé aux nouvelles fonctionnalités',
    'Badge "Finisher" personnalisé',
    'Mention dans le hall of fame'
  ];
  
  return prizes[Math.floor(Math.random() * prizes.length)];
};

/**
 * Génère des règles pour les défis
 */
const generateChallengeRules = (type) => {
  const baseRules = [
    'Seules les activités enregistrées pendant la période du défi comptent',
    'Les activités doivent durer au minimum 10 minutes',
    'Pas de tricherie - fair-play obligatoire'
  ];
  
  const typeSpecificRules = {
    distance: ['Distance minimale par activité : 1 km'],
    activities: ['Toutes les activités sportives sont acceptées'],
    time: ['Temps minimal par activité : 15 minutes'],
    streak: ['Une activité par jour minimum', 'Les jours de repos cassent la série']
  };
  
  return [...baseRules, ...(typeSpecificRules[type] || [])];
};

/**
 * Génère des relations d'amitié
 */
export const generateFriendships = (users, currentUserId) => {
  const friendships = [];
  const currentUser = users.find(u => u.id === currentUserId);
  
  if (!currentUser) return friendships;
  
  // Générer des amis existants
  const numFriends = Math.floor(Math.random() * 15) + 5;
  const potentialFriends = users.filter(u => u.id !== currentUserId);
  const friends = potentialFriends.sort(() => 0.5 - Math.random()).slice(0, numFriends);
  
  friends.forEach(friend => {
    friendships.push({
      id: `friendship_${currentUserId}_${friend.id}`,
      user1: currentUser,
      user2: friend,
      status: 'accepted',
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      mutualFriends: Math.floor(Math.random() * 5),
      sharedActivities: Math.floor(Math.random() * 10)
    });
  });
  
  // Générer des demandes d'amitié en attente
  const numPendingRequests = Math.floor(Math.random() * 5) + 1;
  const remainingUsers = potentialFriends.filter(u => !friends.includes(u));
  const pendingRequests = remainingUsers.sort(() => 0.5 - Math.random()).slice(0, numPendingRequests);
  
  pendingRequests.forEach(requester => {
    friendships.push({
      id: `friendship_${requester.id}_${currentUserId}`,
      user1: requester,
      user2: currentUser,
      status: 'pending',
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      mutualFriends: Math.floor(Math.random() * 3)
    });
  });
  
  return friendships;
};

/**
 * Génère des suggestions d'amis
 */
export const generateFriendSuggestions = (users, currentUserId, existingFriends) => {
  const currentUser = users.find(u => u.id === currentUserId);
  if (!currentUser) return [];
  
  const friendIds = existingFriends.map(f => f.user2.id);
  const availableUsers = users.filter(u => 
    u.id !== currentUserId && 
    !friendIds.includes(u.id)
  );
  
  // Calculer un score de compatibilité basé sur les préférences et la localisation
  const suggestions = availableUsers.map(user => {
    let score = 0;
    
    // Même ville
    if (user.city === currentUser.city) score += 30;
    
    // Activité préférée similaire
    if (user.preferences.favoriteActivity === currentUser.preferences.favoriteActivity) score += 20;
    
    // Objectif similaire
    if (user.preferences.primaryGoal === currentUser.preferences.primaryGoal) score += 15;
    
    // Niveau similaire (basé sur la distance moyenne)
    const userLevel = user.stats.totalDistance / user.stats.totalActivities;
    const currentLevel = currentUser.stats.totalDistance / currentUser.stats.totalActivities;
    const levelDiff = Math.abs(userLevel - currentLevel);
    if (levelDiff < 2) score += 10;
    
    // Activité récente
    const daysSinceLastActivity = (Date.now() - user.lastActivity.getTime()) / (24 * 60 * 60 * 1000);
    if (daysSinceLastActivity < 7) score += 10;
    
    // Ajouter un peu de randomness
    score += Math.random() * 5;
    
    return {
      user,
      score,
      reason: generateSuggestionReason(user, currentUser)
    };
  });
  
  return suggestions
    .sort((a, b) => b.score - a.score)
    .slice(0, 10);
};

/**
 * Génère une raison pour la suggestion d'ami
 */
const generateSuggestionReason = (suggestedUser, currentUser) => {
  const reasons = [];
  
  if (suggestedUser.city === currentUser.city) {
    reasons.push(`Habite à ${suggestedUser.city}`);
  }
  
  if (suggestedUser.preferences.favoriteActivity === currentUser.preferences.favoriteActivity) {
    reasons.push(`Aime aussi la ${suggestedUser.preferences.favoriteActivity}`);
  }
  
  if (suggestedUser.preferences.primaryGoal === currentUser.preferences.primaryGoal) {
    reasons.push('Objectifs similaires');
  }
  
  if (reasons.length === 0) {
    reasons.push('Profil compatible');
  }
  
  return reasons[0];
};

/**
 * Formate le temps en format lisible
 */
export const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

/**
 * Formate la pace en min/km
 */
export const formatPace = (paceInSeconds) => {
  const minutes = Math.floor(paceInSeconds / 60);
  const seconds = Math.round(paceInSeconds % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')} /km`;
};
